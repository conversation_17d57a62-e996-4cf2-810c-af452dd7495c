# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage
/test-results
/playwright-report
/allure-results
/allure-report
result.xml
/e2e/storageState.json


# production
/build

# misc
.DS_Store
.env.local
.env.development.local
.env.test.local
.env.production.local

npm-debug.log*
yarn-debug.log*
yarn-error.log*
libs/hexfluidplatform-react-components-0.0.10.tgz

public/vision/mMapSDK/
playwright-report/
results.xml
test-results.json
reports/
libs/nexusplatform-core-0.3.1.tgz
libs/nexusplatform-react-0.3.1.tgz
libs/nexusplatform-react-components-0.0.26.tgz
libs/nexusplatform-react-components-0.3.1.tgz
libs/nexusplatform-sdc-schema-0.3.3.tgz
libs/nexusplatform-visualization-0.1.0-alpha.5.tgz
src/pages/TestView.tsx
.vscode/launch.json
html-reports/
snapshots/
e2e/storageState.json
libs/nexusplatform-core-0.2.81.tgz
libs/nexusplatform-core-0.2.101.tgz
libs/nexusplatform-core-0.2.102.tgz
libs/nexusplatform-core-0.2.103.tgz
libs/nexusplatform-core-0.2.104.tgz
libs/nexusplatform-react-components-0.0.2.tgz
libs/nexusplatform-react-components-0.0.3.tgz
libs/nexusplatform-react-components-0.0.4.tgz
libs/nexusplatform-react-components-0.0.5.tgz
libs/nexusplatform-react-components-0.0.1.tgz
libs/nexusplatform-react-components-0.0.91.tgz
libs/nexusplatform-react-components-0.0.99.tgz
libs/nexusplatform-core-0.2.83.tgz
libs/nexusplatform-core-0.2.82.tgz
/.vs
