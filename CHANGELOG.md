#### 0.0.1 (2022-09-14)

##### Other Changes

- Added bread crumb in user preference page (5867eaa2)
- Fix issue with mismatch avatar in add pin bubble (42d568ce)
- updated packages (cc013338)
- GEN-6795-Add a default model on the project (a7e2ee07)
- Admin name temporary fix for create organization (c8e8d533)
- GEN-6795 Add a default 3d model on the Project (de4ca5ff)
- User track Activity API integration (1b6c52a1)
- //dev.azure.com/hexagonmi/MI-Genesis/\_git/GenHexFluidReactApp into feature/GEN_5987 (c566aa6c)
- GEN-6677 Hide back Btn in teams and change title (a8cdc2d1)
- Added runtime search while typing (8f326399)
- Styling changes for GEN-6598 (08c8a904)
- //dev.azure.com/hexagonmi/MI-Genesis/\_git/GenHexFluidReactApp into feature/GEN-6598-Unseen-comment-styling-changes-on-comments-panel (c8ef421c)
- GEN-6722-New comment bubble is getting closed when some other user performs operation on comments (cdb9604e)
- unit testing code for HexSDCInfo component (c3e7b07a)
- bubble growing out of screen (487b2622)
- GEN-6185 Remove header from Teams in dev environment (1b2b6631)
- Updated artifact for the closeIcon (405aad5d)
- GEN-6185 Added base path for teams whiteboard (907345bd)
- Show the Project SDC information on the 3D white board (4eb52c59)
- GEN-6185 Whiteboard integration to MSTeams (725b641d)
- GEN-6382-When load the projects view page with params, page size causes inconvenience (1b2baa78)
- GEN-6546 Change Metrology Reporting URL for Production Nexus App (902f483e)
- //dev.azure.com/hexagonmi/MI-Genesis/\_git/GenHexFluidReactApp into feature/GEN-5951-bubble-grows-out-of-screen (0b0756d9)
- //dev.azure.com/hexagonmi/MI-Genesis/\_git/GenHexFluidReactApp into feature/GEN-5951-bubble-grows-out-of-screen (5e64d2ca)
- SDC Viewer page test script is commented in the repo (1997ddb2)
- //dev.azure.com/hexagonmi/MI-Genesis/\_git/GenHexFluidReactApp into feature/GEN-6185_ProjectList_user_Select_3D_WhiteboardApp (42a35b7a)
- code updated to listen left Mouse Button Click (3a8c00dd)
- //dev.azure.com/hexagonmi/MI-Genesis/\_git/GenHexFluidReactApp into feature/SDCViewer_e2e (e7dba448)
- //dev.azure.com/hexagonmi/MI-Genesis/\_git/GenHexFluidReactApp into feature/SDCViewer_e2e (4d0e2407)
- //dev.azure.com/hexagonmi/MI-Genesis/\_git/GenHexFluidReactApp into GEN-6091-ProjectSDCView (cf8e6a60)
- //dev.azure.com/hexagonmi/MI-Genesis/\_git/GenHexFluidReactApp into GEN-6091-ProjectSDCView (99932356)
- UIUX- Name sync issue on 3d Whiteboard (0b09de09)
- //dev.azure.com/hexagonmi/MI-Genesis/\_git/GenHexFluidReactApp into GEN-6091-ProjectSDCView (bbb60778)
- //dev.azure.com/hexagonmi/MI-Genesis/\_git/GenHexFluidReactApp into feature/GEN-4084-NameSyncIssuefix (14149e0d)
- //dev.azure.com/hexagonmi/MI-Genesis/\_git/GenHexFluidReactApp into feature/GEN-4084-NameSyncIssuefix (b747e3a8)
- commented failing test cases (9f5738dc)
- GEN-6404-Container height is broken with the query param on large screen (d8a3a33a)
- react app code modified with comment bubble and version updated in package.js... (fde94f74)
- GEN-6512 Modified sdc page visualization (8e5d97f1)
- //dev.azure.com/hexagonmi/MI-Genesis/\_git/GenHexFluidReactApp into GEN-6091-ProjectSDCView (581d51dc)
- //dev.azure.com/hexagonmi/MI-Genesis/\_git/GenHexFluidReactApp into feature/GEN-4084-NameSyncIssuefix (94dc778f)
- //dev.azure.com/hexagonmi/MI-Genesis/\_git/GenHexFluidReactApp into feature/GEN-4084-NameSyncIssuefix (b600cbf2)
- Updated as per review comment. (016b4eb6)
- Code update as per review comments (6f57bfa4)
- E2E for user flow (e29a5082)
- GEN-6455 Update the Visualization component for loading whiteboard on dev (fd10e7fc)
- GEN-3744 Update the ReactLib package for cancel btn fix on whiteboard (9deda9d5)
- User Preference UI to Edit Contact, Region and Language (76f2a04f)
- //dev.azure.com/hexagonmi/MI-Genesis/\_git/GenHexFluidReactApp into GEN-6091-ProjectSDCView (9b3b6048)
- Remove getting firstname and lastname from logged in users information (Auth) (aafb0cd3)
- //dev.azure.com/hexagonmi/MI-Genesis/\_git/GenHexFluidReactApp into feature/GEN-6085_latest (d99f0d57)
- GEN-6295-Show white board only on dev (d95e6927)
- GEN-6295-Show whiteboard only on dev (7e468ce6)
- GEN-6057-Implement viewing of specific projects (a982b1de)
- //dev.azure.com/hexagonmi/MI-Genesis/\_git/GenHexFluidReactApp into feature/GEN_5991 (51a792c8)
- Search for users by name (e1d374e0)
- GEN-6293 Change copy link to use query params (ceb39dff)
- GEN-6295- Show white board only on Dev (d4922fbd)
- Show white board only on dev (5d734463)
- GEN3734-Added inactive and active cursors for comments (c97005c8)
- Organizations E2E tests. (d912b750)
- //dev.azure.com/hexagonmi/MI-Genesis/\_git/GenHexFluidReactApp into feature/GEN_3972_E2E_Organizations (bb792c19)
- Integrated roles options, updated Pagination and added E2E test cases (32dc9b82)
- Fix nexus icon not showing on whiteboard page in dev (45c7166b)
- [GENP-3832] Initialize sdc on project load [AK] (68def8ea)
- GEN-4931 SDC Viewer page -> Hand icon issue on hovering on Nexus Application (ecf9757c)
- Reverted copy files changes of vision component (6562c315)
- Revert "Pipe line updated to pick proper integartion tests" (1c650097)
- Fix vision files copy path on dev to make vision component working. (4f2e1d78)
- [GEN-5718] disabled blackduck for the react app build pipeline [AK] (56b5edcd)
- Added base url which got overridden in one of the merge (4b708b59)
- GEN-5718 Merge scene controller to main (2bd16200)
- PR for Fixed some errors in the test scripts Pipeline (1d7fcb3a)
- [GENP-4005] Trigger logout window event on platform on logout [AK ] (431ebdb1)
- Pipe line updated to pick proper integartion tests (5344796b)
- [GEN-6014] Update package , SDC Viewer Config [AK] (de5df5eb)
- GEN-5565-Added translations for plastic (48f464f6)
- Image Upload Uploaded image is not reflecting in the projects page (8a9f714a)
- [GEN-4014] Fix project loading after creation [AK ] [FOR APEX] (3c928ac2)
- GEN-5565-Update to the latest version of react-components (b2893fa4)
- [GEN-5804] FIX BAD MERGE Add error page component [AK] (6b91be8a)
- GEN-5565- Add plastic type to project type filter (6a78b81b)
- GEN-5565-Installed latest version of react-components and removed local hexcreateproject (0527913b)
- [GEN-5811] Support material classification [AK] (112958a0)
- [GEN-5804] Add error page component [AK] (38d94469)
- [GEN-5824 ] Navigate to project after creation [AK] (364f14f6)
- [ GEN-5819 ] Update lib package and hide last changed app tag for MVP [AK ] (8083501a)
- GEN-5186-Update E2e tests on SDC Viewer (5eb04910)
- GEN-4803-E2e test for load more duplicate bug (6f61941c)
- [GEN-5417 ] Update config for MVP and latest react lib package (6ffe5b47)
- [ GEN-4907 ] Update packages [AK] (7ca153ab)
- GEN-5186-E2E tests for the SDC Viewer Page (e5544486)
- GEN-5009 E2e test for Clearing search after navigating back from a project fails (b9cccc0a)
- [ GEN-5285 ]Update react app to show container failure error message [AK ] (b7daa5a5)
- disable html reporter by config (1a146e2d)
- GENP-3688-Installed latest verison of nexusplatform/react-component (e71e42e9)
- publish html reporter for all environments (a61d7f15)
- Token for getting thumbnail (175e4f05)
- Redirect to Metro Reporting when project is of type metro reporting (48ebb09a)
- add Mithun and Rejeshwar in the email list for test failure (81d85914)
- [GEN-4845] Update React App with custom config for MVP [AK] (aa463858)
- PR for Platform application - Updated the share project page Script (7b5c08ae)
- PR for Platform application - Updated the share project page locators (b1876edb)
- PR for React Application (a1b55669)
- GEN-5300-Update react app with latest version of react/components package for share dialog (82fa1749)
- GEN-4858-E2e test for scroll issue on mobile view (f1500036)
- update junit test report file name environment setting command (5dd64b30)
- //dev.azure.com/hexagonmi/MI-Genesis/\_git/GenHexFluidReactApp (9aac1bdc)
- add publish test result to pipeline for different environments. (93392816)
- //dev.nexus.hexagon.com/platform-landing, updated login scripts and updated Search Testcases (2d9781d1)
