{
  "env": {
    "browser": true,
    "es2021": true,
    "node": true
  },
  "extends": ["eslint:recommended", "plugin:react/recommended", "plugin:@typescript-eslint/recommended"],
  "parser": "@typescript-eslint/parser",
  "parserOptions": {
    "ecmaFeatures": {
      "jsx": true
    },
    "ecmaVersion": 12,
    "sourceType": "module"
  },
  "plugins": ["react", "@emotion", "@typescript-eslint", "react-hooks", "prettier", "eslint-plugin-tsdoc"],
  "overrides": [
    {
      "files": ["**/*.{js,ts,tsx}"]
    }
  ],
  "settings": {
    "react": {
      "version": "detect"
    }
  },
  "rules": {
    "@emotion/jsx-import": "error",
    "prettier/prettier": [
      "warn",
      {
        "endOfLine": "auto"
      }
    ],
    "react-hooks/rules-of-hooks": "error",
    "react-hooks/exhaustive-deps": "warn",
    "max-len": "off",
    "valid-jsdoc": "off",
    "require-jsdoc": "off",
    "linebreak-style": "off",
    "no-unused-vars": "warn",
    "tsdoc/syntax": "warn"
  },
  "globals": {
    // Define globals used by Vite
    "defineProps": "readonly",
    "defineEmits": "readonly",
    "defineExpose": "readonly",
    "withDefaults": "readonly",
    // Add process.env for backward compatibility
    "process": "readonly"
  }
}
