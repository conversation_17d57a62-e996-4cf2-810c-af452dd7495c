const fs = require('fs');
const htmlparser2 = require('htmlparser2');
const render = require('dom-serializer').default;

const confluence = require('./confluence.js');

confluence
  .getPageContent(import.meta.env.CONFLUENCE_DEPLOYMENT_PAGEID)
  .then((response) => {
    const body = response['body'];
    if (!body) {
      throw new Error(`Body not found`);
    }

    const storage = body['storage'];
    if (!storage) {
      throw new Error(`Storage not found`);
    }

    const value = storage['value'];
    if (!value) {
      throw new Error(`Value not found`);
    }

    let dom = htmlparser2.parseDocument(value);

    let table = htmlparser2.DomUtils.getElementsByTagName('tbody', dom)[0];

    if (!table) {
      console.log('Table not found - seeding');

      const deployment = fs.readFileSync('./deploylog.html', 'utf8');

      dom = htmlparser2.parseDocument(deployment);

      table = htmlparser2.DomUtils.getElementsByTagName('tbody', dom)[0];
    }

    let rowIndex = 0;
    switch (import.meta.env.DEPLOYMENT_TARGET) {
      case 'dev':
        rowIndex = 1;
        break;
      case 'qa':
        rowIndex = 2;
        break;
      case 'sit':
        rowIndex = 3;
        break;
      case 'prd':
        rowIndex = 4;
        break;
    }

    const row = table.children?.filter((row) => row.name === 'tr')[rowIndex];

    // version
    row.children[1].children[0].data = import.meta.env.DEPLOYMENT_VERSION;
    // react components version
    row.children[2].children[0].data = reactComponentsPackageVersion;
    // visualization version
    row.children[3].children[0].data = visualizationPackageVersion;
    // date
    row.children[4].children[0].data = new Date().toISOString();
    // pipeline
    row.children[5].children[0].attribs.href = import.meta.env.DEPLOYMENT_PIPELINE;

    console.log(
      'Data:',
      import.meta.env.DEPLOYMENT_VERSION,
      reactComponentsPackageVersion,
      visualizationPackageVersion,
      new Date().toISOString(),
      import.meta.env.DEPLOYMENT_PIPELINE
    );

    confluence
      .updatePage(import.meta.env.CONFLUENCE_DEPLOYMENT_PAGEID, render(dom, { xmlMode: true }))
      .then((response) => {
        console.log(`updated: ${JSON.stringify(response)}`);
      })
      .catch((error) => {
        console.log('error', error);
      });
  })
  .catch((error) => {
    console.log('error', error);
  });
