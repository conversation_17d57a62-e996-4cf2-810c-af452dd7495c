const fetch = require('node-fetch');

let domain = import.meta.env.CONFLUENCE_DOMAIN;
let basic = import.meta.env.CONFLUENCE_USERNAME + ':' + import.meta.env.CONFLUENCE_PAT;
let token = Buffer.from(basic).toString('base64');

let headers = {
  'Content-Type': 'application/json',
  Authorization: 'Basic ' + token,
  'User-Agent': 'Azure DevOps'
};

async function getPage(pageId) {
  const response = await fetch(`https://${domain}/wiki/rest/api/content/${pageId}`, {
    headers: headers,
    redirect: 'follow'
  });

  if (response.status !== 200) {
    console.error(`Response: ${await response.text()}`);
    throw new Error(`Page ${pageId} get failed`);
  }

  return await response.json();
}

async function getPageContent(pageId) {
  const response = await fetch(`https://${domain}/wiki/rest/api/content/${pageId}?expand=body.storage`, {
    headers: headers,
    redirect: 'follow'
  });

  if (response.status !== 200) {
    console.error(`Response: ${await response.text()}`);
    throw new Error(`Page ${pageId} get failed`);
  }

  return await response.json();
}

async function updatePage(pageId, data) {
  let pageData = await getPage(pageId);

  if (pageData === undefined) {
    throw new Error(`Page ${pageId} not found`);
  }

  if (pageData.version?.number === undefined) {
    throw new Error(`Page ${pageId} version not found`);
  }

  console.log(`Updating page ${pageId} version ${pageData.version.number}`);

  let raw = JSON.stringify({
    id: pageId,
    type: 'page',
    space: {
      key: pageData.space?.key
    },
    version: {
      number: pageData.version?.number + 1
    },
    title: pageData.title,
    body: {
      storage: {
        value: data,
        representation: 'storage'
      }
    }
  });

  console.log(`Sending: ${raw}`);

  const response = await fetch(`https://${domain}/wiki/rest/api/content/${pageId}`, {
    method: 'PUT',
    headers: headers,
    redirect: 'follow',
    body: raw
  });

  if (response.status !== 200) {
    console.error(`Response: ${await response.text()}`);
    throw new Error(`Page ${pageId} update failed`);
  }

  return await response.json();
}

module.exports = { getPage, getPageContent, updatePage };
