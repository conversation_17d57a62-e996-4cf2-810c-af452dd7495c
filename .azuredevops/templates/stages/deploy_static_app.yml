parameters:
  - name: dependsOn
    type: object
    default: []
  - name: vmImage
    type: string
    default: ubuntu-latest
  - name: env
    type: string
  - name: FDID
    type: string
    default: ''
  - name: URL_TOKEN
    type: string
    default: ''
  - name: APP_INSIGHTS_KEY
    type: string
    default: ''
  - name: AUTH_AUTHORITY
    type: string
    default: ''
  - name: AUTH_ISSUER
    type: string
    default: ''
  - name: AUTH_DOMAIN
    type: string
    default: ''
  - name: AUTH_AUDIENCE
    type: string
    default: ''
  - name: AUTH_CLIENT
    type: string
    default: ''
  - name: HREF_URL
    type: string
    default: ''
  - name: MAT_URL
    type: string
    default: ''
  - name: METRO_URL
    type: string
    default: ''
  - name: PORTAL_URL
    type: string
    default: ''
  - name: LD_CLIENT_SIDE_ID
    type: string
    default: ''
  - name: INTENTO_KEY
    type: string
    default: ''
  - name: SHOW_PART_TREE
    type: boolean
    default: false
  - name: PLATFORM_APP_VERSION
    type: string
    default: ''
  - name: location
    type: string
    default: 'uks'
  - name: region
    type: string
    default: 'euw'
  - name: DEFAULT_ORGANIZATION_ID
    type: string
    default: ''
  - name: ResourceName
    type: string
    default: 'hexfluidsample'

# Try moving this to a stage template and move the condition up to the stage
stages:
  - stage: Deploy_${{ parameters.env }}_${{ parameters.location }}
    displayName: Deploy ${{ parameters.env }}_${{ parameters.location }}
    dependsOn: ${{ parameters.dependsOn }}
    condition: succeeded()
    variables:
      APP_INSIGHTS_KEY: ${{parameters.APP_INSIGHTS_KEY}}
      URL_TOKEN: ${{parameters.URL_TOKEN}}
      FDID: ${{parameters.FDID}}
      env_url: ${{ parameters.env }}
      AUTH_AUTHORITY: ${{ parameters.AUTH_AUTHORITY }}
      AUTH_ISSUER: ${{ parameters.AUTH_ISSUER }}
      AUTH_DOMAIN: ${{ parameters.AUTH_DOMAIN }}
      AUTH_AUDIENCE: ${{ parameters.AUTH_AUDIENCE }}
      AUTH_CLIENT: ${{ parameters.AUTH_CLIENT }}
      HREF_URL: ${{parameters.HREF_URL}}
      MAT_URL: ${{parameters.MAT_URL}}
      METRO_URL: ${{parameters.METRO_URL}}
      PORTAL_URL: ${{parameters.PORTAL_URL}}
      LD_CLIENT_SIDE_ID: ${{parameters.LD_CLIENT_SIDE_ID}}
      INTENTO_KEY: ${{parameters.INTENTO_KEY}}
      SHOW_PART_TREE: ${{parameters.SHOW_PART_TREE}}
      DEFAULT_ORGANIZATION_ID: ${{parameters.DEFAULT_ORGANIZATION_ID}}
      DATE_STAMP: $(Get-Date -Format yyyyMMddhhmmss)
    jobs:
      - deployment: Deploy_${{ parameters.env }}
        displayName: Deploy ${{ parameters.env }}_${{ parameters.location }}
        environment: 'nex-${{ parameters.env }}-${{ parameters.ResourceName }}-${{ parameters.location }}'
        pool:
          vmImage: ${{ parameters.vmImage }}
        strategy:
          runOnce:
            deploy:
              steps:
                - script: |
                    if [ "${{ parameters.ResourceName }}" = "hexfluidsample" ]; then
                      if [ "$(Build.SourceBranchName)" != "release/auth0" ]; then
                        echo "Error: ResourceName 'hexfluidsample' must use branch 'release/auth0'."
                        exit 1
                      fi
                    fi
                  displayName: Validate branch for ResourceName

                - download: current
                  artifact: drop

                - script: |
                    ls
                  displayName: Display working directory
                - script: |
                    echo "##vso[task.setvariable variable=PLATFORM_APP_VERSION;issecret=false]$(Build.BuildNumber)"
                  displayName: set App version to build number
                # Extract files
                # Extract a variety of archive and compression files such as .7z, .rar, .tar.gz, and .zip
                - task: ExtractFiles@1
                  inputs:
                    archiveFilePatterns: $(Pipeline.Workspace)/drop/*.zip
                    destinationFolder: build
                    #cleanDestinationFolder: true
                    #overwriteExistingFiles: false
                    #pathToSevenZipTool:

                # - script: |
                #     ls
                #     cd static
                #     ls
                #     cd js
                #     ls
                #   displayName: Display working directory

                - task: replacetokens@5
                  inputs:
                    rootDirectory: 'build'
                    targetFiles: 'staticwebapp.config.json'
                    tokenPattern: 'doublebraces'
                    writeBOM: true
                    actionOnMissing: 'warn'
                    keepToken: true
                    actionOnNoFiles: 'continue'
                    enableTransforms: false
                    enableRecursion: false
                    useLegacyPattern: false
                    enableTelemetry: true
                    useDefaultValue: false
                  displayName: 'WEB_CONFIG Replacing tokens for static web app_location'

                - task: replacetokens@5
                  inputs:
                    rootDirectory: 'build'
                    targetFiles: '**/*.js'
                    tokenPattern: 'doublebraces'
                    writeBOM: true
                    actionOnMissing: 'warn'
                    keepToken: true
                    actionOnNoFiles: 'continue'
                    enableTransforms: false
                    enableRecursion: false
                    useLegacyPattern: false
                    enableTelemetry: true
                    useDefaultValue: false
                  displayName: 'JS Replacing tokens for API_KEY and URLS'

                - task: replacetokens@5
                  inputs:
                    rootDirectory: 'build'
                    targetFiles: '**/*.css'
                    tokenPattern: 'doublebraces'
                    writeBOM: true
                    actionOnMissing: 'warn'
                    keepToken: true
                    actionOnNoFiles: 'continue'
                    enableTransforms: false
                    enableRecursion: false
                    useLegacyPattern: false
                    enableTelemetry: true
                    useDefaultValue: false
                  displayName: 'CSS'

                - task: replacetokens@5
                  inputs:
                    rootDirectory: 'build'
                    targetFiles: '*.html'
                    tokenPattern: 'doublebraces'
                    writeBOM: true
                    actionOnMissing: 'warn'
                    keepToken: true
                    actionOnNoFiles: 'continue'
                    enableTransforms: false
                    enableRecursion: false
                    useLegacyPattern: false
                    enableTelemetry: true
                    useDefaultValue: false
                  displayName: 'Replacing index to point correctly'

                - task: ArchiveFiles@2
                  displayName: Archive
                  inputs:
                    rootFolderOrFile: '$(System.DefaultWorkingDirectory)/build'
                    includeRootFolder: false
                    archiveType: zip
                    archiveFile: $(Build.ArtifactStagingDirectory)/$(Build.BuildId).zip
                    replaceExistingArchive: true

                - task: AzureCLI@2
                  displayName: 'Fetch Static Site API Key for ${{ parameters.env }}'
                  inputs:
                    azureSubscription: 'Hexagon MI Nexus - ${{ parameters.env }}'
                    scriptType: bash
                    scriptLocation: inlineScript
                    inlineScript: |
                      APIKEY=$(az staticwebapp secrets list --name nex-${{ parameters.env }}-${{ parameters.ResourceName }}-${{ parameters.region }}-stapp | jq -r '.properties.apiKey')
                      echo "##vso[task.setvariable variable=sampleAppKey;issecret=true]$APIKEY"

                - task: AzureStaticWebApp@0
                  displayName: Deploy reactapp to ${{ parameters.env }}
                  inputs:
                    app_location: '/build'
                    azure_static_web_apps_api_token: $(sampleAppKey)

                - powershell: |
                    Write-Host $(DATE_STAMP)

                - script: echo '$(DATE_STAMP)' $(DATE_STAMP)

                - publish: $(Build.ArtifactStagingDirectory)/$(Build.BuildId).zip
                  displayName: Publish
                  artifact: ${{parameters.env}}-deployed
