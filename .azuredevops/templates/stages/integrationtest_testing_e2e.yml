parameters:
  - name: dependsOn
    type: object
    default: []
  - name: environment
    type: string
    default: ''
  - name: applicationName
    type: string
    default: ''
  - name: emailTo
    type: string
    default: ''
  - name: authType
    type: string
    default: 'auth0'

stages:
  - stage: IntegrationTest_${{ parameters.environment }}
    variables:
      - group: blue_jays_team
    dependsOn: ${{ parameters.dependsOn }}
    pool:
      vmImage: 'Windows-latest'
    displayName: 'Integration Tests on ${{ parameters.environment }}'
    jobs:
      - job: IntegrationTest
        steps:
          - checkout: AutomationTests
          - task: NodeTool@0
            displayName: 'Install Node.js'
            inputs:
              versionSpec: '20.x'
          - script: |
              ls
            displayName: Working Path
          - task: npmAuthenticate@0
            inputs:
              workingFile: '.npmrc'
          - script: |
              npm ci --if-present
            displayName: Install node modules

          - script: 'npx playwright install'
            displayName: 'npx playwright install'

          - bash: |
              export PLAYWRIGHT_ENV=${{parameters.environment}}
              export APPLICATION_NAME=${{parameters.applicationName}}
              npx playwright test ProjectView/Adaptors DocumentView/MultipleWorkers ProjectView/TwoWorkers --max-failures=20 --workers=2
            displayName: 'Run Platform E2E test cases'
            env:
              idP_Name: ${{ parameters.authType }}
              blueJaysUser5WithUserRole_EMAIL: $(blueJaysUser5WithUserRole_EMAIL)
              BLUEJAYSUSER5WITHUSERROLE_PWD: $(blueJaysUser5WithUserRole_EMAIL)
              blueJaysUser3WithUserRole_EMAIL: $(blueJaysUser3WithUserRole_EMAIL)
              BLUEJAYSUSER3WITHUSERROLE_PWD: $(blueJaysUser3WithUserRole_PWD)
              blueJaysUser2WithHexAdminRole_EMAIL: $(blueJaysUser2WithHexAdminRole_EMAIL)
              BLUEJAYSUSER2WITHHEXADMINROLE_PWD: $(blueJaysUser2WithHexAdminRole_PWD)
              blueJaysUser4WithHexAdminRole_EMAIL: $(blueJaysUser4WithHexAdminRole_EMAIL)
              BLUEJAYSUSER4WITHHEXADMINROLE_PWD: $(blueJaysUser4WithHexAdminRole_PWD)

          - task: PublishTestResults@2
            displayName: Publish Playwright Test Results on ${{ parameters.environment }}
            condition: succeededOrFailed()
            inputs:
              testRunner: JUnit
              testResultsFiles: '$(System.DefaultWorkingDirectory)/reports/playwright-${{ parameters.environment }}.xml'

          - task: PublishBuildArtifacts@1
            displayName: 'Publish Artifact: playwright report'
            inputs:
              PathtoPublish: '$(System.DefaultWorkingDirectory)/html-reports'
              ArtifactName: 'playwright html report'
            condition: succeededOrFailed()

          - powershell: |
              python -m venv xvenv
              .\xvenv\Scripts\activate
              pip install xray-uploader
              pip install requests
              $env:test_execution_title="[${{parameters.environment}}] Platform UI $(Build.BuildNumber)"
              $env:result_file_path="$(System.DefaultWorkingDirectory)/reports/playwright-${{parameters.environment}}.xml"
              xray-uploader -r "$env:result_file_path" -f junit -pi 10391 -s "$env:test_execution_title" -ci $(XRAY_CLIENT_ID) -cs $(XRAY_CLIENT_SECRET) -tp GEN-55949
              deactivate
            displayName: Import test results to Jira/Xray
            condition: succeededOrFailed()

          - task: BatchScript@1
            inputs:
              filename: $(System.DefaultWorkingDirectory)\e2e\config\xray.bat
            condition: succeededOrFailed()

          - task: rvo.SendEmailTask.send-email-build-task.SendEmail@1
            displayName: 'Send email with DataExp test report on ${{parameters.environment}}'
            inputs:
              To: '${{ parameters.emailTo }}'
              From: '<EMAIL>'
              Subject: 'DataExp Automation Test report on ${{parameters.environment}}'
              Body: |
                Please check DataExp automation test result in the latest execution in below pipeline :
                https://dev.azure.com/hexagonmi/MI-Genesis/_build?definitionId=406
                Please open the Tests tab in the detailed build page for test result details.
                You can also download an html test report in published items link from the Summary tab.
              SmtpServer: smtp.office365.com
              SmtpUsername: '<EMAIL>'
              SmtpPassword: 'NexusAutomation@2022'
            condition: Failed()
