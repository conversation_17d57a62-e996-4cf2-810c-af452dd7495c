parameters:
  - name: dependsOn
    type: object
    default: []
  - name: environment
    type: string
    default: ''
  - name: emailTo
    type: string
    default: ''

stages:
  - stage: IntegrationTest_${{ parameters.environment }}
    dependsOn: ${{ parameters.dependsOn }}
    pool:
      vmImage: 'Windows-latest'
    displayName: 'Integration Tests on ${{ parameters.environment }}'
    jobs:
      - job: IntegrationTest
        steps:
          - task: NodeTool@0
            displayName: 'Install Node.js'
            inputs:
              versionSpec: '20.x'
          - script: |
              ls
            displayName: Working Path
          - task: npmAuthenticate@0
            inputs:
              workingFile: '.npmrc'
          - script: |
              npm ci --if-present
            displayName: Install node modules

          - script: 'npx playwright install'
            displayName: 'npx playwright install'

          - script: |
              SET stage=${{parameters.environment}}
              SET PLAYWRIGHT_JUNIT_OUTPUT_NAME=reports/playwright-${{ parameters.environment }}.xml
              npx playwright test --workers 1

            displayName: 'Run all test cases'

          - task: PublishTestResults@2
            displayName: Publish Playwright Test Results on ${{ parameters.environment }}
            condition: succeededOrFailed()
            inputs:
              testRunner: JUnit
              testResultsFiles: '$(System.DefaultWorkingDirectory)/reports/playwright-${{ parameters.environment }}.xml'

          - task: PublishBuildArtifacts@1
            displayName: 'Publish Artifact: playwright report'
            inputs:
              PathtoPublish: '$(System.DefaultWorkingDirectory)/html-reports'
              ArtifactName: 'playwright html report'
            condition: succeededOrFailed()

          # - task: BatchScript@1
          #   inputs:
          #     filename: $(System.DefaultWorkingDirectory)\e2e\config\xray.bat
          #   condition: succeededOrFailed()

          - task: rvo.SendEmailTask.send-email-build-task.SendEmail@1
            displayName: 'Send email with DataExp test report on ${{parameters.environment}}'
            inputs:
              To: '${{ parameters.emailTo }}'
              From: '<EMAIL>'
              Subject: 'DataExp Automation Test report on ${{parameters.environment}}'
              Body: |
                Please check DataExp automation test result in the latest execution in below pipeline :
                https://dev.azure.com/hexagonmi/MI-Genesis/_build?definitionId=406
                Please open the Tests tab in the detailed build page for test result details. 
                You can also download an html test report in published items link from the Summary tab.
              SmtpServer: smtp.office365.com
              SmtpUsername: '<EMAIL>'
              SmtpPassword: 'NexusAutomation@2022'
            condition: Failed()
