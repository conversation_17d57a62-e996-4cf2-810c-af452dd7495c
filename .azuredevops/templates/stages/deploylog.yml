parameters:
  - name: dependsOn
    type: object
    default: []
  - name: vmImage
    type: string
    default: 'ubuntu-latest'
  - name: nodeVersion
    type: string
    default: '20.x'
  - name: environment
    type: string
    default: ''

stages:
  - stage: Deploylog_${{ parameters.environment }}
    condition: and(succeeded(), eq(variables['Build.SourceBranchName'], 'main'))
    dependsOn: ${{ parameters.dependsOn }}
    displayName: Deploylog ${{ parameters.environment }}
    jobs:
      - job: Deploylog_${{ parameters.environment }}
        displayName: Deploylog ${{ parameters.environment }}
        pool:
          vmImage: ${{ parameters.vmImage }}
        steps:
          - checkout: self

          - task: NodeTool@0
            displayName: Use Node ${{ parameters.nodeVersion }}
            condition: succeeded()
            inputs:
              versionSpec: ${{ parameters.nodeVersion }}

          - task: npmAuthenticate@0
            inputs:
              workingFile: '.npmrc'

          - script: |
              npm install --legacy-peer-deps
            displayName: Install modules

          - script: |
              cd .azuredevops/scripts
              node ./deploylog.js
            displayName: 'Publish Deploylog'
            env:
              CONFLUENCE_DOMAIN: $(CONFLUENCE_DOMAIN)
              CONFLUENCE_USERNAME: $(CONFLUENCE_USERNAME)
              CONFLUENCE_PAT: $(CONFLUENCE_PAT)
              CONFLUENCE_DEPLOYMENT_PAGEID: $(CONFLUENCE_DEPLOYMENT_PAGEID)
              DEPLOYMENT_TARGET: ${{ parameters.environment }}
              DEPLOYMENT_VERSION: $(Build.BuildNumber)
              DEPLOYMENT_PIPELINE: '$(System.CollectionUri)$(System.TeamProject)/_build/results?buildId=$(Build.BuildId)'
