parameters:
  - name: dependsOn
    type: object
    default: []
  - name: configFilePath
    type: string
    default: ''

stages:
  - stage: Versioning
    jobs:
      - job: CalculateVersion
        steps:
          - checkout: self
            fetchDepth: 0

          - task: gitversion/setup@0
            displayName: Install GitVersion
            inputs:
              versionSpec: '5.x'

          - task: gitversion/execute@0
            displayName: Calculate SemVer
            name: Version
            inputs:
              useConfigFile: true
              updateAssemblyInfo: false
              configFilePath: ${{ parameters.configFilePath }}

          - script: |
              echo '##vso[build.updatebuildnumber]$(GitVersion.SemVer)'
            displayName: 'Set Build Number to Semantic Version'
