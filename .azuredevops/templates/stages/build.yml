parameters:
  - name: dependsOn
    type: object
    default: []
  - name: sonarqubeScan
    type: boolean
    default: false

stages:
  - stage: Build
    dependsOn: ${{parameters.dependsOn}}
    displayName: Build
    jobs:
      - job: Build
        variables:
          semVer: $[ stageDependencies.Versioning.CalculateVersion.outputs['Version.GitVersion.SemVer'] ]
        steps:
          - task: NodeTool@0
            displayName: 'Install Node.js'
            inputs:
              versionSpec: '20.x'

          - task: npmAuthenticate@0
            inputs:
              workingFile: '.npmrc'

          - script: |
              echo $(semVer)
            displayName: 'Set Build Number to Semantic Version'

          - script: |
              npm install --force
            displayName: Install node modules

          - script: |
              npm run lint --if-present
            displayName: Lint check

          - script: |
              npm run test:ci --if-present
            displayName: Test package

          - task: PublishTestResults@2
            displayName: Publish Unit Test Results
            condition: succeededOrFailed()
            inputs:
              testRunner: JUnit
              testResultsFiles: '$(System.DefaultWorkingDirectory)/reports/vitest-junit.xml'

          - task: PublishCodeCoverageResults@1
            displayName: Publish Unit Test Coverage
            condition: succeededOrFailed()
            inputs:
              codeCoverageTool: Cobertura # or JaCoCo
              summaryFileLocation: '$(System.DefaultWorkingDirectory)/reports/coverage/cobertura-coverage.xml'
              reportDirectory: '$(System.DefaultWorkingDirectory)/reports/coverage'

          - template: /.azuredevops/templates/steps/sonar_qube_analyze.yml
            parameters:
              sonarqubeScan: ${{parameters.sonarqubeScan}}
              sonarqubeProjectKey: NexusPlatformUI

          - task: Npm@1
            displayName: Update Version
            inputs:
              command: custom
              customCommand: version $(semVer) --no-git-tag-version

          - script: |
              npm run build --if-present
            displayName: Prepare binaries

          - script: |
              npm pack
            displayName: Package binaries

          - script: |
              ls
            displayName: Display working directory

          # - publish: "$(System.DefaultWorkingDirectory)/nexus-platform-react-app-$(semVer).tgz"
          #   displayName: Publish binaries
          #   artifact: pack

          - task: ArchiveFiles@2
            displayName: Archive
            inputs:
              rootFolderOrFile: '$(System.DefaultWorkingDirectory)/build'
              includeRootFolder: false
              archiveType: zip
              archiveFile: $(Build.ArtifactStagingDirectory)/$(Build.BuildId).zip
              replaceExistingArchive: true

          - script: |
              ls
            displayName: Display working directory
          # - template: /.azuredevops/templates/steps/black_duck_analyze.yml
          #   parameters:
          #     semVer: $(semVer)
          #     artifact: $(System.DefaultWorkingDirectory)/build

          - publish: $(Build.ArtifactStagingDirectory)/$(Build.BuildId).zip
            displayName: Publish
            artifact: drop

          - task: ArchiveFiles@2
            displayName: Archive
            condition: succeeded()
            inputs:
              rootFolderOrFile: '$(System.DefaultWorkingDirectory)'
              includeRootFolder: false
              archiveType: tar
              tarCompression: gz
              archiveFile: '$(Build.ArtifactStagingDirectory)/build-$(Build.BuildNumber).tar.gz'
              replaceExistingArchive: true

          - task: PublishBuildArtifacts@1
            displayName: Publish Build
            condition: succeeded()
            inputs:
              pathToPublish: '$(Build.ArtifactStagingDirectory)/build-$(Build.BuildNumber).tar.gz'
              artifactName: build
