parameters:
  - name: dependsOn
    type: object
    default: []
  - name: blackduckScan
    type: boolean

stages:
  - stage: BlackDuckScan
    dependsOn: ${{ parameters.dependsOn }}
    displayName: BlackDuckScan
    jobs:
      - job: BlackDuckScan
        steps:
          - checkout: none
          - task: DownloadPipelineArtifact@2
            displayName: Download Build Artifact
            inputs:
              buildType: 'current'
              artifactName: 'build'
              targetPath: '$(Build.SourcesDirectory)/build'

          - task: ExtractFiles@1
            displayName: Extract Build Artifact
            inputs:
              archiveFilePatterns: '$(Build.SourcesDirectory)/build/**/*.tar.gz'
              destinationFolder: '$(Build.SourcesDirectory)/extracted'

          - script: |
              ls -la $(Build.SourcesDirectory)/extracted
            displayName: List Source Directory Extracted Folder
          - task: BlackDuckDetectTask@10
            displayName: Black Duck Scan
            condition: and(succeededOrFailed(), eq(${{ parameters.blackduckScan }}, true))
            inputs:
              BlackDuckScaService: 'BlackDuckV2'
              DetectArguments: |
                --detect.project.name=MI-Genesis
                --detect.project.version.name=nexus-platform-ui
                --detect.timeout=1200
                --detect.tools=ALL
                --detect.source.path=$(Build.SourcesDirectory)/extracted
                --detect.detector.search.continue=true
                --detect.detector.search.depth=10
                --detect.wait.for.results=true
                --detect.risk.report.pdf=true
                --detect.risk.report.pdf.path=$(Build.SourcesDirectory)/reports/blackduck
                --detect.cleanup=false
                --detect.policy.check.fail.on.severities=ALL
                --detect.npm.include.dev.dependencies=false
                --detect.accuracy.required=NONE
                --detect.excluded.directories=".git,.azuredevops,.scannerwork,.sonarlint,.vscode,node_modules"
              DetectVersion: 'latest'

          - task: PublishBuildArtifacts@1
            displayName: BD Scan Results - Build
            inputs:
              artifactName: bd-scan-results
