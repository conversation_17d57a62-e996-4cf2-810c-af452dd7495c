parameters:
  - name: sonarqubeScan
    type: boolean
    default: true
  - name: sonarqubeProjectKey
    type: string
    default: ''
  - name: sonarqubeQualityGateTimeout
    type: number
    default: 1800

steps:
  - task: JavaToolInstaller@0
    inputs:
      versionSpec: '17' # 17 is a required version for SonarQube
      jdkArchitectureOption: 'x64'
      jdkSourceOption: 'PreInstalled'

  - task: SonarQubePrepare@7
    displayName: SonarQube Prepare
    condition: and(succeeded(), eq(${{ parameters.sonarqubeScan }}, true))
    inputs:
      SonarQube: 'Sonar-Nexus'
      scannerMode: 'CLI'
      configMode: 'manual'
      cliProjectKey: ${{ parameters.sonarqubeProjectKey }}
      cliProjectVersion: $(Build.BuildNumber)
      cliSources: '.'
      extraProperties: |
        sonar.log.level=INFO
        sonar.qualitygate.wait=true
        sonar.qualitygate.timeout=${{ parameters.sonarqubeQualityGateTimeout }}
        sonar.ws.timeout=900
        sonar.javascript.lcov.reportPaths=./reports/coverage/lcov.info

  - script: |
      FILTERED_PARAMS=$(echo $SONARQUBE_SCANNER_PARAMS | sed 's/"sonar.branch.name":"[^"]*"\,//g')
      echo "##vso[task.setvariable variable=SONARQUBE_SCANNER_PARAMS]$FILTERED_PARAMS"
    condition: and(succeeded(), eq(${{ parameters.sonarqubeScan }}, true))
    displayName: Filter out non-CE sonarqube parameters

  - task: SonarQubeAnalyze@7
    displayName: SonarQube Analyze
    condition: and(succeeded(), eq(${{ parameters.sonarqubeScan }}, true))

  - task: SonarQubePublish@7
    displayName: SonarQube Report
    condition: and(succeeded(), eq(${{ parameters.sonarqubeScan }}, true))
    inputs:
      pollingTimeoutSec: '300'
