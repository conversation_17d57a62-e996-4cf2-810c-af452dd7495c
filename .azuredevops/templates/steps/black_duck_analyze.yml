parameters:
  - name: semVer
    type: string
  - name: artifact
    type: string

steps:
  - task: SynopsysDetectTask@7
    displayName: Black Duck Scan
    condition: succeeded()
    inputs:
      BlackDuckService: "Blackduck"
      DetectArguments: |
        --detect.project.name=MI-Genesis
        --detect.project.version.name=data_exp_platform-${{parameters.semVer}}
        --detect.timeout=1200
        --detect.tools=ALL
        --detect.source.path=${{ parameters.artifact }}
        --detect.detector.search.continue=true
        --detect.detector.search.depth=10
        --detect.wait.for.results=true
        --detect.risk.report.pdf=true
        --detect.risk.report.pdf.path='$(Build.SourcesDirectory)/reports/blackduck'
        --detect.cleanup=false
        --detect.policy.check.fail.on.severities=ALL
      DetectVersion: "latest"
