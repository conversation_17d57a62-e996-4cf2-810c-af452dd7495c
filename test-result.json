{"config": {"forbidOnly": false, "fullyParallel": false, "globalSetup": null, "globalTeardown": null, "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {}, "preserveOutput": "always", "projects": [{"outputDir": "C:/CODE_SANDBOX/GEN_REACT_APP/test-results", "repeatEach": 1, "retries": 0, "name": "Chrome", "testDir": "C:/CODE_SANDBOX/GEN_REACT_APP/e2e/testcases", "testIgnore": [], "testMatch": ["**/?(*.)@(spec|test).*"], "timeout": 90000}], "reporter": [["dot", null], ["json", {"outputFile": "test-result.json"}], ["C:\\CODE_SANDBOX\\GEN_REACT_APP\\node_modules\\allure-playwright\\dist\\index.js", {"outputFolder": "my-allure-results"}]], "reportSlowTests": {"max": 5, "threshold": 15000}, "rootDir": "C:/CODE_SANDBOX/GEN_REACT_APP/e2e/testcases", "quiet": false, "shard": null, "updateSnapshots": "missing", "version": "1.22.2", "workers": 8, "webServer": null, "_globalOutputDir": "C:\\CODE_SANDBOX\\GEN_REACT_APP", "_configDir": "C:\\CODE_SANDBOX\\GEN_REACT_APP", "_testGroupsCount": 0}, "suites": [], "errors": [{"message": "ENOENT: no such file or directory, open './e2e/DataFile.xlsx'", "stack": "Error: ENOENT: no such file or directory, open './e2e/DataFile.xlsx'\n    at read_binary (C:\\CODE_SANDBOX\\GEN_REACT_APP\\node_modules\\xlsx\\xlsx.js:3153:44)\n    at readSync (C:\\CODE_SANDBOX\\GEN_REACT_APP\\node_modules\\xlsx\\xlsx.js:23698:69)\n    at Object.readFileSync (C:\\CODE_SANDBOX\\GEN_REACT_APP\\node_modules\\xlsx\\xlsx.js:23738:9)\n    at Object.excel (C:\\CODE_SANDBOX\\GEN_REACT_APP\\e2e\\utils\\Common\\handleexcel.js:8:21)\n    at Object.<anonymous> (C:\\CODE_SANDBOX\\GEN_REACT_APP\\e2e\\pages\\Homepage.js:10:22)\n    at Object.<anonymous> (C:\\CODE_SANDBOX\\GEN_REACT_APP\\e2e\\testcases\\Homepage.test.js:3:22)"}, {"message": "ENOENT: no such file or directory, open './e2e/DataFile.xlsx'", "stack": "Error: ENOENT: no such file or directory, open './e2e/DataFile.xlsx'\n    at read_binary (C:\\CODE_SANDBOX\\GEN_REACT_APP\\node_modules\\xlsx\\xlsx.js:3153:44)\n    at readSync (C:\\CODE_SANDBOX\\GEN_REACT_APP\\node_modules\\xlsx\\xlsx.js:23698:69)\n    at Object.readFileSync (C:\\CODE_SANDBOX\\GEN_REACT_APP\\node_modules\\xlsx\\xlsx.js:23738:9)\n    at Object.excel (C:\\CODE_SANDBOX\\GEN_REACT_APP\\e2e\\utils\\Common\\handleexcel.js:8:21)\n    at Object.<anonymous> (C:\\CODE_SANDBOX\\GEN_REACT_APP\\e2e\\pages\\Login.js:10:21)\n    at Object.<anonymous> (C:\\CODE_SANDBOX\\GEN_REACT_APP\\e2e\\testcases\\Login.test.js:3:24)"}, {"message": "ENOENT: no such file or directory, open './e2e/DataFile.xlsx'", "stack": "Error: ENOENT: no such file or directory, open './e2e/DataFile.xlsx'\n    at read_binary (C:\\CODE_SANDBOX\\GEN_REACT_APP\\node_modules\\xlsx\\xlsx.js:3153:44)\n    at readSync (C:\\CODE_SANDBOX\\GEN_REACT_APP\\node_modules\\xlsx\\xlsx.js:23698:69)\n    at Object.readFileSync (C:\\CODE_SANDBOX\\GEN_REACT_APP\\node_modules\\xlsx\\xlsx.js:23738:9)\n    at Object.excel (C:\\CODE_SANDBOX\\GEN_REACT_APP\\e2e\\utils\\Common\\handleexcel.js:8:21)\n    at Object.<anonymous> (C:\\CODE_SANDBOX\\GEN_REACT_APP\\e2e\\pages\\Login.js:10:21)\n    at Object.<anonymous> (C:\\CODE_SANDBOX\\GEN_REACT_APP\\e2e\\testcases\\Projectfeatures.test.js:2:24)"}, {"message": "ENOENT: no such file or directory, open './e2e/DataFile.xlsx'", "stack": "Error: ENOENT: no such file or directory, open './e2e/DataFile.xlsx'\n    at read_binary (C:\\CODE_SANDBOX\\GEN_REACT_APP\\node_modules\\xlsx\\xlsx.js:3153:44)\n    at readSync (C:\\CODE_SANDBOX\\GEN_REACT_APP\\node_modules\\xlsx\\xlsx.js:23698:69)\n    at Object.readFileSync (C:\\CODE_SANDBOX\\GEN_REACT_APP\\node_modules\\xlsx\\xlsx.js:23738:9)\n    at Object.excel (C:\\CODE_SANDBOX\\GEN_REACT_APP\\e2e\\utils\\Common\\handleexcel.js:8:21)\n    at Object.<anonymous> (C:\\CODE_SANDBOX\\GEN_REACT_APP\\e2e\\pages\\Login.js:10:21)\n    at Object.<anonymous> (C:\\CODE_SANDBOX\\GEN_REACT_APP\\e2e\\testcases\\ShareProject.test.js:3:24)"}, {"message": "=================\n no tests found.\n================="}]}