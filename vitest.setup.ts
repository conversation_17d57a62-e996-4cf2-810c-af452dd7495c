import '@testing-library/jest-dom';
import { vi } from 'vitest';

// Set up window.process for legacy code compatibility
Object.defineProperty(window, 'process', {
  value: {
    env: {
      NODE_ENV: 'test',
      VITE_APP_BASE: 'dev',
      VITE_APP_FLUID_MODE: 'frs',
      VITE_APP_DEBUG_LOG: 'debug'
      // Add any other environment variables your tests need
    }
  },
  writable: true
});

// Mock matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: (query: string) => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(),
    removeListener: vi.fn(),
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn()
  })
});

// Define global test mocks
vi.mock('@nexusplatform/react', () => ({
  useHexAuth: () => ({
    getAccessTokenSilently: vi.fn().mockResolvedValue('mock-token')
  })
}));

// Mock window.ResizeObserver
window.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn()
}));

// Mock MutationObserver required by React Hook Form validation
global.MutationObserver = class {
  constructor(callback) {
    // Empty constructor needed for the mock
  }
  disconnect() {
    // No-op function
  }
  observe(element, initObject) {
    // No-op function
  }
  takeRecords() {
    return [];
  }
};

// Mock requestAnimationFrame
global.requestAnimationFrame = function (callback) {
  return setTimeout(callback, 0);
};

// Mock cancelAnimationFrame
global.cancelAnimationFrame = function (id) {
  clearTimeout(id);
};
