import { LanguageType } from '@nexusui/theme';
import { IRawStroke } from '@microsoft/live-share-canvas';
import { t } from 'i18next';

export const stringValues = {
  justNow: t('justNowLabel'),
  readMore: t('readMore'),
  readLess: t('readLess'),
  showMore: t('showMore'),
  showLess: t('showLess'),
  replies: t('replies'),
  close: t('close')
};

export const timeAgo = (date: Date) => {
  const formatter = new Intl.RelativeTimeFormat('en');
  const ranges: { [key: string]: number } = {
    years: 3600 * 24 * 365,
    months: 3600 * 24 * 30,
    weeks: 3600 * 24 * 7,
    days: 3600 * 24,
    hours: 3600,
    minutes: 60,
    seconds: 1
  };
  const secondsElapsed = (date?.getTime() - Date.now()) / 1000;
  for (const key in ranges) {
    if (ranges[key] < Math.abs(secondsElapsed)) {
      const delta = secondsElapsed / ranges[key];
      return formatter.format(Math.round(delta), key as Intl.RelativeTimeFormatUnit);
    }
  }
  return stringValues?.justNow;
};

export type LanguageOption = {
  value: LanguageType;
  title: string;
};

export const languageDict: LanguageOption[] = [
  { value: 'en-US', title: 'English (US)' },
  { value: 'en-GB', title: 'English (UK)' },
  { value: 'fr-FR', title: 'Français' },
  { value: 'de-DE', title: 'Deutsch' },
  { value: 'it-IT', title: 'Italia' }
];

// https://stackoverflow.com/questions/75774800/how-to-stop-resizeobserver-loop-limit-exceeded-error-from-appearing-in-react-a
export const handleErrors = (e: any) => {
  if (e.message === 'ResizeObserver loop limit exceeded') {
    const resizeObserverErrDiv = document.getElementById('webpack-dev-server-client-overlay-div');
    const resizeObserverErr = document.getElementById('webpack-dev-server-client-overlay');
    if (resizeObserverErr) {
      resizeObserverErr.setAttribute('style', 'display: none');
    }
    if (resizeObserverErrDiv) {
      resizeObserverErrDiv.setAttribute('style', 'display: none');
    }
  }
};

const captureInkingToImageUrl = () => {
  let url = '';
  let canvasW = 0;
  let canvasH = 0;
  const inkPanel = document.getElementById('divOverlayRef');

  if (inkPanel) {
    const inkCanvas = inkPanel.getElementsByTagName('canvas')[0];

    if (inkCanvas) {
      url = inkCanvas.toDataURL('image/png');
      canvasW = inkCanvas.width;
      canvasH = inkCanvas.height;
    }
  }

  return { url, width: canvasW, height: canvasH };
};

export const overlayInkingImageToCameraSnapshot = async (CADSnapshotBlob: any, callback: (blob: any) => void) => {
  const { url, width, height } = captureInkingToImageUrl();

  // If inkingCanvasActive
  if (width) {
    const c = document.createElement('canvas');
    c.width = width;
    c.height = height;
    const ctx = c.getContext('2d') as any;

    const image = new Image();
    const cadUrl = URL.createObjectURL(CADSnapshotBlob);
    image.src = cadUrl;
    await image.decode();

    URL.revokeObjectURL(cadUrl);

    const image2 = new Image();
    image2.src = url;
    await image2.decode();

    ctx?.drawImage(image, 0, 0, width, height);
    ctx?.drawImage(image2, 0, 0, width, height);

    c.toBlob((blob) => {
      callback(blob);
    });
  } else {
    callback(CADSnapshotBlob);
  }
};

export const parseStrokes = (text: string): IRawStroke[] => {
  const rawJson: unknown = JSON.parse(text);
  if (isStrokeList(rawJson)) {
    return rawJson;
  }
  throw Error('Invalid JSON value.');
};

function isStrokeList(value: unknown): value is IRawStroke[] {
  return Array.isArray(value) && value.every(isStroke);
}

function isStroke(value: unknown): value is IRawStroke {
  return typeof value === 'object' && !!value && Array.isArray((value as any).points) && typeof (value as any).brush === 'object';
}

export const getSecureRandomNumber = () => {
  const crypto = window.crypto || window.Crypto;
  const array = new Uint32Array(1);
  crypto.getRandomValues(array); // Compliant for security-sensitive use cases
  return array[0] / (0xffffffff + 1); // scales it to the range [0, 1)
};

export const cleanSensitiveInfo = async (purgeRedux = true) => {
  localStorage.removeItem('persist:root');

  // Keeping to clear for existing users if any
  localStorage.removeItem('access_token');
  localStorage.removeItem('ems_token');
  localStorage.removeItem('sdc_token');

  if (purgeRedux) {
    try {
      const { persist } = await import('../redux');
      persist.purge();
    } catch (error) {
      console.error('Failed to purge Redux store:', error);
    }
  }
};
