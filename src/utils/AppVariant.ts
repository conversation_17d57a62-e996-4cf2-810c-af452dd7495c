export interface ApplicationVariant {
  createdDate: string;
  id: string;
  applicationId: string;
  name: string;
  description: string;
  status: string;
  extensions?: Extension[];
  communityForumsUrl?: string;
  documentationUrl?: string;
  requestHelpUrl?: string;
  updatedBy: string;
  updatedDate: string;
}
export interface AppTagData {
  id: string;
  name: string;
  type: string;
  orgIdpId: string;
  misc?: {
    nfd: string;
  };
  communityForumsUrl?: string;
  documentationUrl?: string;
  requestHelpUrl?: string;
}

export interface Extension {
  id: string;
  enabled: boolean;
}
