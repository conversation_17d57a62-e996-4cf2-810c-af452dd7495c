import {
  ADDITIVE_APPLICATIONS as addApps,
  PLASTIC_APPLICATIONS as plasticApps,
  ADAM_CAR_APPLICATION as adamCarApps,
  COMPUTE_APPLICATIONS as computeApps,
  MAGNETO_APPLICATIONS as sasApps,
  PRODUCTION_SOFTWARE_APPLICATIONS as prodApps
} from '../configuration/application.confg';

export const isAdditiveApplication = (app: string) => {
  return Object.keys(addApps).some((key) => {
    return addApps[key].tag.toLowerCase() === app.toLowerCase();
  });
};

export const isSASApplication = (app: string) => {
  return Object.keys(sasApps).some((key) => {
    return sasApps[key].tag.toLowerCase() === app.toLowerCase();
  });
};
export const isPlasticsApplication = (app: string) => {
  return Object.keys(plasticApps).some((key) => {
    return plasticApps[key].tag.toLowerCase() === app.toLowerCase();
  });
};

export const isAdamCarApplication = (app: string) => {
  return Object.keys(adamCarApps).some((key) => {
    return adamCarApps[key].tag.toLowerCase() === app.toLowerCase();
  });
};

export const isComputeApplication = (app: string) => {
  return Object.keys(computeApps).some((key) => {
    return computeApps[key].tag.toLowerCase() === app.toLowerCase();
  });
};

export const isPointSolution = (app: string) => {
  return isPlasticsApplication(app) || isAdditiveApplication(app) || isComputeApplication(app) || isSASApplication(app) || isAdamCarApplication(app);
};

export const isProductionSoftwareApplication = (appTags: string) => {
  const apps = appTags.split(',');
  return Object.keys(prodApps).some((key) => {
    return apps.includes(prodApps[key].tag.toLowerCase());
  });
};
