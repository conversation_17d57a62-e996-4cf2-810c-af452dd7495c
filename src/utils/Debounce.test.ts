// eslint-disable-next-line no-unused-vars
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { debounce } from './Debounce';
vi.useFakeTimers();
describe('Debounce', () => {
  let func: ReturnType<typeof vi.fn>;
  let debouncedFunc: () => void;

  beforeEach(() => {
    func = vi.fn();
    debouncedFunc = debounce(func, 1000);
  });
  it('should debounce after a set time', () => {
    for (let i = 0; i < 100; i++) {
      debouncedFunc();
    }

    // Fast-forward time
    vi.runAllTimers();

    expect(func).toBeCalledTimes(1);
  });
});
