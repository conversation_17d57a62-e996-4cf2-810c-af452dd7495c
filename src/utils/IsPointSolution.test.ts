import React from 'react';
import { isAdditiveApplication, isPlasticsApplication, isPointSolution, isComputeApplication, isSASApplication, isAdamCarApplication } from './IsPointSolution';
describe('Can User Edit Util', () => {
  it('should check if the application is additive application', () => {
    const isAdditiveApex = isAdditiveApplication('apex');
    const isAdditiveCads = isAdditiveApplication('cads');
    const isAdditiveSFAdditive = isAdditiveApplication('sf-additive');
    const isNotAdditive = isAdditiveApplication('not-additive');
    expect(isAdditiveSFAdditive).toBe(true);
    expect(isAdditiveCads).toBe(true);
    expect(isAdditiveApex).toBe(true);
    expect(isNotAdditive).toBe(false);
  });

  it('should check if plastic application', () => {
    const isPlasticsApplicationMarc = isPlasticsApplication('marcmentat');
    const isNotPlasticsApplication = isPlasticsApplication('not-plastics');
    expect(isPlasticsApplicationMarc).toBe(true);
    expect(isNotPlasticsApplication).toBe(false);
  });

  it('should check if adamCar application', () => {
    const isComputeApplicationEsprit = isAdamCarApplication('adamsCar');
    const isNotComputeApplication = isAdamCarApplication('not-adamcar');
    expect(isComputeApplicationEsprit).toBe(true);
    expect(isNotComputeApplication).toBe(false);
  });

  it('should check if compute application', () => {
    const isComputeApplicationEsprit = isComputeApplication('compute');
    const isNotComputeApplication = isComputeApplication('not-compute');
    expect(isComputeApplicationEsprit).toBe(true);
    expect(isNotComputeApplication).toBe(false);
  });

  it('should check if sas application', () => {
    const isSASApp = isSASApplication('smartassembly');
    const isNotComputeApplication = isSASApplication('not-compute');
    expect(isSASApp).toBe(true);
    expect(isNotComputeApplication).toBe(false);
  });

  it('should check if point solution', () => {
    const isPointSolutionMarc = isPointSolution('marcmentat');
    const isPointSolutionApex = isPointSolution('apex');
    const isPointSolutionCads = isPointSolution('cads');
    const isPointSolutionAdams = isPointSolution('adamsCar');
    const isNotPointSolution = isPointSolution('not-point-solution');
    expect(isPointSolutionMarc).toBe(true);
    expect(isPointSolutionApex).toBe(true);
    expect(isPointSolutionCads).toBe(true);
    expect(isPointSolutionAdams).toBe(true);
    expect(isNotPointSolution).toBe(false);
  });
});
