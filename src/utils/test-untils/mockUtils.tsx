/* eslint-disable camelcase */
/* eslint-disable require-jsdoc */
/* !
 * Copyright (c) Microsoft Corporation and contributors. All rights reserved.
 * Licensed under the MIT License.
 */

import { PropertyProxy } from '@fluid-experimental/property-proxy';
import { BaseProperty, NodeProperty, PropertyFactory } from '@fluid-experimental/property-properties';
import {
  nonPrimitiveCollectionsSchema,
  primitiveCollectionsSchema,
  sdcViewerSampleSchema,
  customSessionsSchema,
  customMainSchema,
  customWorkspacesSchema,
  customAdditiveSchema
} from './schemas';
import React from 'react';
import { AuthContext } from 'react-oidc-context';
import { Provider } from 'react-redux';
import configureStore from 'redux-mock-store';
import { vi } from 'vitest';

export const uniqueIdentifier = 'uniqueIdentifier';

const registerSchemas = () => {
  const schemas = [
    primitiveCollectionsSchema,
    nonPrimitiveCollectionsSchema,
    sdcViewerSampleSchema,
    customSessionsSchema,
    customMainSchema,
    customWorkspacesSchema,
    customAdditiveSchema
  ];

  schemas.forEach((schema) => {
    PropertyFactory.register(schema);
  });
};

export const populateWorkspace = (workspace: MockWorkspace) => {
  registerSchemas();

  workspace.root.insert('String', PropertyFactory.create('String', 'single', 'Hello ') as BaseProperty);

  const primitives = ['String'];
  const collections = ['Array', 'Map'];
  for (const type of primitives) {
    for (const context of collections) {
      workspace.root.insert(type.toLowerCase() + context, PropertyFactory.create(type, context.toLowerCase()) as BaseProperty);
    }
  }

  const property = PropertyFactory.create('hxgn:SmartDataContract-1.0.0', undefined, {
    sessions: { main: { workspaces: { additive: { assembly: { name: 'test123', subParts: { basePlate: { name: 'base1' } } } } } } }
  }) as BaseProperty;

  workspace.root.insert('sdc', property);
};

// eslint-disable-next-line require-jsdoc
export class MockWorkspace {
  public root: NodeProperty;
  // eslint-disable-next-line require-jsdoc
  constructor() {
    this.root = PropertyFactory.create('NodeProperty');
    this.root.getWorkspace = () => this;
  }
  getRoot() {
    return this.root;
  }
  commit() {
    return Promise.resolve();
  }
  get<T>(...args: (string | string[])[]) {
    return this.root.get<T>(...args);
  }
  getIds() {
    return this.root.getIds();
  }
  getEntriesReadOnly() {
    return this.root.getEntriesReadOnly();
  }
  insert(in_id: string, in_property: BaseProperty) {
    return this.root.insert(in_id, in_property);
  }
  // eslint-disable-next-line @typescript-eslint/no-empty-function
  on(event: string, args: any) {}
}

export const initializeWorkspace = (populate = true) => {
  const workspace = new MockWorkspace();
  if (populate) {
    populateWorkspace(workspace);
  }
  const rootProxy = PropertyProxy.proxify(workspace.getRoot());
  return { workspace, rootProxy };
};

export const mockAuthContextValue = {
  activeNavigator: undefined,
  user: {
    access_token: 'mock-access-token',
    sub: 'mock-sub',
    email: '<EMAIL>'
  },
  isAuthenticated: true,
  isLoading: false,
  error: null,
  signinRedirect: vi.fn(),
  signinSilent: vi.fn().mockResolvedValue({
    access_token: 'mock-access-token'
  }),
  signoutRedirect: vi.fn(),
  signoutSilent: vi.fn(),
  removeUser: vi.fn(),
  events: {
    addUserLoaded: vi.fn(),
    addSilentRenewError: vi.fn(),
    addUserSignedOut: vi.fn(),
    addUserSessionChanged: vi.fn(),
    addAccessTokenExpired: vi.fn(),
    addAccessTokenExpiring: vi.fn()
  }
};

export const AuthProviderMock: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <AuthContext.Provider value={mockAuthContextValue as any}>{children}</AuthContext.Provider>
);

// Create the mock store creator
const mockStore = configureStore([]);

// Default state that can be used across tests
const defaultStoreState = {
  userProfile: {
    user: {
      name: 'Test User',
      email: '<EMAIL>'
    },
    language: 'en',
    theme: 'light',
    access_token: 'mock-token'
  }
};

export const StoreProviderMock: React.FC<{
  children: React.ReactNode;
  initialState?: Record<string, any>;
}> = ({ children, initialState = defaultStoreState }) => {
  const store = mockStore(initialState);
  return <Provider store={store}>{children}</Provider>;
};
