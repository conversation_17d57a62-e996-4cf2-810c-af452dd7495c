/* !
 * Copyright (c) Microsoft Corporation and contributors. All rights reserved.
 * Licensed under the MIT License.
 */

export const primitiveCollectionsSchema = {
  properties: [
    {
      context: 'array',
      id: 'array',
      typeid: 'Int32',
      value: [1, 2, 3]
    },
    {
      context: 'map',
      id: 'map',
      typeid: 'String',
      value: { a: 'Phone', b: 'Home' }
    }
  ],
  typeid: 'autodesk.collections:primitive-1.0.0'
};

export const primitiveCollectionsNodeSchema = {
  inherits: ['NodeProperty', 'autodesk.collections:primitive-1.0.0'],
  typeid: 'autodesk.collections:primitive.node-1.0.0'
};

export const nonPrimitiveCollectionsSchema = {
  properties: [
    {
      context: 'array',
      id: 'array',
      typeid: 'autodesk.math:point3d-1.0.0',
      value: [
        { x: 1, y: 0, z: 0 },
        { x: 0, y: 1, z: 0 },
        { x: 0, y: 0, z: 1 }
      ]
    },
    {
      context: 'map',
      id: 'map',
      typeid: 'autodesk.math:point3d-1.0.0',
      value: {
        axisX: { x: 1, y: 0, z: 0 },
        axisY: { x: 0, y: 1, z: 0 },
        axisZ: { x: 0, y: 0, z: 1 }
      }
    },
    {
      context: 'set',
      id: 'set',
      typeid: 'NamedProperty',
      value: [{}, {}]
    }
  ],
  typeid: 'autodesk.collections:non.primitive-1.0.0'
};

export const customAdditiveSchema = {
  properties: [
    {
      id: 'name',
      typeid: 'String'
    }
  ],
  typeid: 'sdcViewer:Additive-1.0.0'
};

export const customWorkspacesSchema = {
  properties: [
    {
      id: 'name',
      typeid: 'String'
    },
    {
      id: 'subParts',
      typeid: 'sdcViewer:Additive-1.0.0',
      context: 'map'
    }
  ],
  typeid: 'sdcViewer:Workspaces-1.0.0'
};

export const customMainSchema = {
  properties: [
    {
      id: 'assembly',
      typeid: 'sdcViewer:Workspaces-1.0.0'
    }
  ],
  typeid: 'sdcViewer:Main-1.0.0'
};

export const customSessionsSchema = {
  properties: [
    {
      id: 'workspaces',
      typeid: 'sdcViewer:Main-1.0.0',
      context: 'map'
    }
  ],
  typeid: 'sdcViewer:Session-1.0.0'
};

export const sdcViewerSampleSchema = {
  properties: [
    {
      id: 'sessions',
      typeid: 'sdcViewer:Session-1.0.0',
      context: 'map'
    }
  ],
  typeid: 'hxgn:SmartDataContract-1.0.0'
};
