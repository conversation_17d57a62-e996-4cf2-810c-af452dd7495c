// eslint-disable-next-line no-unused-vars
import { describe, it, expect } from 'vitest';
import { IUserInfo } from '@nexusui/components';

import { IUser } from '../models/User';
import { setUserFirstNameAndLastName } from './AudienceUserInfo';

describe('Audience user info Utils', () => {
  it('should return users info with first and last name', () => {
    const user: IUser = { id: 'Test', name: 'First Last', email: 'Last' };
    const audience: IUserInfo = { id: 'Test', email: '<EMAIL>', firstName: '', lastName: '', avatar: '' };
    setUserFirstNameAndLastName(user, audience);
    expect(audience.firstName).toBe('First');
    expect(audience.lastName).toBe('Last');
  });

  it('should return users info with first and last name for sfx user', () => {
    const metadata = { sfx: { GivenName: 'Fisrt', Surname: 'Last' } };
    const user: IUser = { id: 'Test', name: 'Test Name', email: '<EMAIL>', metadata: metadata };
    const audience: IUserInfo = { id: 'Test', email: '<EMAIL>', firstName: '', lastName: '', avatar: '' };
    setUserFirstNameAndLastName(user, audience);
    expect(audience.firstName).toBe(metadata.sfx.GivenName);
    expect(audience.lastName).toBe(metadata.sfx.Surname);
  });
});
