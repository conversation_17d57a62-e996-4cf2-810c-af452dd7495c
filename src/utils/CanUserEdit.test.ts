// eslint-disable-next-line no-unused-vars
import { describe, it, expect } from 'vitest';
import { IUserProject } from './CanUserEdit';
import { canUserEdit } from './CanUserEdit';
describe('Can User Edit Util', () => {
  const project: IUserProject = {
    acl: [
      { userId: 'mack', access: 'readWrite' },
      { userId: 'mack2', access: 'readOnly' }
    ]
  };
  const sampleUserReadWrite = { sub: 'mack' };
  const sampleUserReadOnly = { sub: 'mack2' };

  it('should return true if user has read/write permission', () => {
    const perimssion = canUserEdit(project, sampleUserReadWrite);
    expect(perimssion).toBe(true);
  });

  it('should return true if user has read permission', () => {
    const perimssion = canUserEdit(project, sampleUserReadOnly);
    expect(perimssion).toBe(false);
  });
});
