import { IUserInfo } from '@nexusui/components';
import { IUser } from '../models/User';

export const setUserFirstNameAndLastName = (user: IUser, audienceUserInfo: IUserInfo) => {
  audienceUserInfo.firstName = user && user?.metadata && user?.metadata?.sfx && user?.metadata?.sfx.GivenName ? user?.metadata?.sfx?.GivenName : '';
  audienceUserInfo.lastName = user && user?.metadata && user?.metadata?.sfx && user?.metadata?.sfx.Surname ? user?.metadata?.sfx?.Surname : '';
  if (audienceUserInfo.firstName === '' && user && user.name) {
    const splitname = user.name.split(/(\s+)/).filter((e) => e.trim().length > 0);
    audienceUserInfo.firstName = splitname[0];
    audienceUserInfo.lastName = splitname[1];
  }
};
