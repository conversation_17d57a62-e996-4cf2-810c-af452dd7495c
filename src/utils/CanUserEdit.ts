import { IACLUser } from '../models/Project';
import { ORG_ID_LOCATOR } from '../configuration/URL.config';

export interface IUserProject {
  acl: Array<IACLUser>;
}
export interface IAuthUser {
  sub: string;
}
export const canUserEdit = (project: any, user: any) => {
  if (user) {
    const found = project.acl.find((projectUser: any) => {
      return projectUser.userId === user.sub;
    });
    const foundOrg = project.acl.find((projectOrg: any) => {
      return projectOrg.organizationId === user[ORG_ID_LOCATOR];
    });
    if (found || (found && foundOrg.access === 'readOnly')) {
      return found.access === 'readWrite';
    }
    if (!found && foundOrg) {
      return foundOrg.access === 'readWrite';
    }
  }
  return false;
};
