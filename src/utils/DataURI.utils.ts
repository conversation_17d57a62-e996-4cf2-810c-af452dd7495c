import Colorize from '@mui/icons-material/Colorize';
import WebIcon from '@mui/icons-material/Web';
// import InvertColorsIcon from '@mui/icons-material/InvertColors';
// import PrecisionManufacturingIcon from '@mui/icons-material/PrecisionManufacturing';
import SmartToyOutlinedIcon from '@mui/icons-material/SmartToyOutlined';
import DirectionsCarFilledOutlinedIcon from '@mui/icons-material/DirectionsCarFilledOutlined';
import computeIcon from '/src/assets/images/compute-app.svg';
import { IDocumentTypeInfo } from '@nexusplatform/core-react-components';

import {
  APPLICATIONS as apps,
  ADDITIVE_APPLICATIONS as additiveApps,
  PLASTIC_APPLICATIONS as plasticApps,
  MATERIAL_CENTER_APPLICATIONS as matApps,
  MAGNETO_APPLICATIONS as magnetoApps,
  ADAM_CAR_APPLICATION as adamCarApps,
  COMPUTE_APPLICATIONS as computeApps,
  PRODUCT_DIGITAL_TWIN_APPLICATION as productDigitalTwinApps,
  adamsCarTag,
  computeTag,
  matEnrichTag,
  plasticTag,
  metrologyReportingTag,
  productDigitalTwinTag,
  smartAssemblyTag,
  productDigitalTwin,
  additiveTag
} from '../configuration/application.confg';
import Design from '../assets/Design.png';
import { t } from 'i18next';

export const getDataUri = (appName: string, id: string): string | null => {
  if (apps && Object.prototype.hasOwnProperty.call(apps, appName)) {
    return apps[appName].dataURI + id;
  }
  return null;
};

export const getApplicationTag = (appName: string): string | null => {
  if (apps && Object.prototype.hasOwnProperty.call(apps, appName)) {
    return apps[appName].tag;
  }
  return null;
};

export const getApplicationGroupTag = (appNameStr: string): string => {
  const appName = appNameStr.toLocaleLowerCase();
  if (additiveApps && Object.prototype.hasOwnProperty.call(additiveApps, appName)) {
    return additiveTag;
  } else if (plasticApps && Object.prototype.hasOwnProperty.call(plasticApps, appName)) {
    return plasticTag;
  } else if (matApps && Object.prototype.hasOwnProperty.call(matApps, appName)) {
    return matEnrichTag;
  } else if (magnetoApps && Object.prototype.hasOwnProperty.call(magnetoApps, appName)) {
    return smartAssemblyTag;
  } else if (adamCarApps && Object.prototype.hasOwnProperty.call(adamCarApps, appName)) {
    return adamsCarTag;
  } else if (computeApps && Object.prototype.hasOwnProperty.call(computeApps, appName)) {
    return computeTag;
  } else if (productDigitalTwinApps && Object.prototype.hasOwnProperty.call(productDigitalTwinApps, appName)) {
    return productDigitalTwinTag;
  }
  return '';
};

export const getAppNameFromProjectTag = (projectTag: string): string => {
  if (projectTag === adamsCarTag) {
    return 'adamscar';
  } else if (projectTag === computeTag) {
    return 'compute';
  } else if (projectTag === additiveTag) {
    return 'additive';
  } else if (projectTag === plasticTag) {
    return 'plastic';
  } else if (projectTag === matEnrichTag) {
    return 'materialsenrich';
  } else if (projectTag === productDigitalTwin) {
    return 'productDigitalTwin';
  } else if (projectTag === smartAssemblyTag) {
    return 'smartAssembly';
  }

  return '';
};

export const additiveManufacturingProjectTypeInfo: IDocumentTypeInfo = {
  value: 'Design and Additive Manufacturing',
  mainHeading: t('dfam'),
  subHeading: t('designAndAMSubHeading'),
  tags: ['additiveManufacturing'],
  icon: Design
};

export const plasticProjectTypeInfo: IDocumentTypeInfo = {
  value: 'Plastics',
  mainHeading: t('PlasticTileText'),
  subHeading: t('plasticSubHeading'),
  tags: ['plastic'],
  icon: Colorize
};

export const metrologyProjectTypeInfo: IDocumentTypeInfo = {
  value: 'Metrology',
  mainHeading: t('metrologyMainHeading'),
  subHeading: t('metrologySubHeading'),
  tags: ['metroReports'],
  icon: WebIcon
};

export const smartAssemblyProjectTypeInfo: IDocumentTypeInfo = {
  value: 'smartAssembly',
  mainHeading: t('smartassembly'),
  subHeading: t('smartassemblySubHeading'),
  tags: ['smartassembly'],
  icon: SmartToyOutlinedIcon
};

export const adamsCarProjectTypeInfo: IDocumentTypeInfo = {
  value: 'adamsCar',
  mainHeading: t('adamsCar'),
  subHeading: t('adadamsCarSubHeadingamsCar'),
  tags: ['adamsCar'],
  icon: DirectionsCarFilledOutlinedIcon
};

export const computeProjectTypeInfo: IDocumentTypeInfo = {
  value: 'compute',
  mainHeading: t('compute'),
  subHeading: t('computeSubHeading'),
  tags: ['compute'],
  icon: computeIcon
};

export const productDigitalTwinProjectTypeInfo: IDocumentTypeInfo = {
  value: 'productDigitalTwin',
  mainHeading: t('productDigitalTwin'),
  subHeading: t('productDigitalTwinSubHeading'),
  tags: ['productDigitalTwin'],
  icon: computeIcon
};

// export const materialsEnrichProjectType: IDocumentTypeInfo = {
//   value: 'materialsEnrich',
//   mainHeading: 'Material Enrichment',
//   subHeading: 'Enrich material data portfolio',
//   tags: ['asterix'],
//   icon: PrecisionManufacturingIcon
// };

export const getAllProjectTypeInfos = (selectedTags: string[]): IDocumentTypeInfo[] => {
  const projectTypes: IDocumentTypeInfo[] = [];
  selectedTags?.forEach((tag) => {
    if (tag === additiveTag) {
      projectTypes.push(additiveManufacturingProjectTypeInfo);
    } else if (tag === plasticTag) {
      projectTypes.push(plasticProjectTypeInfo);
    } else if (tag === metrologyReportingTag) {
      projectTypes.push(metrologyProjectTypeInfo);
    } else if (tag === smartAssemblyTag) {
      projectTypes.push(smartAssemblyProjectTypeInfo);
    } else if (tag === adamsCarTag) {
      projectTypes.push(adamsCarProjectTypeInfo);
    } else if (tag === computeTag) {
      projectTypes.push(computeProjectTypeInfo);
    } else if (tag === productDigitalTwinTag) {
      projectTypes.push(productDigitalTwinProjectTypeInfo);
    }
  });
  return projectTypes;
};

export const getProjectTypeInfo = (appName: string): IDocumentTypeInfo[] | undefined => {
  if (additiveApps && Object.prototype.hasOwnProperty.call(additiveApps, appName)) {
    return [additiveManufacturingProjectTypeInfo];
  } else if (plasticApps && Object.prototype.hasOwnProperty.call(plasticApps, appName)) {
    return [plasticProjectTypeInfo];
  } else if (adamCarApps && Object.prototype.hasOwnProperty.call(adamCarApps, appName)) {
    return [adamsCarProjectTypeInfo];
  } else if (computeApps && Object.prototype.hasOwnProperty.call(computeApps, appName)) {
    return [computeProjectTypeInfo];
  } else if (productDigitalTwinApps && Object.prototype.hasOwnProperty.call(productDigitalTwinApps, appName)) {
    return [productDigitalTwinProjectTypeInfo];
  }
  // else if (matApps && Object.prototype.hasOwnProperty.call(matApps, appName)) {
  //  return [
  //    materialsEnrichProjectType
  //  ];
  // }
  else if (magnetoApps && Object.prototype.hasOwnProperty.call(magnetoApps, appName)) {
    return [smartAssemblyProjectTypeInfo];
  } else {
    return [
      additiveManufacturingProjectTypeInfo,
      plasticProjectTypeInfo,
      metrologyProjectTypeInfo,
      smartAssemblyProjectTypeInfo,
      adamsCarProjectTypeInfo,
      computeProjectTypeInfo,
      productDigitalTwinProjectTypeInfo
      // ,
      // materialsEnrichProjectType
      // }
    ];
  }
};

export const getRedirectionKey = (appName: string): string => {
  if (plasticApps && Object.prototype.hasOwnProperty.call(plasticApps, appName.toLocaleLowerCase())) {
    return plasticApps[appName.toLocaleLowerCase()].dataURI;
  } else if (additiveApps && Object.prototype.hasOwnProperty.call(additiveApps, appName.toLocaleLowerCase())) {
    return additiveApps[appName.toLocaleLowerCase()].dataURI;
  } else if (matApps && Object.prototype.hasOwnProperty.call(matApps, appName.toLocaleLowerCase())) {
    return matApps[appName.toLocaleLowerCase()].dataURI;
  } else if (magnetoApps && Object.prototype.hasOwnProperty.call(magnetoApps, appName.toLocaleLowerCase())) {
    return magnetoApps[appName.toLocaleLowerCase()].dataURI;
  } else if (adamCarApps && Object.prototype.hasOwnProperty.call(adamCarApps, appName.toLocaleLowerCase())) {
    return adamCarApps[appName.toLocaleLowerCase()].dataURI;
  } else if (computeApps && Object.prototype.hasOwnProperty.call(computeApps, appName.toLocaleLowerCase())) {
    return computeApps[appName.toLocaleLowerCase()].dataURI;
  } else if (productDigitalTwinApps && Object.prototype.hasOwnProperty.call(productDigitalTwinApps, appName.toLocaleLowerCase())) {
    return productDigitalTwinApps[appName.toLocaleLowerCase()].dataURI;
  }
  return '';
};
