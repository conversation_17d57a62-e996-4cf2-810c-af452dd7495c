// eslint-disable-next-line no-unused-vars
import { describe, it, expect } from 'vitest';
// import { APPLICATIONS as apps } from '../configuration/application.confg';
import { getDataUri, getApplicationTag, getApplicationGroupTag, getProjectTypeInfo } from './DataURI.utils';

describe('Data URI Utils', () => {
  it('should return a data uri', () => {
    const dataURI = getDataUri('designer', 'happy');
    const expected = 'nexus-designer://happy';
    expect(dataURI).toBe(expected);
  });

  it('should return a null if app does not exist', () => {
    const dataURI = getDataUri('designera', 'happy');
    expect(dataURI).toBe(null);
  });

  it('should return a null tag if app does not exist', () => {
    const dataURI = getApplicationTag('designera');
    expect(dataURI).toBe(null);
  });
  it('should return a null if app does not exist', () => {
    const dataURI = getDataUri('designera', 'happy');
    // const expected = 'nexus-designer://happy';
    expect(dataURI).toBe(null);
  });

  it('should return the tag', () => {
    const tag = getApplicationTag('designer');
    const expected = 'additiveManufacturing';
    expect(tag).toBe(expected);
  });

  it('should return the group tag', () => {
    const tag1 = getApplicationGroupTag('apex');
    const tag2 = getApplicationGroupTag('digimatrp');
    const tag3 = getApplicationGroupTag('smartassembly');
    const expected1 = 'additiveManufacturing';
    const expected2 = 'plastic';
    const expected3 = 'smartassembly';
    expect(tag1).toBe(expected1);
    expect(tag2).toBe(expected2);
    expect(tag3).toBe(expected3);
  });

  it('should return the project info', () => {
    const tag1 = getProjectTypeInfo('apex');
    const tag2 = getProjectTypeInfo('digimatrp');
    const tag3 = getProjectTypeInfo('smartassembly');
    const expected1 = 'Design';
    const expected2 = 'Plastics';
    const expected3 = 'smartAssembly';
    tag1 && expect(tag1[0].value).toContain(expected1);
    tag2 && expect(tag2[0].value).toContain(expected2);
    tag3 && expect(tag3[0].value).toContain(expected3);
  });
});
