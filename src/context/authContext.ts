/* eslint-disable @typescript-eslint/no-empty-function */
import React from 'react';
import { UserRoles } from './AdminContext';
import { IOrgItemSelfUser, IUserDetails, IUserSelfResponse } from '../models/User.model';

export interface IAuthContext {
  userDetails: IUserDetails;
  privileges: string[];
  orgPrivileges: string[];
  defaultOrganization: string;
  organizations: IOrgItemSelfUser[];
  currentUserRoles: string[];
  currentOrgUserRole: string;
  currentAmsUser: IUserSelfResponse | null;
  isLoading: boolean;

  setPrivileges: (a: string[]) => void;
  setOrgPrivileges: (a: string[]) => void;
  setCurrentUserRoles: (a: string[]) => void;
  setCurrentOrgUserRole: (a: string) => void;
}

export const AuthContext = React.createContext<IAuthContext>({
  userDetails: { name: '', picture: '', email: '', given_name: '', family_name: '' },
  privileges: [],
  orgPrivileges: [],
  defaultOrganization: '',
  organizations: [],
  currentUserRoles: [UserRoles.User],
  currentOrgUserRole: UserRoles.User,
  currentAmsUser: null,
  isLoading: false,

  setPrivileges: (a: string[]) => {},
  setOrgPrivileges: (a: string[]) => {},
  setCurrentUserRoles: (a: string[]) => {},
  setCurrentOrgUserRole: (a: string) => {}
});
