// React import
import React, { useCallback, useContext, useEffect, useLayoutEffect, useRef, useState } from 'react';
// Custom Components
import { IProject, DocumentType } from '../models/Project';
import { ProjectGroupData, ShareAccessFilter, SortFilter, SortEnum, SortFilterDocument } from '../configuration/filters.config';
import { ContainerType, createSdcInstance, retrieveSdcInstances, SdcCollectionResponse, SdcResponse } from '@nexusplatform/core';
import axios, { RawAxiosRequestHeaders } from 'axios';
import QueryString from 'qs';
import { InformationWithProgressBackDrop } from '../components/BackDrops/InformationWithProgressBackDrop';
import { Box, Typography, Stack, Drawer, List, ListItemText, ListItemButton, ListSubheader, Divider, IconButton, Button, Link } from '@mui/material';

import { BASE_PATH, BASE_APP_PATH, BASE_URL, METRO_ROUTE } from '../configuration/URL.config';
import { useSearchParams, useNavigate, useParams } from 'react-router-dom';
import {
  adamsCarProjectTypeInfo,
  additiveManufacturingProjectTypeInfo,
  computeProjectTypeInfo,
  getAllProjectTypeInfos,
  getApplicationGroupTag,
  getApplicationTag,
  getProjectTypeInfo,
  getRedirectionKey,
  metrologyProjectTypeInfo,
  plasticProjectTypeInfo,
  productDigitalTwinProjectTypeInfo,
  smartAssemblyProjectTypeInfo
} from '../utils/DataURI.utils';
import { useTranslation } from 'react-i18next';
import { HexFilter } from '../components/HexFilter/HexFilter';
import { HexSearch } from '@nexusplatform/core-react-components';
import { HexInputSelect } from '../components/CustomFormFields/HexInputSelect/HexInputSelect';
import { HexInputSelectMobile } from '../components/CustomFormFields/HexInputSelect/HexInputSelectMobile';

import { debounce } from '../utils/Debounce';
import { PAGESIZE, PROJECTS_PAGESIZE } from '../configuration/PAGESIZE.config';
import { HexErrorPageCard } from '../components/ErrorPage/hex-error-page-card';
import { canUserEdit } from '../utils/CanUserEdit';
import { HexUnfollowDialog } from '../components/HexUnfollowDialogComponent/HexUnfollowDialog';
import { MATERIAL_CENTER_APPLICATIONS } from '../configuration/application.confg';
import { HexDeleteDialog } from '../components/HexDeleteDialog';
import SortIcon from '@mui/icons-material/Sort';
import FilterAltOutlinedIcon from '@mui/icons-material/FilterAltOutlined';
import PeopleAltOutlinedIcon from '@mui/icons-material/PeopleAltOutlined';
import { HexFilterMobile } from '../components/HexFilter/HexFilterMobile';
import { TagContext } from '../App';
import { isPointSolution, isProductionSoftwareApplication } from '../utils/IsPointSolution';
import { sdcPrivilegeContext } from '../context/SdcUserPrivilegesContext';
import { CAD_DEMO_PROJECT, DEMO_PROJECT } from '../utils/data/DemoProject';
import FolderOpenOutlinedIcon from '@mui/icons-material/FolderOpenOutlined';
import StickyNote2OutlinedIcon from '@mui/icons-material/StickyNote2Outlined';
import EditNoteOutlinedIcon from '@mui/icons-material/EditNoteOutlined';
import { HexNoProjects } from '../components/HexNoProjects/HexNoProjects';
import MenuIcon from '@mui/icons-material/Menu';
import { MobileMenu } from '../components/MobileMenu/MobileMenu';
import { unfollowProject } from '../services/folders.service';
import { FoldersComponent } from './Folders/Folders';
import { IFolderList } from '../models/Folders';
import { GroupOutlined } from '@mui/icons-material';
import './ProjectsDocumentView.css';
import { HexRenameFolder } from './ProjectSettings/HexRenameFolder/HexRenameFolder';
import { CreateProjectDialog } from './CreateProject/CreateProject';
import { createProject } from '../services/CreateProject';
import { CreateDocument, IDocumentInfo, OptionType, Snackbar } from '@nexusui/components';
import { IProjectTypeInfo } from '@nexusplatform/core/lib/dist/mocks/Project';
import { getFolderInstance } from '../services/shareDialog.service';
import InfoIcon from '@mui/icons-material/Info';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import { HexEmptyFolder } from './EmptyProjectView/HexEmptyFolder';
import { AMS_AUDIENCE } from '../configuration/AUTH.config';
import Extensions from './Extensions/Extensions';
import { useFlags } from 'launchdarkly-react-client-sdk';
import { IDocument } from '@nexusui/connected-components';
import { ConnectedDocumentView } from './ConnectedDocumentView/ConnectedDocumentView';
import { HexEmptyProject } from './EmptyProjectView/HexEmptyProject';
import ShareDialogComponent from './ShareDialog';
import { useHexAuth } from '@nexusplatform/react';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../redux';
import { setProjectCount } from '../redux/slices/userProjectCount';

export interface IProjectEventDetails {
  id: string;
  name: string;
  description: string;
}

interface ProjectDocumentViewProps {
  forceMobileMode?: boolean;
}

/**
 * Provides the project viewer
 */
function ProjectDocumentView({ forceMobileMode = false }: ProjectDocumentViewProps) {
  const [query] = useSearchParams();
  const appTag = query.get('app');
  const dynamicTag = query.get('dynamic-url');
  const paramsTag = query.get('filter');
  const currentlySelectedTag = query.get('currentlySelected');
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isLoader, setIsLoader] = useState<boolean>(true);
  const [hexDocuments, setHexDocuments] = useState<Array<IProject>>([]);
  const [showCreateDocument, setShowCreateDocument] = useState<boolean>(false);
  const [showCreateDemoDocument, setShowCreateDemoDocument] = useState<boolean>(false);
  const [containerWidth, setContainerWidth] = useState<number>(0);
  const [containerHeight, setContainerHeight] = useState<number>(0);
  const [showListView, setShowListView] = useState(false);
  const [showSavingDialog, setShowSavingDialog] = useState(false);
  const [showUnfollowDialog, setShowUnfollowDialog] = useState(false);
  const [showUnfollowProgress, setShowUnfollowProgress] = useState(false);
  const [selectedProject, setSelectedProject] = useState<IProject | null>();
  const [selectedFolder, setSelectedFolder] = useState<IFolderList | null>();
  const [hasError, setHasError] = useState<boolean>(false);
  const sortMenuAnchorEl = useRef(null);
  const [showSortFilter, setShowSortFilter] = useState(false);
  const [showMobileSelect, setShowMobileSelect] = useState<boolean>(false);
  const [selectedTags, setSelectedTags] = useState<string | undefined>(paramsTag ? paramsTag : appTag ? getApplicationGroupTag(appTag) : '');
  const [accessType, setAccessType] = useState<string>('all');
  const [ProjectAccessType, setProjectAccessType] = useState<string>('all');
  const [showFilterMobile, setShowFilterMobile] = useState<boolean>(false);
  const [showFolderRenamePopup, setShowFolderRenamePopup] = useState<boolean>(false);
  const selectedFolderRef = useRef<IFolderList>();
  const [showDeleteProjectModal, setshowDeleteProjectModal] = useState(false);
  const [showUnfollowProjectModal, setshowUnfollowProjectModal] = useState(false);
  const [isDemoProject, setIsDemoProject] = useState<boolean>(false);
  const projectsContainerRef = useRef<HTMLDivElement>(null);
  const sidePanelRef = useRef<HTMLDivElement>(null);
  const navigate = useNavigate();
  const xmsContinuation = useRef('');
  const hexAuthToken = useRef('');
  const projectNameInfo = useRef<string | undefined>('');
  const { t } = useTranslation();
  const pageSize = useRef(1);
  const projectsCount = useRef<number>(0);
  const [searchProjectName, setSearchProjectName] = useState<string | undefined>('');
  const [searchFolderName, setSearchFolderName] = useState<string | undefined>('');
  const [xmsToken, setXmsToken] = useState<string>('');
  const showProjectFilter = useRef(true);
  const filterAnchorRef = useRef(null);
  const accessTypeAnchorRef = useRef(null);
  const [isMobile, setIsMobile] = useState<boolean>(forceMobileMode);
  const [isDraft, setIsDraft] = useState<boolean>(false);
  const [isProject, setIsProject] = useState<boolean>(false);
  const [isDocument, setIsDocument] = useState<boolean>(false);
  const [isProjectCardClicked, setIsProjectCardClicked] = useState<boolean>(false);

  const [isMenu, setIsMenu] = useState<boolean>(false);
  const [foldersList, setFoldersList] = useState<IFolderList[]>([]);
  const [headerTitle, setHeaderTitle] = useState<string>('');

  const [isCreateProject, setIsCreateProject] = useState<boolean>(false);
  const [projectName, setProjectName] = useState<string>('');
  const [projectOptions, setprojectOptions] = useState<OptionType[]>([]);
  const [solutionOptions, setsolutionOptions] = useState<OptionType[]>([]);
  const selectedApp = useContext<string>(TagContext);
  const { sdcPrivileges } = useContext(sdcPrivilegeContext);
  const { projectId: clickedProjectId } = useParams();
  const [isProjectLoader, setIsProjectLoader] = useState<boolean>(false);
  const [infoIconProject, setInfoIconProject] = useState<boolean>(false);
  const [infoIconDocument, setInfoIconDocument] = useState<boolean>(false);
  const [infoIconDraft, setInfoIconDraft] = useState<boolean>(false);
  const { platformManageExtensionsLinkButtonVisible, platformSortLastOpenedBymeFilterMenuItemVisible } = useFlags();
  const [projectNameValue, setProjectNameValue] = useState('');
  const SortFilterDocumentFlagged = useRef(SortFilterDocument);
  const [documents, setDocuments] = React.useState<IDocument[]>([]); // The documents you want to display in connected document card
  const [showShareDialog, setShowShareDialog] = useState(false);
  const { getAccessTokenSilently } = useHexAuth();

  const user = useSelector((state: RootState) => state.userProfile.user);
  const projectCount = useSelector((state: RootState) => state.userProjectCount.projectCount);
  const dispatch = useDispatch();

  useEffect(() => {
    if (platformSortLastOpenedBymeFilterMenuItemVisible && SortFilterDocumentFlagged.current.options.length === 2) {
      SortFilterDocumentFlagged.current.options.push({ value: SortEnum.lastOpenedByMe, name: 'Sort by last opened by me' });
    }
  }, [platformSortLastOpenedBymeFilterMenuItemVisible]);

  useEffect(() => {
    if (appTag) {
      showProjectFilter.current = getApplicationGroupTag(appTag) !== '' ? false : true;
    } else {
      showProjectFilter.current = true;
    }
  }, [appTag]);
  useEffect(() => {
    if (isProductionSoftwareApplication(selectedApp)) {
      getExtensions();
    }
  }, []);

  useEffect(() => {
    const data = sessionStorage.getItem('data');
    const clickedProject = sessionStorage.getItem('clickedProject');
    const toggle = sessionStorage.getItem('toggle');
    if (toggle === 'false') {
      sessionStorage.setItem('folderName', '');
    }
    if (data == 'Drafts') {
      setIsDraft(true);
      setHeaderTitle(`${t('drafts')}`);
    } else if (data == 'Documents') {
      setIsDocument(true);
      setHeaderTitle(`${t('allDocuments')}`);
    } else if (data == 'Projectcard' && clickedProjectId) {
      setIsProjectCardClicked(true);
      if (clickedProject) {
        setHeaderTitle(`${clickedProject}`);
      }
    } else {
      setIsProject(true);
      setHeaderTitle(`${t('projects')}`);
      sessionStorage.setItem('data', 'Projects');
      sessionStorage.setItem('ProjectAccessType', 'all');
      sessionStorage.setItem('folderName', '');
    }
    getSearchFolders();
  }, []);

  useEffect(() => {
    const project = foldersList?.find((ele) => ele.id === clickedProjectId);
    project && setProjectNameValue(project?.name);
    project && sessionStorage.setItem('clickedProject', project?.name);
  }, [foldersList]);

  const deleteProjectInstance = useCallback(async () => {
    const customHeaders = await getCustomHeaders();
    const config = {
      url: `${BASE_URL}/project/instances/${selectedFolderRef.current!.id}`,
      headers: {
        ...customHeaders
      }
    };
    return axios
      .delete(config.url, { headers: config.headers })
      .then((response) => {
        getSearchFolders();
        Snackbar.success(`"${selectedFolderRef.current!.name}" ${t('deletedSuccessfully').toLocaleLowerCase()}`, { enableClose: true });
      })
      .catch((error) => {
        if (error.response.status == 403) Snackbar.error(`${t('errorDeletingAuthorization')}`, { enableClose: true });
        else Snackbar.error(`${t('errorDeletingProject')} "${selectedFolderRef.current!.name}"`, { enableClose: true });
      })
      .finally(() => {
        setshowDeleteProjectModal(false);
      });
  }, []);

  const shareProjectFolder = useCallback(
    async (folder: IFolderList, show?: boolean) => {
      setSelectedFolder(folder);
      setShowShareDialog(true);
    },
    [selectedFolder]
  );

  const handleCloseProject = () => {
    setshowDeleteProjectModal(false);
    setshowUnfollowProjectModal(false);
  };
  const getCustomHeaders = async () => {
    const JWT = await getAccessTokenSilently();
    if (JWT) {
      let customHeaders: RawAxiosRequestHeaders;
      hexAuthToken.current = JWT;

      if (xmsContinuation.current !== null && xmsContinuation.current && xmsContinuation.current !== 'Token not found') {
        customHeaders = {
          authorization: `Bearer ${JWT}`,
          'x-ms-continuation': xmsContinuation.current
        };
      } else {
        customHeaders = {
          authorization: `Bearer ${JWT}`
        };
      }
      return customHeaders;
    }
    return undefined;
  };

  const getUserOwnedProjects = useCallback(async () => {
    const JWT = await getAccessTokenSilently();
    let customHeaders: RawAxiosRequestHeaders = {};
    if (JWT) {
      customHeaders = {
        authorization: `Bearer ${JWT}`
      };
    }
    const queryParams = { orderBy: 'createdByDate', orderDirection: 'ASC', accessType: 'all' };
    const response = await axios({
      method: 'get',
      baseURL: BASE_URL,
      url: '/project/allInstances',
      headers: customHeaders,
      params: queryParams
    });
    if (response && response.status === 200) {
      return response.data.content;
    }
    return [];
  }, []);

  const setUserOwnedPojects = useCallback(async (type: DocumentType, isMoveDocument: boolean) => {
    const userOwnedProjects: IFolderList[] = await getUserOwnedProjects();
    setprojectOptions(() => {
      const options: OptionType[] = [];
      switch (type) {
        case DocumentType.projectDocuments: {
          const project = userOwnedProjects.find((project: IFolderList) => project.id === clickedProjectId);
          options[0] = { label: project?.name, value: project?.id ?? '' };
          return [...options];
        }
        case DocumentType.allDocuments: {
          options[0] = { label: t('chooseProject'), value: '0', disabled: true };
          break;
        }
        default: {
          if (!isMoveDocument) {
            options[0] = { label: t('drafts'), value: '0', disabled: false };
          }
        }
      }
      if (userOwnedProjects && userOwnedProjects.length > 0) {
        userOwnedProjects.sort((a, b) => a.name.localeCompare(b.name));
        userOwnedProjects.forEach((project: IFolderList) => {
          options.push({ label: project.name, value: project.id });
        });
      }
      return [...options];
    });
  }, []);

  const handleCreateDemoProject = async () => {
    setprojectOptions([]);
    setsolutionOptions(() => {
      const options: OptionType[] = [];
      options[0] = { label: 'CAD Assembly', value: 'CAD Assembly', disabled: false };
      options[1] = { label: t('dfam'), value: 'Design and Additive Manufacturing', disabled: false };
      return [...options];
    });
    setShowCreateDemoDocument(true);
  };

  const handleCreateDocumentClick = async (type: DocumentType) => {
    setIsDemoProject(false);
    setprojectOptions([]);
    setUserOwnedPojects(type, false);

    // Selected document is now based on the selected filters on create document and it is NOT  based on the current app tag only.
    // This is to support the multiple solution type. eg. apex now can create document of two types (additiveManufacturing and smart-assembly).
    // if app tag is only presented then default app tag projects will be displayed. eg. apex will display additiveManufacturing projects.
    // if app tag and selected tags are presented then selected tags projects will be displayed. eg. apex and additiveManufacturing will display additiveManufacturing , smart assembly documents.
    const selectedFilterProjectTypeInfo = selectedTags && getAllProjectTypeInfos(selectedTags.split(','));
    const projectTypeInfo = selectedFilterProjectTypeInfo && selectedFilterProjectTypeInfo.length > 0 ? selectedFilterProjectTypeInfo : getProjectTypeInfo(appTag || '');

    setsolutionOptions(() => {
      const options: OptionType[] = [];
      if (projectTypeInfo && projectTypeInfo.length > 0) {
        projectTypeInfo.forEach((project: IProjectTypeInfo) => {
          options.push({ label: project.mainHeading, value: project.value });
        });
      }
      return [...options];
    });
    setShowCreateDocument(true);
  };
  const addQueryParam = (key: string, value: string) => {
    const url = new URL(window.location.href);
    if (!value.length) {
      url.searchParams.get(key) && url.searchParams.delete(key);
    } else {
      url.searchParams.set(key, value);
    }
    window.history.pushState(null, '', url.toString());
  };

  const onFilterChange = (filterNames: string[]) => {
    setShowFilterMobile(false);
    // Warning: setQueryParams will cause reload the hole page, replace it with `addQueryParam` function.
    // do not know if we want to have this thing here to also filter by app... maybe
    const selectedTags = filterNames.length > 0 ? filterNames.join(',').toString() : '';
    addQueryParam('app', appTag ? appTag : '');
    addQueryParam('filter', selectedTags);
    setSelectedTags(selectedTags.length > 0 ? selectedTags : undefined);
  };

  useEffect(() => {
    const fetchData = async () => {
      !isLoading ? setDocuments(hexDocuments as IDocument[]) : setDocuments([]);
    };
    fetchData();
  }, [isLoading, hexDocuments]);

  // TODO: Need more abstraction in this component... we have technical debt.
  const checkSelectedProject = (projectList: IProject[]) => {
    if (currentlySelectedTag) {
      setHexDocuments((prevstate: any) => {
        const combinedArr = [...prevstate, ...projectList];
        const distinctArr = combinedArr.filter((item, i) => combinedArr.findIndex((t) => t.id === item.id) === i);
        let data = [...distinctArr];
        // recent item to be listed at the top
        const recentItem = currentlySelectedTag && distinctArr.find((item) => item.id === currentlySelectedTag);
        if (recentItem) {
          data = distinctArr.filter((item) => item.id !== currentlySelectedTag);
          data = [recentItem, ...data];
        }
        return data;
      });
    } else {
      setHexDocuments([...projectList]);
    }
  };

  const getHexDocuments = useCallback(async () => {
    try {
      const customHeaders = await getCustomHeaders();
      setIsLoader(true);

      if (xmsContinuation.current === 'Token not found') {
        handleTokenNotFound();
      } else {
        const queryParams = buildQueryParams();
        const response = await retrieveSdcInstances(BASE_URL, customHeaders ?? null, queryParams, undefined, 'v2', ContainerType.All);
        handleGetSdcInstancesResponse(response);
      }
    } catch (err) {
      handleError();
    }
  }, [xmsToken]);

  const handleTokenNotFound = () => {
    setIsLoader(false);
    setIsLoading(false);
  };

  const buildQueryParams = () => {
    const page = parseInt(PAGESIZE);
    const draftsPage = sessionStorage.getItem('data');
    const queryParams: any =
      selectedTags !== '' && !selectedTags?.includes('all')
        ? { pageSize: page, tags: selectedTags, orderBy: 'createdByDate', orderDirection: 'DESC', hasProject: draftsPage === 'Drafts' ? false : true }
        : { pageSize: page, orderBy: 'createdByDate', orderDirection: 'DESC', hasProject: draftsPage === 'Drafts' ? false : true };
    if (accessType !== '' && accessType !== 'all') {
      queryParams['accessType'] = accessType;
    }
    return queryParams;
  };

  const handleGetSdcInstancesResponse = (response: SdcCollectionResponse) => {
    if (response && response.status === 200) {
      processSuccessfulResponse(response);
    } else {
      handleError();
    }
  };

  const processSuccessfulResponse = (response: SdcCollectionResponse) => {
    setIsLoader(false);

    const projectsToPass: IProject[] = response.data.content.map((project: any) => {
      const editPermission = canUserEdit(project, user);

      return {
        ...project,
        thumbnail: project.thumbnailRef && project.thumbnailRef.sas ? project.thumbnailRef.sas : null,
        canDelete: user?.id === project.createdBy,
        canUnfollow: user?.id !== project.createdBy,
        canUserRename: editPermission,
        canChangeCover: editPermission
      };
    });

    let projects = projectsToPass;
    const clickedProject = sessionStorage.getItem('clickedProject');
    if (clickedProject) {
      projects = projectsToPass.filter((project: IProject) => project.projectId === clickedProjectId);
    }

    setIsLoading(false);
    setHexDocuments([...projects]);
    xmsContinuation.current = response.continuationToken || 'Token not found';
    setXmsToken(response.continuationToken || 'Token not found');
    pageSize.current = pageSize.current + 1;
    setHasError(false);
  };

  const handleError = () => {
    setIsLoader(false);
    setIsLoading(false);
    setHasError(true);
    Snackbar.error(t('errorRetrievingProjects'), { enableClose: true });
  };

  useLayoutEffect(() => {
    const updateView = () => {
      const containerWidth = projectsContainerRef.current?.offsetWidth || 0;
      const sidePanelWidth = sidePanelRef.current?.offsetWidth || 0;
      const totalWidth = containerWidth + sidePanelWidth;

      // Skip width detection if forceMobileMode is enabled
      if (!forceMobileMode) {
        if (!showListView && totalWidth < 900) {
          setShowListView(true);
          setIsMobile(true);
        } else if (showListView && totalWidth >= 900) {
          setShowListView(false);
          setIsMobile(false);
        }
      } else {
        // Force mobile mode settings
        setShowListView(true);
        setIsMobile(true);
      }

      setContainerWidth(totalWidth);
      setContainerHeight(projectsContainerRef.current?.offsetHeight || 0);
    };

    const debounceMe = debounce(updateView, 500);
    window.addEventListener('resize', debounceMe);
    updateView();

    return () => window.removeEventListener('resize', debounceMe);
  }, [showListView, forceMobileMode]);

  const closeCreateProject = useCallback(() => {
    setShowCreateDocument(false);
    setShowCreateDemoDocument(false);
  }, []);

  const handleMaterialEnrichRedirection = (redirectKey: string, id: string) => {
    if (id !== '') {
      if (dynamicTag) {
        window.location.assign(BASE_APP_PATH + (BASE_PATH + BASE_PATH.slice(-1) === '/' ? '' : '/') + `${redirectKey}/${dynamicTag}/` + id);
      } else {
        window.location.assign(BASE_APP_PATH + (BASE_PATH + BASE_PATH.slice(-1) === '/' ? '' : '/') + `${redirectKey}/` + id);
      }
    } else {
      window.location.assign(BASE_APP_PATH + (BASE_PATH + BASE_PATH.slice(-1) === '/' ? '' : '/') + `materials-enrich/`);
    }
  };
  const createNewDocument = useCallback(async (value: IProject) => {
    setShowCreateDocument(false);
    setShowSavingDialog(true);

    // TODO: Update to have workflow type in the later one
    const newProject: IProject = { project: value.project, description: value.description, schemaTypeId: 'hxgn:SmartDataContract-1.0.0', projectId: value.projectId };

    try {
      const tagToPass = value.tags;
      const defaultTags = getApplicationTag(appTag ?? '');
      if (tagToPass) {
        newProject.tags = tagToPass;
      } else if (defaultTags) {
        newProject.tags = [defaultTags];
      } else {
        throw new Error('No default document tag found');
      }

      if (newProject.tags && newProject.tags.length === 1 && newProject.tags[0] === MATERIAL_CENTER_APPLICATIONS.materialsenrich.tag) {
        const redirectKey = getRedirectionKey('materialsenrich');
        if (redirectKey) {
          handleMaterialEnrichRedirection(redirectKey, '');
        }
      } else {
        const customHeaders = await getCustomHeaders();
        const response: SdcResponse = await createSdcInstance(newProject, BASE_URL, customHeaders ?? null);
        if (response) {
          // setShowCreateProject(false);
          response.data['canDelete'] = user?.id === response.data.createdBy;
          response.data['canUnfollow'] = user?.id !== response.data.createdBy;
          setHexDocuments((prev: any) => [response.data, ...prev]);
          Snackbar.success(`"${newProject.project}" ${t('savedSuccessfully')}`, { enableClose: true });
          setShowSavingDialog(false);
          const newProjectId = response.data['id'];
          handleClickProject(newProjectId, response.data as IProject);
        } else {
          Snackbar.error(`${t('errorWhileSaving')}`, { enableClose: true });
          setShowSavingDialog(false);
          dispatch(setProjectCount(projectCount + 1));
        }
      }
    } catch (err) {
      Snackbar.error(`${t('errorWhileSaving')}`, { enableClose: true });
      setShowSavingDialog(false);
    }
  }, []);

  const api = axios.create({
    headers: {
      'Content-Type': 'application/json'
    },
    paramsSerializer: (params) => {
      return QueryString.stringify(params, { arrayFormat: 'repeat' });
    }
  });

  const createDemoDocument = useCallback(async (value: IDocumentInfo) => {
    setShowCreateDemoDocument(false);
    setShowSavingDialog(true);
    let projectData;
    if (value.solution === 'CAD Assembly') {
      projectData = CAD_DEMO_PROJECT;
      if (value && value.name && value.name !== '') {
        projectData = { ...CAD_DEMO_PROJECT, project: value.name };
      }
    } else if (value.solution === 'Design and Additive Manufacturing') {
      projectData = DEMO_PROJECT;
      if (value && value.name && value.name !== '') {
        projectData = { ...DEMO_PROJECT, project: value.name };
      }
    }
    if (clickedProjectId) {
      projectData.projectId = clickedProjectId ?? '';
    }
    const getCustomHeaders = async () => {
      const JWT = await getAccessTokenSilently();
      if (JWT) {
        const customHeaders: RawAxiosRequestHeaders = {
          authorization: `Bearer ${JWT}`
        };
        return customHeaders;
      }
    };

    const customHeaders = await getCustomHeaders();
    try {
      const response = await api.post<any>(BASE_URL + '/sdc/instances/createdemosdc', projectData, {
        headers: {
          ...customHeaders,
          'Content-Type': 'application/json'
        }
      });
      const { status, data } = response;
      if (status === 200) {
        setShowSavingDialog(false);
        const newProjectId = response.data['id'];
        handleClickProject(newProjectId, data);
      }
    } catch (error) {
      console.error('Failed to Create a demo project', value);
      Snackbar.error(t('failedToCreateDemoProject'), { enableClose: true });
      setShowSavingDialog(false);
    }
  }, []);

  const adjustUnfollowDialog = (open = true) => {
    setShowUnfollowDialog(open);
  };

  const handleClose = () => {
    adjustUnfollowDialog(false);
  };

  const onClickCreate = async () => {
    const customHeaders = await getCustomHeaders();
    setIsProjectLoader(true);
    setIsCreateProject(false);
    const projectData = {
      project: projectName,
      description: 'Test sdc project',
      tags: [''],
      metadata: {}
    };
    const response = await createProject(customHeaders, projectData);
    if (response.status == 200) {
      setIsProjectLoader(false);
      await setFoldersList((prev: any) => [response.data, ...prev]);
      Snackbar.success(`"${projectName}" ${t('projectSuccess')}`, { enableClose: true });
      setShowSavingDialog(false);
      dispatch(setProjectCount(projectCount + 1));
    } else {
      Snackbar.error(`${t('somethingWentWrongMsg')}`, { enableClose: true });
      setShowSavingDialog(false);
    }
    setIsProjectLoader(false);
  };

  const getProject = (id: string): IProject | undefined => {
    return hexDocuments.find((project: IProject) => {
      if (id === project.id) {
        return project;
      }
    });
  };
  const triggerCustomEvent = (details: IProjectEventDetails, eventName = 'nexus:projectSelected') => {
    const projectClicked = new CustomEvent<IProjectEventDetails>(eventName, { detail: { ...details } });
    window.dispatchEvent(projectClicked);

    // Code added here to for additive manufacutring
    const externalCefEventHandler = eventName in window ? (window as { [key: string]: any })[eventName] : undefined;
    if (externalCefEventHandler) {
      externalCefEventHandler(JSON.stringify({ detail: { ...details } }));
    }
  };

  // handles clicking a project to dispatch action to data-uri
  const handleClickProject = (id: string | null | undefined, _newProject?: IProject) => {
    if (id) {
      const project = _newProject ? _newProject : getProject(id);
      if (project && project?.tags?.includes('metroReports')) {
        // window.location.assign(METRO_ROUTE + project.id);
        window.open(METRO_ROUTE + project.id, '_blank');
      } else if (project && project?.tags?.includes('asterix')) {
        const redirectKey = getRedirectionKey('materialsenrich');
        if (redirectKey && project.id) {
          handleMaterialEnrichRedirection(redirectKey, project.id);
        }
      } else if (project && project?.tags?.includes('smartassembly')) {
        triggerCustomEvent({
          id: project.id as string,
          name: project.project,
          description: project.description
        });
        const redirectKey = getRedirectionKey('smartassembly');

        if (redirectKey) {
          window.location.assign(BASE_APP_PATH + (BASE_PATH + BASE_PATH.slice(-1) === '/' ? '' : '/') + `${redirectKey}/` + id);
        }
      } else if (project && project?.tags?.includes('adamsCar')) {
        triggerCustomEvent({
          id: project.id as string,
          name: project.project,
          description: project.description
        });
        const redirectKey = getRedirectionKey('adamsCar');
        if (redirectKey) {
          window.location.assign(BASE_APP_PATH + (BASE_PATH + BASE_PATH.slice(-1) === '/' ? '' : '/') + `${redirectKey}/` + id);
        }
      } else if (project && project?.tags?.includes('compute') && appTag?.toLocaleLowerCase() === 'apex') {
        triggerCustomEvent({
          id: project.id as string,
          name: project.project,
          description: project.description
        });
        const redirectKey = getRedirectionKey('apexcompute');
        if (redirectKey) {
          window.location.assign(BASE_APP_PATH + (BASE_PATH + BASE_PATH.slice(-1) === '/' ? '' : '/') + `${redirectKey}/` + id);
        }
      } else if (project && project?.tags?.includes('compute')) {
        triggerCustomEvent({
          id: project.id as string,
          name: project.project,
          description: project.description
        });
        const redirectKey = getRedirectionKey('compute');
        if (redirectKey) {
          window.location.assign(BASE_APP_PATH + (BASE_PATH + BASE_PATH.slice(-1) === '/' ? '' : '/') + `${redirectKey}/` + id);
        }
      } else if (project && project?.tags?.includes('plastic') && !appTag) {
        const redirectKey = getRedirectionKey('plastic');
        if (redirectKey) {
          window.location.assign(BASE_APP_PATH + (BASE_PATH + BASE_PATH.slice(-1) === '/' ? '' : '/') + `${redirectKey}/` + id);
        }
      } else if (project?.tags?.includes('productDigitalTwin')) {
        const redirectKey = getRedirectionKey('productDigitalTwin');
        if (redirectKey) {
          window.location.assign(BASE_APP_PATH + (BASE_APP_PATH + BASE_APP_PATH.slice(-1) === '/' ? '' : '/') + `${redirectKey}/` + id);
        }
      } else {
        if (project) {
          triggerCustomEvent({
            id: project.id as string,
            name: project.project,
            description: project.description
          });
        }
        const redirectionKey = getRedirectionKey(appTag || '');
        if (redirectionKey) {
          if (appTag) {
            window.location.assign(BASE_APP_PATH + (BASE_PATH + BASE_PATH.slice(-1) === '/' ? '' : '/') + `${redirectionKey}/` + id + '?app=' + appTag);
          } else {
            window.location.assign(BASE_APP_PATH + (BASE_PATH + BASE_PATH.slice(-1) === '/' ? '' : '/') + `${redirectionKey}/` + id);
          }
        } else if (appTag) {
          navigate('/project/' + id + '?app=' + appTag, { replace: false });
        } else {
          navigate('/project/' + id, { replace: false });
        }
      }
    }
  };

  const handleRemoveUser = async (projectOrSdc: string) => {
    if (user && user?.id) {
      const jwt = await getAccessTokenSilently();
      try {
        if (projectOrSdc === 'Project' && selectedFolder && selectedFolder.id) {
          setshowUnfollowProjectModal(true);
          const resp = await unfollowProject(selectedFolder.id, [user?.id], jwt);
          if (resp && resp.status === 200) {
            setshowUnfollowProjectModal(false);
            Snackbar.success(`${t('unfollowedSuccessfully')} "${selectedFolder.name}"`, { enableClose: true });
            handleClose();
            getSearchFolders();
            setSelectedFolder(null);
          } else if (resp.status === 206) {
            setShowUnfollowProgress(false);
            handleClose();
            Snackbar.info(`${t('cannotUnfollow')}`, { enableClose: true });
            getSearchFolders();
            setSelectedProject(null);
          } else {
            setshowUnfollowProjectModal(false);
            Snackbar.error(`${t('unfollowedError')} "${selectedFolder.name}"`, { enableClose: true });
          }
        }
      } catch (ex) {
        console.error(ex);

        setShowUnfollowProgress(false);
        Snackbar.error(`${t('unfollowedError')} "${selectedProject?.project}"`, { enableClose: true });
      }
    }
  };

  // This is required in case when we first search and navigate inside to a project and navigate back outside to home page and clear the search text.
  // The searchProjectName state needs to be in sync with session projectName.
  useEffect(() => {
    const searchFor = sessionStorage.getItem('projectName');
    searchFor && setSearchProjectName(searchFor);
  }, []);
  useEffect(() => {
    const searchFor = sessionStorage.getItem('folderName');
    searchFor && setSearchFolderName(searchFor);
  }, []);
  const changeFilterInformation = useCallback((event: React.ChangeEvent<HTMLInputElement>, value: string) => {
    // When do the clear action,the valve will return null, and if the value is them same with before, do nothing.
    const pageType = sessionStorage.getItem('data');
    const inputValue = !value ? '' : value;
    if (inputValue === projectNameInfo.current) {
      return;
    }
    event.preventDefault();
    if (pageType === 'Projects') {
      setSearchFolderName(inputValue);
      projectNameInfo.current = inputValue;
      sessionStorage.setItem('folderName', String(inputValue));
      getSearchFolders();
    } else {
      setSearchProjectName(inputValue);
      projectNameInfo.current = inputValue;
      sessionStorage.setItem('projectName', String(inputValue));
    }
  }, []);

  const checkSearchValue = useCallback(
    debounce((event: any) => {
      event.preventDefault();
      const pageType = sessionStorage.getItem('data');
      if (event.target.value.trim() || !searchProjectName || !searchFolderName) {
        projectNameInfo.current = event.target.value;
        if (pageType == 'Projects') {
          sessionStorage.setItem('folderName', String(projectNameInfo.current));
          setSearchFolderName(event.target.value);
          getSearchFolders();
        } else {
          sessionStorage.setItem('projectName', String(projectNameInfo.current));
          setSearchProjectName(event.target.value);
        }
      } else {
        if (pageType === 'Projects') {
          sessionStorage.removeItem('folderName');
          sessionStorage.removeItem('ProjectAccessType');
        } else sessionStorage.removeItem('projectName');
        sessionStorage.removeItem('accessType');
      }
    }, 1000),
    []
  );

  const defaultAccessType = 'all';
  const handleShareAccessChange = useCallback((v?: any) => {
    setShowMobileSelect(false);
    const pageType = sessionStorage.getItem('data');
    if (pageType === 'Projects') {
      sessionStorage.setItem('ProjectAccessType', String(v.value));
      setFoldersList([]);
      setProjectAccessType(v.value);
    } else sessionStorage.setItem('accessType', String(v.value));
    setHexDocuments([]);
    setAccessType(v.value);
  }, []);

  const getExtensions = useCallback(async () => {
    const getAmsCustomHeaders = async () => {
      let amsCustomHeaders: RawAxiosRequestHeaders;
      const JWT = await getAccessTokenSilently({ audience: AMS_AUDIENCE });
      if (JWT) {
        amsCustomHeaders = {
          authorization: `Bearer ${JWT}`
        };
        return amsCustomHeaders;
      }
      return undefined;
    };

    const amsCustomHeaders = await getAmsCustomHeaders();
    let appTags;
    let appVariant;
    let result;
    const withVariantURL = new URL(window.location.href);
    if (withVariantURL.searchParams.has('appVariant')) {
      appTags = withVariantURL.searchParams.get('app');
      appVariant = withVariantURL.searchParams.get('appVariant');
      result = await axios.get(BASE_URL + `/ams/applications/extensions?appTags=${appTags}&appVariant=${appVariant}`, { headers: amsCustomHeaders });
    } else {
      const url = window.location.href.toString().replace(/&/g, ',');
      const urlParams = new URLSearchParams(new URL(url).search);
      appTags = urlParams.get('app');
      result = await axios.get(BASE_URL + `/ams/applications/extensions?appTags=${appTags}`, { headers: amsCustomHeaders });
    }
    const documentView: any = result.data?.find((item) => item?.documentView === 'enabled');
    if (documentView?.length !== 0) {
      setIsDocument(true);
      setHeaderTitle(`${t('allDocuments')}`);
    }
  }, [getAccessTokenSilently, t]);

  const getSearchFolders = useCallback(async () => {
    try {
      const searchFor = sessionStorage.getItem('folderName');
      const sessionAccessType = sessionStorage.getItem('ProjectAccessType') ?? ProjectAccessType;
      if ((searchFor && searchFor === '') || sessionAccessType) {
        setIsLoader(true);
        const customHeaders = await getCustomHeaders();
        if (typeof customHeaders !== 'undefined') {
          delete customHeaders['x-ms-continuation'];
        }
        const queryParams: any =
          (selectedTags !== '' && !selectedTags?.includes('all')) ||
          (selectedTags !== '' && !selectedTags?.includes('all') && (searchFolderName || searchFor) && ProjectAccessType !== '' && ProjectAccessType !== 'all') ||
          (ProjectAccessType !== '' && ProjectAccessType !== 'all') ||
          searchFor
            ? {
                // TODO: NO POINT CALLING THE SESSION STORAGE API A 100 TIMES PLEASE FIX
                pageSize: PROJECTS_PAGESIZE,
                name: searchFor ? searchFor : '',
                accessType: sessionAccessType ? sessionAccessType : '',
                orderBy: 'createdByDate',
                orderDirection: 'DESC'
              }
            : { pageSize: PROJECTS_PAGESIZE, orderBy: 'createdByDate', orderDirection: 'DESC', accessType: sessionAccessType };

        const response = await axios({
          method: 'get',
          baseURL: BASE_URL,
          url: '/project/allInstances',
          headers: customHeaders,
          params: queryParams
        });
        if (response && response.status === 200) {
          setIsLoader(false);
          let editPermission;
          const projectsToPassList: IFolderList[] = response.data.content.map((project: any) => {
            editPermission = canUserEdit(project, user);

            return {
              ...project,
              thumbnail: project.thumbnailRef && project.thumbnailRef.sas ? project.thumbnailRef.sas : null,
              canDelete: user?.id === project.createdBy,
              canUnfollow: user?.id !== project.createdBy,
              canUserRename: editPermission,
              canChangeCover: editPermission
            };
          });
          const projects = projectsToPassList;
          xmsContinuation.current = response.headers['x-ms-continuation'] || 'Token not found';
          setXmsToken(response.headers['x-ms-continuation'] || 'Token not found');
          setFoldersList([...projects]);
          //  checkSelectedProject(projects);
          setIsLoading(false);
          setHasError(false);
        } else {
          setIsLoader(false);
          setHasError(true);
          Snackbar.error(t('errorRetrievingProjects'), { enableClose: true });
          setIsLoading(false);
        }
      }
    } catch (err) {
      setIsLoading(false);
      setIsLoader(false);
      setHasError(true);
      Snackbar.error(t('errorCallingResponse'), { enableClose: true });
    }
  }, [searchFolderName, accessType, selectedTags]);

  const handleAccessProjectFilter = useCallback(async (v: any) => {
    setIsLoader(true);
    setShowMobileSelect(false);
    sessionStorage.setItem('ProjectAccessType', String(v.value));
    const customHeaders = await getCustomHeaders();
    const queryParams = { pageSize: PROJECTS_PAGESIZE, orderBy: 'createdByDate', orderDirection: 'DESC', accessType: v.value };
    const result = await axios({
      method: 'get',
      baseURL: BASE_URL,
      url: '/project/allInstances',
      headers: customHeaders,
      params: queryParams
    });
    if (result.status == 200) {
      setIsLoader(false);
      await setFoldersList(result.data.content);
    } else {
      Snackbar.error(`${t('defaultErrorTitle')}`, { enableClose: true });
    }
    setIsLoader(false);
  }, []);
  // TODO: REFACTOR THIS METHOD... HOLY BATMAN DEEP LOGIC
  const getHexSearchDocuments = useCallback(async () => {
    try {
      const searchFor = sessionStorage.getItem('projectName');
      const sessionAccessType = sessionStorage.getItem('accessType') ?? accessType;
      const draftsPage = sessionStorage.getItem('data');
      if ((searchFor && searchFor === '') || sessionAccessType) {
        setHexDocuments([]);
        setIsLoader(true);
        const customHeaders = await getCustomHeaders();
        if (typeof customHeaders !== 'undefined') {
          delete customHeaders['x-ms-continuation'];
        }

        const queryParams: any =
          (selectedTags !== '' && !selectedTags?.includes('all')) ||
          (selectedTags !== '' && !selectedTags?.includes('all') && (searchProjectName || searchFor) && accessType !== '' && accessType !== 'all') ||
          (accessType !== '' && accessType !== 'all') ||
          searchFor
            ? {
                // TODO: NO POINT CALLING THE SESSION STORAGE API A 100 TIMES PLEASE FIX
                pageSize: PAGESIZE,
                tags: selectedTags,
                project: searchFor ?? '',
                accessType: sessionAccessType ?? '',
                orderBy: 'createdByDate',
                orderDirection: 'DESC',
                hasProject: draftsPage !== 'Drafts'
              }
            : { pageSize: PAGESIZE, orderBy: 'createdByDate', orderDirection: 'DESC', hasProject: draftsPage !== 'Drafts' };

        const response: SdcCollectionResponse = await retrieveSdcInstances(BASE_URL, customHeaders ?? null, queryParams, undefined, 'v2', ContainerType.All);

        if (response && response.status === 200) {
          setIsLoader(false);
          let editPermission;
          const projectsToPassList: IProject[] = response.data.content.map((project: any) => {
            editPermission = canUserEdit(project, user);

            return {
              ...project,
              thumbnail: project.thumbnailRef && project.thumbnailRef.sas ? project.thumbnailRef.sas : null,
              canDelete: user?.id === project.createdBy,
              canUnfollow: user?.id !== project.createdBy,
              canUserRename: editPermission,
              canChangeCover: editPermission
            };
          });
          let projects = projectsToPassList;
          const clickedProject = sessionStorage.getItem('clickedProject');

          if (clickedProject) {
            projects = projectsToPassList.filter((project: IProject) => project.projectId === clickedProjectId);
          }
          xmsContinuation.current = response.continuationToken || 'Token not found';
          setXmsToken(response.continuationToken || 'Token not found');
          setHexDocuments([...projects]);
          //  checkSelectedProject(projects);
          setIsLoading(false);
          setHasError(false);
        } else {
          setIsLoader(false);
          setHasError(true);
          Snackbar.error(t('errorRetrievingProjects'), { enableClose: true });
          setIsLoading(false);
        }
      } else {
        getHexDocuments();
      }
    } catch (err) {
      setIsLoading(false);
      setIsLoader(false);
      setHasError(true);
      Snackbar.error(t('errorCallingResponse'), { enableClose: true });
    }
  }, [searchProjectName, accessType, selectedTags]);

  useEffect(() => {
    const page = sessionStorage.getItem('data');
    if (page !== 'Projects') {
      getHexSearchDocuments();
    }
  }, [getHexSearchDocuments, isDraft, isDocument]);

  useEffect(() => {
    projectsCount.current = hexDocuments.length;
  }, [hexDocuments]);

  // close the dialog when we resize between desktop/mobile
  useEffect(() => {
    setShowSortFilter(false);
    window.sessionStorage.setItem('isMobile', isMobile?.toString());
  }, [isMobile]);

  const setPageCountInSessionStorage = useCallback(() => {
    sessionStorage.removeItem('accessType');
  }, []);

  useEffect(() => {
    window.addEventListener('beforeunload', setPageCountInSessionStorage);
    return () => {
      window.removeEventListener('beforeunload', setPageCountInSessionStorage);
    };
  }, [setPageCountInSessionStorage]);

  const onClickSort: React.MouseEventHandler<HTMLButtonElement> = (e) => {
    setShowSortFilter(true);
  };

  const handleSortMenuClick = (option: any) => {
    const userId = user?.id;
    const sortByCreateTimeFunc = (a: IProject, b: IProject) => (b.createdByDate || '')?.localeCompare(a.createdByDate || '');
    const sortByAZFunc = (a: IProject, b: IProject) => a.project?.localeCompare(b.project);
    const sortByLastOpened = (a: IProject, b: IProject) =>
      (b.mostRecentlyUsed ? (b.mostRecentlyUsed[userId as string] ? b.mostRecentlyUsed[userId as string].lastOpenedDate : '') : '')?.localeCompare(
        a.mostRecentlyUsed ? (a.mostRecentlyUsed[userId as string] ? a.mostRecentlyUsed[userId as string].lastOpenedDate : '') : ''
      );
    let sortFunc;
    switch (option.value) {
      case SortEnum.aToz:
        sortFunc = sortByAZFunc;
        break;
      case SortEnum.createDate:
        sortFunc = sortByCreateTimeFunc;
        break;
      case SortEnum.lastOpenedByMe:
        sortFunc = sortByLastOpened;
        break;
    }
    setDocuments((prev) => prev.sort(sortFunc));
    setShowSortFilter(false);
  };

  const handleFolderSortMenuClick = (option: any) => {
    const sortByCreateTimeFunc = (a: IFolderList, b: IFolderList) => (b.createdByDate || '').localeCompare(a.createdByDate || '');
    const sortByAZFunc = (a: IFolderList, b: IFolderList) => a.name.localeCompare(b.name);
    const sortFunc = option.value === SortEnum.aToz ? sortByAZFunc : sortByCreateTimeFunc;
    setFoldersList((prev) => prev.sort(sortFunc));
    setShowSortFilter(false);
  };

  const getContainerWidth = () => {
    const sidePanelWidth = !sidePanelRef.current ? 0 : sidePanelRef.current!.offsetWidth;
    return containerWidth - sidePanelWidth;
  };

  const mobileFilter = () => {
    setShowFilterMobile((prev) => !prev);
  };

  const mobileSelect = () => {
    setShowMobileSelect((prev) => !prev);
  };

  const handleNavigate = (url: string) => {
    let navUrl = url;
    if (appTag) {
      navUrl = navUrl + '?app=' + appTag;
    }

    // keep selected filters when navigating
    if (selectedTags && selectedTags.length > 0) {
      navUrl = navUrl + '&filter=' + selectedTags.split(',');
    }
    navigate(navUrl);
  };
  const onClickDraft = () => {
    sessionStorage.setItem('data', 'Drafts');
    sessionStorage.setItem('projectName', '');
    setIsDraft(true);
    setIsDocument(false);
    setIsProject(false);
    setIsProjectCardClicked(false);
    setHeaderTitle(`${t('drafts')}`);
    handleNavigate('/');
  };

  const onClickDocuments = () => {
    sessionStorage.setItem('data', 'Documents');
    sessionStorage.setItem('projectName', '');
    setIsDocument(true);
    setIsProject(false);
    setIsDraft(false);
    setIsProjectCardClicked(false);
    setHeaderTitle(`${t('allDocuments')}`);
    handleNavigate('/');
  };

  const onClickProjects = () => {
    sessionStorage.setItem('data', 'Projects');
    sessionStorage.setItem('projectName', '');
    setSearchProjectName('');
    sessionStorage.setItem('folderName', '');
    setSearchFolderName('');
    setIsProject(true);
    getSearchFolders();
    setIsDocument(false);
    setIsDraft(false);
    setIsProjectCardClicked(false);
    setHeaderTitle(`${t('projects')}`);
    handleNavigate('/');
  };

  const onClickProjectCard = (id: string, projectName: string) => {
    handleNavigate(`/projectfolders/${id}`);
    setIsProjectCardClicked(true);
    sessionStorage.setItem('data', 'Projectcard');
    sessionStorage.setItem('clickedProject', projectName);
  };

  useEffect(() => {
    if (clickedProjectId || projectNameValue) {
      setIsProjectCardClicked(true);
      setIsProject(false);
      setIsDocument(false);
      setIsDraft(false);
      const getProjectName = sessionStorage.getItem('clickedProject');
      setHeaderTitle(getProjectName || '');
    } else {
      sessionStorage.removeItem('clickedProject');
    }
  }, [clickedProjectId, projectNameValue]);

  const onClickMenu = () => {
    setIsMenu(true);
    // sessionStorage.setItem('folderName', '');
    getSearchFolders();
  };

  const handleMenuClose = () => {
    setIsMenu(false);
  };

  const onCreateProject = () => {
    setIsCreateProject(true);
  };

  const onCancleCreate = () => {
    setIsCreateProject(false);
  };

  const handleShareCurrentFolder = async (projectId: string) => {
    const customHeaders = await getCustomHeaders();
    const project = await getFolderInstance(projectId, customHeaders);
    shareProjectFolder(project!);
  };

  const isDisabled = !isPointSolution(selectedApp) || !sdcPrivileges.includes('create:sdcinstance');
  const isProjectCreateDisabled = !sdcPrivileges.includes('create:projectinstance');

  const updateFolderInstance = async (v: string) => {
    const customHeaders = await getCustomHeaders();
    const data = JSON.stringify({
      name: v
    });

    const config = {
      method: 'put',
      url: `${BASE_URL}/project/instances/${selectedFolderRef.current?.id}`,
      headers: {
        ...customHeaders,
        'Content-Type': 'application/json'
      },
      data: data
    };

    axios
      .put(config.url, config.data, { headers: config.headers })
      .then((resp) => {
        setShowFolderRenamePopup(false);
        const updateHexFolders = foldersList.map((hexFolder: IFolderList) => {
          if (hexFolder.id === selectedFolderRef.current?.id) {
            return { ...hexFolder, name: JSON.parse(config.data).name };
          } else {
            return { ...hexFolder };
          }
        });
        const selectedFolderUpdate = { ...selectedFolderRef.current, name: JSON.parse(config.data).name } as IFolderList;
        setFoldersList(updateHexFolders);
        selectedFolderRef.current = selectedFolderUpdate;
        // Snackbar.success(`${t('renamedSuccessfully')}`, {enableClose: true});
        triggerCustomEvent({ id: selectedFolderUpdate.id ?? '', description: selectedFolderUpdate.description, name: selectedFolderUpdate.name }, 'nexus:FolderRenamed');
      })
      .catch((error: any) => {
        console.error(error);
        if (error.response.status === 401) {
          Snackbar.error(`${t('errorUpdatingAuthorization')}`, { enableClose: true });
        } else {
          Snackbar.error(`${t('errorUpdatingProject')}`, { enableClose: true });
        }
      })
      .finally(() => {
        setShowFolderRenamePopup(false);
      });
  };
  const handleRenameFolder = (folder: IFolderList) => {
    setShowFolderRenamePopup(true);
    selectedFolderRef.current = foldersList.find((f) => f.id === folder.id) as IFolderList;
  };
  const handleDeleteProjectFolder = (folder: IFolderList) => {
    setshowDeleteProjectModal(true);
    selectedFolderRef.current = foldersList.find((f) => f.id === folder.id) as IFolderList;
  };

  const handleUnfollowProjectFolder = async (project: IFolderList) => {
    setSelectedFolder(project);
    setshowUnfollowProjectModal(true);
  };

  const onClickInfoIcon = () => {
    setInfoIconProject((prev) => !prev);
  };

  const onClickInfoDocument = () => {
    setInfoIconDocument((prev) => !prev);
  };

  const onClickInfoDraft = () => {
    setInfoIconDraft((prev) => !prev);
  };

  const handleManageExtensions = () => {
    const win: any = window.open(`${BASE_APP_PATH}/platform-landing/manage-extensions/${selectedApp}`, '_blank');
    win.onload = function onload() {
      win.document.title = 'Nexus';
    };
  };

  // Maps the solution type to the document type (tag).
  // TODO: We need to find a better way to do this.
  // The create document component is limited to provide only solution type, instead of solution type and document type (tag).
  // This can be removed after extending create document component to provide selected document type (tag) as well.
  const getTagsFromDocumentSolution = (documentSolution: string) => {
    switch (documentSolution) {
      case additiveManufacturingProjectTypeInfo.value:
        return additiveManufacturingProjectTypeInfo.tags;

      case smartAssemblyProjectTypeInfo.value:
        return smartAssemblyProjectTypeInfo.tags;

      case plasticProjectTypeInfo.value:
        return plasticProjectTypeInfo.tags;

      case metrologyProjectTypeInfo.value:
        return metrologyProjectTypeInfo.tags;

      case adamsCarProjectTypeInfo.value:
        return adamsCarProjectTypeInfo.tags;

      case computeProjectTypeInfo.value:
        return computeProjectTypeInfo.tags;

      case productDigitalTwinProjectTypeInfo.value:
        return productDigitalTwinProjectTypeInfo.tags;

      // commented as this typeInfo  was commented in the DataURL.utils.ts
      // added here to make it easier to add back in the future if needed
      // case materialsEnrichProjectType.value:
      //   return materialsEnrichProjectType.tags;

      default:
        return undefined;
    }
  };

  return (
    <>
      <>
        {isProductionSoftwareApplication(selectedApp) && (
          <Box
            sx={{
              flex: '1 1 0px',
              mx: 'auto',
              mt: 2,
              width: 'calc(100% - 32px)'
            }}
          >
            {platformManageExtensionsLinkButtonVisible && sdcPrivileges.includes('manage:extensions') && (
              <Link onClick={handleManageExtensions} color="primary" sx={{ display: 'flex', justifyContent: 'flex-end' }}>
                {t('manageExtensions')}
              </Link>
            )}
            <Extensions />
          </Box>
        )}
        {!isMobile && (
          <Drawer
            anchor={'left'}
            variant={'permanent'}
            PaperProps={{
              ref: sidePanelRef,
              sx: {
                width: isProductionSoftwareApplication(selectedApp) ? 0 : 256,
                top: { xs: 56, sm: 64 },
                height: { xs: `calc(100vh - 56px)`, sm: `calc(100vh - 64px)` },
                zIndex: (t) => t.zIndex.appBar - 1
              }
            }}
          >
            <List>
              <ListItemButton
                onClick={onClickProjects}
                sx={{ backgroundColor: isProject ? (theme) => `rgba(${theme.vars.palette.primary.mainChannel} / 0.08)` : 'transparent' }}
                data-testid="projects"
              >
                <FolderOpenOutlinedIcon sx={{ mr: 2, color: '#747577' }} />
                <ListItemText primary={t('projects')} primaryTypographyProps={{ sx: { fontSize: 14 } }} />
              </ListItemButton>
              <ListItemButton
                onClick={onClickDocuments}
                sx={{ backgroundColor: isDocument ? (theme) => `rgba(${theme.vars.palette.primary.mainChannel} / 0.08)` : 'transparent' }}
                data-testid="allDocuments"
              >
                <StickyNote2OutlinedIcon sx={{ mr: 2, color: '#747577' }} />
                <ListItemText primary={t('allDocuments')} primaryTypographyProps={{ sx: { fontSize: 14 } }} />
              </ListItemButton>
              <ListItemButton
                onClick={onClickDraft}
                sx={{ backgroundColor: isDraft ? (theme) => `rgba(${theme.vars.palette.primary.mainChannel} / 0.08)` : 'transparent' }}
                data-testid="drafts"
              >
                <EditNoteOutlinedIcon sx={{ mr: 2, color: '#747577' }} />
                <ListItemText primary={t('drafts')} primaryTypographyProps={{ sx: { fontSize: 14 } }} />
              </ListItemButton>
            </List>
            <Divider />
            <List
              subheader={
                <ListSubheader component={Typography} variant={'body2'} sx={{ fontWeight: 700, maxHeight: 40, backgroundColor: 'background.paper' }} data-testid="listOfProjects">
                  {t('projects')}
                </ListSubheader>
              }
              sx={{ overflowY: 'auto' }}
            >
              {foldersList.length == 0 ? (
                <Typography sx={{ color: 'grey.600', display: 'flex', paddingLeft: '15px' }} variant="caption">
                  {' '}
                  {t('noProjects')}{' '}
                </Typography>
              ) : (
                foldersList.map((folder) => (
                  <ListItemButton
                    key={folder?.id}
                    onClick={() => {
                      setHeaderTitle(folder?.name);
                      onClickProjectCard(folder?.id, folder?.name);
                    }}
                    sx={{ backgroundColor: clickedProjectId === folder?.id ? (theme) => `rgba(${theme.vars.palette.primary.mainChannel} / 0.08)` : 'transparent' }}
                    data-testid="projectNameLeftPanel"
                  >
                    <ListItemText primary={folder.name} primaryTypographyProps={{ noWrap: true, sx: { fontSize: 14 } }} />
                  </ListItemButton>
                ))
              )}
            </List>
          </Drawer>
        )}
        <Box sx={{ ml: isMobile ? 0 : isProductionSoftwareApplication(selectedApp) ? 0 : '256px', flexGrow: 3 }}>
          <Stack data-testid="projects-container">
            {!isProductionSoftwareApplication(selectedApp) && isProject && !hasError && (
              <Box sx={{ mx: 6, mt: 6 }}>
                <Stack spacing={3} direction="row" sx={{ display: 'flex', alignItems: 'center', height: '100%', marginBottom: '5px' }}>
                  {isMobile && <MenuIcon onClick={onClickMenu} />}
                  {!isMobile && (
                    <Box sx={{ display: 'flex', flexDirection: 'column' }}>
                      <Typography variant="h5">{headerTitle}</Typography>
                      <Typography variant="caption">{t('projectDescription')}</Typography>
                    </Box>
                  )}
                  {isMobile && <Typography variant="h5">{headerTitle}</Typography>}
                  <IconButton sx={{ position: 'absolute', right: '25px' }} onClick={onClickInfoIcon}>
                    {isMobile && !infoIconProject && <InfoOutlinedIcon color={'disabled'} />}
                    {isMobile && infoIconProject && <InfoIcon color={'primary'} />}
                  </IconButton>
                  {!isMobile && (
                    <Box sx={{ justifyContent: 'flex-end', flexGrow: 1, display: 'flex' }}>
                      <Box>
                        <Button variant="outlined" color="primary" onClick={onCreateProject} data-testid="create-project-btn" disabled={isProjectCreateDisabled}>
                          <FolderOpenOutlinedIcon sx={{ mr: 2 }} />
                          <Typography fontFamily={'Open Sans'} fontSize={'15px'} fontWeight={'700'}>
                            {t('createProjects')}
                          </Typography>
                        </Button>
                      </Box>
                    </Box>
                  )}
                </Stack>
                {isMobile && infoIconProject && <Typography variant="caption">{t('projectDescription')}</Typography>}
                <Stack direction={'row'} justifyContent={'space-between'} flexWrap={'wrap'} sx={{ marginTop: '30px' }} alignItems={'center'}>
                  <Box sx={{ flex: '1 1 0px', mr: isMobile ? 0 : 2, maxWidth: isMobile ? '100%' : '550px' }}>
                    <HexSearch
                      showListView={showListView}
                      items={hexDocuments}
                      checkSearchValue={checkSearchValue}
                      changeFilterInformation={changeFilterInformation}
                      searchValue={searchFolderName}
                      placeholder={t('searchProject')}
                      myOptions={hexDocuments && hexDocuments.length > 0 ? hexDocuments.slice(0, 5).map((obj: any) => obj.project) : []}
                    ></HexSearch>
                  </Box>
                  {/* Desktop Filters */}
                  {!isMobile && (
                    <Stack direction={'row'} alignItems={'center'}>
                      {showProjectFilter.current && (
                        <HexFilter
                          onFilterChange={onFilterChange}
                          groupedData={ProjectGroupData}
                          defaultFilterValues={selectedTags ? [...selectedTags.split(',')] : ProjectGroupData[0].data.map((data) => data.name)}
                          isProject={isProject}
                        />
                      )}
                      <HexInputSelect {...ShareAccessFilter} multiple={false} onChange={handleAccessProjectFilter} defaultValue={defaultAccessType} />
                      <IconButton color={'inherit'} onClick={onClickSort} sx={{ ml: 2 }} ref={sortMenuAnchorEl} disabled={projectCount === 0}>
                        <SortIcon />
                      </IconButton>
                    </Stack>
                  )}
                  {/* Filtering Menu used on both mobile  */}
                  {showMobileSelect && (
                    <HexInputSelectMobile
                      {...ShareAccessFilter}
                      showMobileSelect={showMobileSelect}
                      multiple={false}
                      onChange={handleAccessProjectFilter}
                      defaultValue={sessionStorage.getItem('ProjectAccessType') ?? defaultAccessType}
                      onDialogClosed={() => setShowMobileSelect(false)}
                      anchorElement={accessTypeAnchorRef.current}
                    />
                  )}
                  <HexInputSelectMobile
                    {...SortFilter}
                    showMobileSelect={showSortFilter}
                    multiple={false}
                    onChange={handleFolderSortMenuClick}
                    defaultValue={SortEnum.createDate}
                    onDialogClosed={() => setShowSortFilter(false)}
                    anchorElement={sortMenuAnchorEl.current}
                  />
                </Stack>
                <Stack>
                  {isMobile && (
                    <Box sx={{ justifyContent: 'center', display: 'flex', margin: '10px 0px 10px 0px' }}>
                      <Box sx={{ width: '100%' }}>
                        <Button
                          variant="outlined"
                          color="primary"
                          sx={{ width: '100%', height: '40px' }}
                          onClick={onCreateProject}
                          data-testid="create-project-btn"
                          disabled={isProjectCreateDisabled}
                        >
                          <FolderOpenOutlinedIcon sx={{ mr: 2 }} />
                          <Typography fontFamily={'Open Sans'} fontSize={'15px'} fontWeight={'700'}>
                            {t('createProjects')}
                          </Typography>
                        </Button>
                      </Box>
                    </Box>
                  )}
                </Stack>
                {isMobile && (
                  <Stack sx={{ display: 'flex', flexDirection: 'row', marginBottom: '10px', gap: '5px' }}>
                    <IconButton size={'small'} onClick={mobileSelect} ref={accessTypeAnchorRef}>
                      <PeopleAltOutlinedIcon color={'disabled'} />
                    </IconButton>
                    <IconButton size={'small'} onClick={onClickSort} ref={sortMenuAnchorEl}>
                      <SortIcon color={'disabled'} />
                    </IconButton>
                  </Stack>
                )}
                <Stack>
                  {searchFolderName && foldersList?.length === 0 && isLoader == false ? (
                    <HexEmptyFolder folderName={searchFolderName} foldersList={foldersList} containerHeight={containerHeight} />
                  ) : (
                    foldersList?.length === 0 && isLoader == false && <HexNoProjects containerHeight={containerHeight} onCreateProject={onCreateProject} />
                  )}
                  {isLoader && <InformationWithProgressBackDrop open={isLoader} message={t('ProjectLoading')} description={t('LoadingProjectsList')} />}
                  {isProjectLoader && <InformationWithProgressBackDrop open={isProjectLoader} message={t('CreatingProjects')} />}
                </Stack>
              </Box>
            )}
            {isDocument && !hasError && (
              <>
                <Box sx={{ mx: 6, mt: 6 }}>
                  <Stack spacing={3} direction="row" sx={{ display: 'flex', alignItems: 'center', height: '100%', marginBottom: '30px' }}>
                    {isMobile && !isProductionSoftwareApplication(selectedApp) && <MenuIcon onClick={onClickMenu} />}

                    {!isMobile && (
                      <Box sx={{ display: 'flex', flexDirection: 'column' }}>
                        <Typography variant="h5">{headerTitle}</Typography>
                        <Typography variant="caption">{t('documentDescription')}</Typography>
                      </Box>
                    )}
                    {isMobile && <Typography variant="h5">{headerTitle}</Typography>}
                    <IconButton sx={{ position: 'absolute', right: '25px' }} onClick={onClickInfoDocument}>
                      {isMobile && !infoIconDocument && <InfoOutlinedIcon color={'disabled'} />}
                      {isMobile && infoIconDocument && <InfoIcon color={'primary'} />}
                    </IconButton>
                    {!isMobile && !isDisabled && (
                      <Box sx={{ justifyContent: 'flex-end', flexGrow: 1, display: 'flex' }}>
                        <Box>
                          <Button
                            variant="contained"
                            color="secondary"
                            onClick={() => {
                              handleCreateDocumentClick(DocumentType.allDocuments);
                            }}
                            data-testid="add-projects-card"
                          >
                            <StickyNote2OutlinedIcon sx={{ mr: 2 }} />
                            <Typography fontFamily={'Open Sans'} fontSize={'15px'} fontWeight={'700'}>
                              {t('createProject')}
                            </Typography>
                          </Button>
                        </Box>
                      </Box>
                    )}
                  </Stack>
                  {isMobile && infoIconDocument && <Typography variant="caption">{t('documentDescription')}</Typography>}
                  <Stack direction={'row'} justifyContent={'space-between'} flexWrap={'wrap'} alignItems={'center'} marginTop={'30px'}>
                    <Box sx={{ flex: '1 1 0px', mr: isMobile ? 0 : 2, maxWidth: isMobile ? '100%' : '550px' }}>
                      <HexSearch
                        showListView={showListView}
                        items={hexDocuments}
                        checkSearchValue={checkSearchValue}
                        changeFilterInformation={changeFilterInformation}
                        searchValue={searchProjectName}
                        placeholder={t('searchProject')}
                        myOptions={hexDocuments && hexDocuments.length > 0 ? hexDocuments.slice(0, 5).map((obj: any) => obj.project) : []}
                      ></HexSearch>
                    </Box>
                    {/* Desktop Filters */}
                    {!isMobile && (
                      <Stack direction={'row'} alignItems={'center'}>
                        {showProjectFilter.current && (
                          <HexFilter
                            onFilterChange={onFilterChange}
                            groupedData={ProjectGroupData}
                            defaultFilterValues={selectedTags ? [...selectedTags.split(',')] : ProjectGroupData[0].data.map((data) => data.name)}
                          />
                        )}
                        <HexInputSelect {...ShareAccessFilter} multiple={false} onChange={handleShareAccessChange} defaultValue={defaultAccessType} />
                        <IconButton color={'inherit'} onClick={onClickSort} sx={{ ml: 2 }} ref={sortMenuAnchorEl} disabled={projectCount === 0}>
                          <SortIcon />
                        </IconButton>
                      </Stack>
                    )}
                    {/* Shared Sort Menu used on both mobile and desktop */}
                    <HexInputSelectMobile
                      {...SortFilterDocumentFlagged.current}
                      showMobileSelect={showSortFilter}
                      multiple={false}
                      onChange={handleSortMenuClick}
                      defaultValue={SortEnum.createDate}
                      onDialogClosed={() => setShowSortFilter(false)}
                      anchorElement={sortMenuAnchorEl.current}
                    />
                  </Stack>
                  <Stack>
                    {isMobile && !isDisabled && (
                      <Box sx={{ justifyContent: 'center', flexGrow: 1, display: 'flex', margin: '10px 0px 10px 0px' }}>
                        <Box sx={{ width: '100%' }}>
                          <Button
                            variant="contained"
                            color="secondary"
                            onClick={() => {
                              handleCreateDocumentClick(DocumentType.allDocuments);
                            }}
                            sx={{ width: '100%', height: '40px' }}
                            data-testid="add-projects-card"
                          >
                            <StickyNote2OutlinedIcon sx={{ mr: 2 }} />
                            <Typography fontFamily={'Open Sans'} fontSize={'15px'} fontWeight={'700'}>
                              {t('createProject')}
                            </Typography>
                          </Button>
                        </Box>
                      </Box>
                    )}
                  </Stack>
                  <Stack>
                    {isMobile && (
                      <>
                        <Box sx={{ flex: 1 }} />
                        <Stack direction={'row'} sx={{ color: 'text.secondary' }}>
                          {showProjectFilter.current && (
                            <IconButton ref={filterAnchorRef} size={'small'} onClick={mobileFilter} disabled={projectCount === 0}>
                              <FilterAltOutlinedIcon color={'disabled'} />
                            </IconButton>
                          )}
                          <IconButton size={'small'} onClick={mobileSelect} ref={accessTypeAnchorRef} disabled={projectCount === 0}>
                            <PeopleAltOutlinedIcon color={'disabled'} />
                          </IconButton>
                          <IconButton size={'small'} onClick={onClickSort} ref={sortMenuAnchorEl} disabled={projectCount === 0}>
                            <SortIcon color={'disabled'} />
                          </IconButton>
                        </Stack>
                        {showFilterMobile && (
                          <HexFilterMobile
                            anchorElement={filterAnchorRef.current}
                            onFilterChange={onFilterChange}
                            showFilterMobile={showFilterMobile}
                            groupedData={[...ProjectGroupData]}
                            onDialogClosed={() => setShowFilterMobile(false)}
                            defaultFilterValues={selectedTags ? [...(selectedTags || '').split(',')] : ProjectGroupData[0].data.map((data) => data.name)}
                          />
                        )}
                        {showMobileSelect && (
                          <HexInputSelectMobile
                            {...ShareAccessFilter}
                            showMobileSelect={showMobileSelect}
                            multiple={false}
                            onChange={handleShareAccessChange}
                            defaultValue={sessionStorage.getItem('accessType') ?? defaultAccessType}
                            onDialogClosed={() => setShowMobileSelect(false)}
                            anchorElement={accessTypeAnchorRef.current}
                          />
                        )}
                      </>
                    )}
                  </Stack>
                </Box>
              </>
            )}
            {!isProductionSoftwareApplication(selectedApp) && !hasError && isDraft && (
              <Box sx={{ mx: 6, mt: 6 }}>
                <Stack spacing={3} direction="row" sx={{ display: 'flex', alignItems: 'center', height: '100%', marginBottom: '5px' }}>
                  {isMobile && <MenuIcon onClick={onClickMenu} />}
                  {!isMobile && (
                    <Box sx={{ display: 'flex', flexDirection: 'column' }}>
                      <Typography variant="h5" data-testid={'allDocumentsTitle'}>
                        {headerTitle}
                      </Typography>
                      <Typography variant="caption">{t('draftDescription')}</Typography>
                    </Box>
                  )}
                  {isMobile && (
                    <Typography variant="h5" data-testid={'allDocumentsTitle'}>
                      {headerTitle}
                    </Typography>
                  )}
                  <IconButton sx={{ position: 'absolute', right: '25px' }} onClick={onClickInfoDraft}>
                    {isMobile && !infoIconDraft && <InfoOutlinedIcon color={'disabled'} />}
                    {isMobile && infoIconDraft && <InfoIcon color={'primary'} />}
                  </IconButton>
                  {!showListView && !isDisabled && (
                    <Box sx={{ justifyContent: 'flex-end', flexGrow: 1, display: 'flex' }}>
                      <Box>
                        <Button
                          variant="contained"
                          color="secondary"
                          onClick={() => {
                            handleCreateDocumentClick(DocumentType.drafts);
                          }}
                          data-testid="add-projects-card"
                        >
                          <StickyNote2OutlinedIcon sx={{ mr: 2 }} />
                          <Typography fontFamily={'Open Sans'} fontSize={'15px'} fontWeight={'700'}>
                            {t('createProject')}
                          </Typography>
                        </Button>
                      </Box>
                    </Box>
                  )}
                </Stack>
                {isMobile && infoIconDraft && <Typography variant="caption">{t('draftDescription')}</Typography>}
                <Stack direction={'row'} justifyContent={'space-between'} flexWrap={'wrap'} alignItems={'center'} marginTop={'30px'}>
                  <Box sx={{ flex: '1 1 0px', mr: isMobile ? 0 : 2, maxWidth: isMobile ? '100%' : '550px' }}>
                    <HexSearch
                      showListView={showListView}
                      items={hexDocuments}
                      checkSearchValue={checkSearchValue}
                      changeFilterInformation={changeFilterInformation}
                      searchValue={searchProjectName}
                      placeholder={t('searchProject')}
                      myOptions={hexDocuments && hexDocuments.length > 0 ? hexDocuments.slice(0, 5).map((obj: any) => obj.project) : []}
                    ></HexSearch>
                  </Box>

                  {/* Desktop Filters */}
                  {!isMobile && (
                    <Stack direction={'row'} alignItems={'center'}>
                      {showProjectFilter.current && (
                        <HexFilter
                          onFilterChange={onFilterChange}
                          groupedData={ProjectGroupData}
                          defaultFilterValues={selectedTags ? [...selectedTags.split(',')] : ProjectGroupData[0].data.map((data) => data.name)}
                        />
                      )}
                      <HexInputSelect {...ShareAccessFilter} multiple={false} onChange={handleShareAccessChange} defaultValue={defaultAccessType} />
                      <IconButton color={'inherit'} onClick={onClickSort} sx={{ ml: 2 }} ref={sortMenuAnchorEl} disabled={projectCount === 0}>
                        <SortIcon />
                      </IconButton>
                    </Stack>
                  )}
                  {/* Shared Sort Menu used on both mobile and desktop */}
                  <HexInputSelectMobile
                    {...SortFilterDocumentFlagged.current}
                    showMobileSelect={showSortFilter}
                    multiple={false}
                    onChange={handleSortMenuClick}
                    defaultValue={SortEnum.createDate}
                    onDialogClosed={() => setShowSortFilter(false)}
                    anchorElement={sortMenuAnchorEl.current}
                  />
                </Stack>
                <Stack>
                  {isMobile && !isDisabled && (
                    <Box sx={{ justifyContent: 'center', flexGrow: 1, display: 'flex', margin: '10px 0px 10px 0px' }}>
                      <Box sx={{ width: '100%' }}>
                        <Button
                          variant="contained"
                          color="secondary"
                          onClick={() => {
                            handleCreateDocumentClick(DocumentType.drafts);
                          }}
                          sx={{ width: '100%', height: '40px' }}
                          data-testid="add-projects-card"
                        >
                          <StickyNote2OutlinedIcon sx={{ mr: 2 }} />
                          <Typography fontFamily={'Open Sans'} fontSize={'15px'} fontWeight={'700'}>
                            {t('createProject')}
                          </Typography>
                        </Button>
                      </Box>
                    </Box>
                  )}
                </Stack>
                <Stack>
                  {isMobile && (
                    <>
                      <Box sx={{ flex: 1 }} />
                      <Stack direction={'row'} sx={{ color: 'text.secondary' }}>
                        {showProjectFilter.current && (
                          <IconButton ref={filterAnchorRef} size={'small'} onClick={mobileFilter} disabled={projectCount === 0}>
                            <FilterAltOutlinedIcon color={'disabled'} />
                          </IconButton>
                        )}
                        <IconButton size={'small'} onClick={mobileSelect} ref={accessTypeAnchorRef} disabled={projectCount === 0}>
                          <PeopleAltOutlinedIcon color={'disabled'} />
                        </IconButton>
                        <IconButton size={'small'} onClick={onClickSort} ref={sortMenuAnchorEl} disabled={projectCount === 0}>
                          <SortIcon color={'disabled'} />
                        </IconButton>
                      </Stack>
                      {showFilterMobile && (
                        <HexFilterMobile
                          anchorElement={filterAnchorRef.current}
                          onFilterChange={onFilterChange}
                          showFilterMobile={showFilterMobile}
                          groupedData={[...ProjectGroupData]}
                          onDialogClosed={() => setShowFilterMobile(false)}
                          defaultFilterValues={selectedTags ? [...(selectedTags || '').split(',')] : ProjectGroupData[0].data.map((data) => data.name)}
                        />
                      )}
                      {showMobileSelect && (
                        <HexInputSelectMobile
                          {...ShareAccessFilter}
                          showMobileSelect={showMobileSelect}
                          multiple={false}
                          onChange={handleShareAccessChange}
                          defaultValue={sessionStorage.getItem('accessType') ?? defaultAccessType}
                          onDialogClosed={() => setShowMobileSelect(false)}
                          anchorElement={accessTypeAnchorRef.current}
                        />
                      )}
                    </>
                  )}
                </Stack>
              </Box>
            )}
            {!hasError && isProjectCardClicked && (
              <Box sx={{ mx: 6, mt: 6 }}>
                <Stack spacing={3} direction="row" sx={{ display: 'flex', alignItems: 'center', height: '100%', marginBottom: '30px' }}>
                  {isMobile && <MenuIcon onClick={onClickMenu} />}
                  <Typography variant="h5" data-testid={'allDocumentsTitle'}>
                    {headerTitle}
                  </Typography>
                  {sdcPrivileges.includes('share:projectinstance') && (
                    <Button color={'primary'} variant={'text'} onClick={() => handleShareCurrentFolder(clickedProjectId!)} startIcon={<GroupOutlined />} data-testid="shareBtn">
                      {t('share')}
                    </Button>
                  )}
                  {/* <Button
                    onClick={() => {
                      console.log('More Option');
                    }}
                    data-testid="moreOptionBtn"
                  >
                    <MoreHorizIcon />
                  </Button> */}
                  {!showListView && !isDisabled && (
                    <Box sx={{ justifyContent: 'flex-end', flexGrow: 1, display: 'flex' }}>
                      <Box>
                        <Button
                          variant="contained"
                          color="secondary"
                          onClick={() => {
                            handleCreateDocumentClick(DocumentType.projectDocuments);
                          }}
                          data-testid="add-projects-card"
                        >
                          <StickyNote2OutlinedIcon sx={{ mr: 2 }} />
                          <Typography fontFamily={'Open Sans'} fontSize={'15px'} fontWeight={'700'}>
                            {t('createProject')}
                          </Typography>
                        </Button>
                      </Box>
                    </Box>
                  )}
                </Stack>

                <Stack direction={'row'} justifyContent={'space-between'} flexWrap={'wrap'} alignItems={'center'}>
                  <Box sx={{ flex: '1 1 0px', mr: isMobile ? 0 : 2, maxWidth: isMobile ? '100%' : '550px' }}>
                    <HexSearch
                      showListView={showListView}
                      items={hexDocuments}
                      checkSearchValue={checkSearchValue}
                      changeFilterInformation={changeFilterInformation}
                      searchValue={searchProjectName}
                      placeholder={t('searchProject')}
                      myOptions={hexDocuments && hexDocuments.length > 0 ? hexDocuments.slice(0, 5).map((obj: any) => obj.project) : []}
                    ></HexSearch>
                  </Box>

                  {/* Desktop Filters */}
                  {!isMobile && (
                    <Stack direction={'row'} alignItems={'center'}>
                      {showProjectFilter.current && (
                        <HexFilter
                          onFilterChange={onFilterChange}
                          groupedData={ProjectGroupData}
                          defaultFilterValues={selectedTags ? [...selectedTags.split(',')] : ProjectGroupData[0].data.map((data) => data.name)}
                        />
                      )}
                      <HexInputSelect {...ShareAccessFilter} multiple={false} onChange={handleShareAccessChange} defaultValue={defaultAccessType} />
                      <IconButton color={'inherit'} onClick={onClickSort} sx={{ ml: 2 }} ref={sortMenuAnchorEl} disabled={projectCount === 0}>
                        <SortIcon />
                      </IconButton>
                    </Stack>
                  )}
                  {/* Shared Sort Menu used on both mobile and desktop */}
                  <HexInputSelectMobile
                    {...SortFilterDocumentFlagged.current}
                    showMobileSelect={showSortFilter}
                    multiple={false}
                    onChange={handleSortMenuClick}
                    defaultValue={SortEnum.createDate}
                    onDialogClosed={() => setShowSortFilter(false)}
                    anchorElement={sortMenuAnchorEl.current}
                  />
                </Stack>
                <Stack>
                  {isMobile && !isDisabled && (
                    <Box sx={{ justifyContent: 'center', flexGrow: 1, display: 'flex', margin: '10px 0px 10px 0px' }}>
                      <Box sx={{ width: '100%' }}>
                        <Button
                          variant="contained"
                          color="secondary"
                          onClick={() => {
                            handleCreateDocumentClick(DocumentType.projectDocuments);
                          }}
                          sx={{ width: '100%', height: '40px' }}
                          data-testid="add-projects-card"
                        >
                          <StickyNote2OutlinedIcon sx={{ mr: 2 }} />
                          <Typography fontFamily={'Open Sans'} fontSize={'15px'} fontWeight={'700'}>
                            {t('createProject')}
                          </Typography>
                        </Button>
                      </Box>
                    </Box>
                  )}
                </Stack>
                <Stack>
                  {isMobile && (
                    <>
                      <Box sx={{ flex: 1 }} />
                      <Stack direction={'row'} sx={{ color: 'text.secondary' }}>
                        {showProjectFilter.current && (
                          <IconButton ref={filterAnchorRef} size={'small'} onClick={mobileFilter} disabled={projectCount === 0}>
                            <FilterAltOutlinedIcon color={'disabled'} />
                          </IconButton>
                        )}
                        <IconButton size={'small'} onClick={mobileSelect} ref={accessTypeAnchorRef} disabled={projectCount === 0}>
                          <PeopleAltOutlinedIcon color={'disabled'} />
                        </IconButton>
                        <IconButton size={'small'} onClick={onClickSort} ref={sortMenuAnchorEl} disabled={projectCount === 0}>
                          <SortIcon color={'disabled'} />
                        </IconButton>
                      </Stack>
                      {showFilterMobile && (
                        <HexFilterMobile
                          anchorElement={filterAnchorRef.current}
                          onFilterChange={onFilterChange}
                          showFilterMobile={showFilterMobile}
                          groupedData={[...ProjectGroupData]}
                          onDialogClosed={() => setShowFilterMobile(false)}
                          defaultFilterValues={selectedTags ? [...(selectedTags || '').split(',')] : ProjectGroupData[0].data.map((data) => data.name)}
                        />
                      )}
                      {showMobileSelect && (
                        <HexInputSelectMobile
                          {...ShareAccessFilter}
                          showMobileSelect={showMobileSelect}
                          multiple={false}
                          onChange={handleShareAccessChange}
                          defaultValue={sessionStorage.getItem('accessType') ?? defaultAccessType}
                          onDialogClosed={() => setShowMobileSelect(false)}
                          anchorElement={accessTypeAnchorRef.current}
                        />
                      )}
                    </>
                  )}
                </Stack>
              </Box>
            )}
            {!isProductionSoftwareApplication(selectedApp) && isDraft && (
              <Box className={`hex-project-list-view ${!showProjectFilter.current && isMobile ? 'no-filter' : ''}`} ref={projectsContainerRef}>
                <Box className="hex-flex-column hex-space-between">
                  {hasError && !isLoading && !showCreateDocument ? (
                    <HexErrorPageCard retryCallback={() => navigate(`/`)}></HexErrorPageCard>
                  ) : !isLoader && !isLoading && hexDocuments.length === 0 ? (
                    <HexEmptyProject projectName={searchProjectName} projects={hexDocuments} containerHeight={containerHeight} createDemoProject={handleCreateDemoProject} />
                  ) : (
                    // Drafts Component with projects view toggle on
                    <Box sx={{ marginLeft: '24px', marginRight: '15px', marginTop: '24px', marginBottom: '40px' }}>
                      <ConnectedDocumentView userDocuments={documents} isLoading={isLoader} isMobile={isMobile} clickProject={handleClickProject} projectsList={foldersList} />
                    </Box>
                  )}
                  {/* {isLoader && <HexLoader className="hex-loader"></HexLoader>} */}
                  {isLoader && <InformationWithProgressBackDrop open={isLoader} message={t('ProjectLoading')} description={t('ProjectLoadingDescription')} />}
                </Box>
              </Box>
            )}
            {isDocument && (
              <Box className={`hex-project-list-view ${!showProjectFilter.current && isMobile ? 'no-filter' : ''}`} ref={projectsContainerRef}>
                <Box className={'hex-flex-column hex-space-between'}>
                  {hasError && !isLoading && !showCreateDocument ? (
                    <HexErrorPageCard retryCallback={() => navigate(`/`)}></HexErrorPageCard>
                  ) : !isLoader && !isLoading && hexDocuments.length === 0 ? (
                    <HexEmptyProject projectName={searchProjectName} projects={hexDocuments} containerHeight={containerHeight} createDemoProject={handleCreateDemoProject} />
                  ) : (
                    // Documents Component with projects view toggle on
                    <Box sx={{ marginLeft: '24px', marginRight: '15px', marginTop: '24px', marginBottom: '40px' }}>
                      <ConnectedDocumentView userDocuments={documents} isLoading={isLoader} isMobile={isMobile} clickProject={handleClickProject} projectsList={foldersList} />
                    </Box>
                  )}
                  {isLoader && <InformationWithProgressBackDrop open={isLoader} message={t('ProjectLoading')} description={t('ProjectLoadingDescription')} />}
                </Box>
              </Box>
            )}
            {!isProductionSoftwareApplication(selectedApp) && isProject && (
              <Box className={`hex-project-list-view ${!showProjectFilter.current && isMobile ? 'no-filter' : ''}`} ref={projectsContainerRef}>
                <Box className={'hex-flex-column hex-space-between'}>
                  {hasError && !isLoading ? (
                    <HexErrorPageCard retryCallback={() => navigate(`/`)}></HexErrorPageCard>
                  ) : (
                    !isLoading && (
                      <Stack>
                        {foldersList?.length !== 0 && (
                          <FoldersComponent
                            foldersList={foldersList}
                            onClickProjectCard={onClickProjectCard}
                            handleRenameFolder={handleRenameFolder}
                            containerHeight={containerHeight}
                            containerWidth={getContainerWidth()}
                            showListView={showListView}
                            addEnabled={isPointSolution(selectedApp)}
                            hexAuthToken={hexAuthToken.current}
                            isProjectCardClicked={isProjectCardClicked}
                            handleDeleteProjectFolder={handleDeleteProjectFolder}
                            handleUnfollowProjectFolder={handleUnfollowProjectFolder}
                          />
                        )}
                      </Stack>
                    )
                  )}
                  {isLoader && <InformationWithProgressBackDrop open={isLoader} message={t('ProjectLoading')} description={t('LoadingProjectsList')} />}
                  {isProjectLoader && <InformationWithProgressBackDrop open={isProjectLoader} message={t('CreatingProjects')} />}
                </Box>
              </Box>
            )}

            <Box className={`hex-project-list-view ${!showProjectFilter.current && isMobile ? 'no-filter' : ''}`} ref={projectsContainerRef}>
              {isProjectCardClicked && (
                <Box className="hex-flex-column hex-space-between">
                  {hasError && !isLoading && !showCreateDocument ? (
                    <HexErrorPageCard retryCallback={() => navigate(`/project`)}></HexErrorPageCard>
                  ) : (
                    !isLoading && (
                      //  Documents inside the project folders component with the projects view toggle on
                      <Box sx={{ marginLeft: '24px', marginRight: '24px', marginTop: '24px', marginBottom: '40px' }}>
                        <ConnectedDocumentView userDocuments={documents} isLoading={isLoader} isMobile={isMobile} clickProject={handleClickProject} projectsList={foldersList} />
                      </Box>
                    )
                  )}
                  {/* {isLoader && <HexLoader className="hex-loader"></HexLoader>} */}
                  {isLoader && <InformationWithProgressBackDrop open={isLoader} message={t('ProjectLoading')} description={t('ProjectLoadingDescription')} />}
                </Box>
              )}
            </Box>
          </Stack>
        </Box>
      </>
      {!isLoading && showCreateDocument && (
        // TODO : should extend create document component to provide selected document type (tag) as well,
        // instead of using solution / document tag mapper.
        <CreateDocument
          open={showCreateDocument}
          initialDocumentName={t('projectName')}
          projectOptions={projectOptions}
          solutionOptions={solutionOptions}
          initialProject={isDraft ? '0' : clickedProjectId ?? ''}
          disableProject={!!clickedProjectId}
          onCreate={(data: IDocumentInfo) => {
            const projectId = parseInt(data.project) !== 0 ? projectOptions.find((ele) => ele.value === data.project)?.value ?? '' : '';
            const description = solutionOptions.find((ele: OptionType) => ele.value === data.solution)?.label?.toLocaleString() ?? '';

            // Maps the solution type to the document type (tag).
            // TODO: We need to find a better way to do this.
            // The create document component is limited to provide only solution type, instead of solution type and document type (tag).
            // This can be removed after extending create document component to provide selected document type (tag) as well.
            const tags = getTagsFromDocumentSolution(data.solution);

            if (!tags || tags.length === 0) {
              console.error('No tags found for the selected solution');
            }

            // Selected document is now based on the selected filters on create document and it is NOT  based on the current app tag only.
            // This is to support the multiple solution type. eg. apex now can create document of two types (additiveManufacturing and smart-assembly).
            // if app tag is only presented then default app tag projects will be displayed. eg. apex will display additiveManufacturing projects.
            // if app tag and selected tags are presented then selected tags projects will be displayed. eg. apex and additiveManufacturing will display additiveManufacturing , smart assembly documents.
            const project: IProject = { project: data.name, projectId, description, schemaTypeId: '', tags };

            isDemoProject ? createDemoDocument({ name: data.name, project: data.project, solution: data.solution } as IDocumentInfo) : createNewDocument(project);
            closeCreateProject();
          }}
          onCancel={closeCreateProject}
        />
      )}
      {!isLoading && showCreateDemoDocument && (
        <CreateDocument
          open={showCreateDemoDocument}
          initialDocumentName={t('projectName')}
          projectOptions={projectOptions}
          solutionOptions={solutionOptions}
          onCreate={(data: IDocumentInfo) => {
            createDemoDocument(data);
            closeCreateProject();
          }}
          onCancel={closeCreateProject}
        />
      )}
      {showSavingDialog && <InformationWithProgressBackDrop open={showSavingDialog} message={t('creatingProject')} />}
      {!showUnfollowProgress && (
        <HexUnfollowDialog showUnfollowDialog={showUnfollowDialog} handleClose={handleClose} handleUnfollow={handleRemoveUser} project={selectedProject} isProjectOrSDC={'SDC'} />
      )}
      {showUnfollowProjectModal && (
        <HexUnfollowDialog
          showUnfollowDialog={showUnfollowProjectModal}
          handleClose={handleCloseProject}
          handleUnfollow={handleRemoveUser}
          project={selectedFolder}
          isProjectOrSDC={'Project'}
        />
      )}

      {showDeleteProjectModal && (
        <HexDeleteDialog showDeleteDialog={showDeleteProjectModal} handleClose={handleCloseProject} handleDelete={deleteProjectInstance} project={selectedFolderRef.current} />
      )}

      {showShareDialog && (
        <ShareDialogComponent
          show={showShareDialog}
          authToken={hexAuthToken.current}
          onClose={() => setShowShareDialog(false)}
          instanceId={selectedFolder?.id as string}
          isProjectOrSDC={'Project'}
        />
      )}

      {isMobile && !isProductionSoftwareApplication(selectedApp) && (
        <MobileMenu
          showDialog={isMenu}
          handleClose={handleMenuClose}
          setIsProject={setIsProject}
          setIsDocument={setIsDocument}
          setIsDrafts={setIsDraft}
          setIsMenu={setIsMenu}
          setIsProjectCardClicked={setIsProjectCardClicked}
          setHeaderTitle={setHeaderTitle}
          folders={foldersList}
          onClickProjectCard={onClickProjectCard}
          handleNavigate={handleNavigate}
        />
      )}
      <HexRenameFolder
        show={showFolderRenamePopup}
        instanceData={{ name: selectedFolderRef.current?.name }}
        onUpdate={updateFolderInstance}
        onClose={() => {
          setShowFolderRenamePopup(false);
        }}
      />
      <CreateProjectDialog
        showUnfollowDialog={isCreateProject}
        handleClose={onCancleCreate}
        handleCreate={onClickCreate}
        setProjectName={setProjectName}
        projectName={projectName}
      />
    </>
  );
}
export default ProjectDocumentView;
