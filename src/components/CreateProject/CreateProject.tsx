import React from 'react';
import { Button, Dialog, DialogActions, DialogTitle, DialogContent, Divider, TextField } from '@mui/material';
import { useTranslation } from 'react-i18next';
export interface ICreateProjectProps {
  showUnfollowDialog: boolean;
  handleClose: () => void;
  handleCreate: () => void;
  setProjectName: any;
  projectName: string;
}
export const CreateProjectDialog = ({ showUnfollowDialog, handleClose, handleCreate, setProjectName, projectName }: ICreateProjectProps) => {
  const { t } = useTranslation();

  const onChangeValue = (e: any) => {
    setProjectName(e.target.value);
  };

  return (
    <Dialog
      data-testid="create-dialog"
      open={showUnfollowDialog}
      onClose={handleClose}
      PaperProps={{
        sx: {
          width: '60%',
          height: '230px'
        }
      }}
    >
      <DialogTitle>{t('createProjects')}</DialogTitle>
      <Divider />
      <DialogContent>
        <TextField label={t('projectNameField')} placeholder={t('projectNameField')} fullWidth onChange={onChangeValue} />
      </DialogContent>
      <Divider />
      <DialogActions sx={{ display: 'flex', justifyContent: 'flex-end', flexWrap: 'wrap' }}>
        <Button onClick={handleClose} color="primary" sx={{ m: 0 }}>
          {t('cancel')}
        </Button>
        <Button onClick={handleCreate} disabled={!projectName}>
          {t('create')}
        </Button>
      </DialogActions>
    </Dialog>
  );
};
