import { useEffect } from 'react';
import { wstStaticUrl, wstKey } from '../../configuration/Intento.config.ts';

const IntentoTranslation = () => {
  useEffect(() => {
    if (!document.getElementById('intento-translation')) {
      const loadIntentoScript = async () => {
        try {
          const response = await fetch(wstStaticUrl, {
            headers: { wstkey: wstKey },
            method: 'GET'
          });

          if (!response.ok) {
            throw new Error('Failed to read configuration');
          }
          const scriptContent = await response.blob();
          const objectURL = URL.createObjectURL(scriptContent);

          const intentoTag = document.createElement('script');
          intentoTag.setAttribute('src', objectURL);
          intentoTag.setAttribute('type', 'text/javascript');
          intentoTag.setAttribute('id', 'intento-translation');
          document.head.appendChild(intentoTag);
          console.log('Intento script loaded', intentoTag);
        } catch (error) {
          console.error('Error loading Intento script:', error);
        }
      };

      loadIntentoScript();
    }
  }, []);

  return null;
};

export default IntentoTranslation;
