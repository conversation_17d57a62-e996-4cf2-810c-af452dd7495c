import { render } from '@testing-library/react';
import React from 'react';
import { InformationWithProgressBackDrop } from './InformationWithProgressBackDrop';

describe('InformationWithProgressBackDrop', () => {
  it('should render message', () => {
    const component = render(<InformationWithProgressBackDrop open={true} message="Test" />);
    expect(component.getByText('Test')).toBeInTheDocument();
    expect(component.getByTestId('progress')).toBeInTheDocument();
  });
});
