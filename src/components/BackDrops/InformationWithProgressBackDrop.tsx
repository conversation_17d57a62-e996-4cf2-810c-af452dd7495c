import { Backdrop, CircularProgress, Typography, Box, Theme } from '@mui/material';
import React from 'react';

interface InformationWithProgressBackDropProps {
  message: string;
  open: boolean;
  description?: string;
}
/**
 * @param {InformationWithProgressBackDropProps} props - provides the properties fo React component
 * @return {ReactNode} - provides the react component to be ingested
 **/
export const InformationWithProgressBackDrop: React.FC<InformationWithProgressBackDropProps> = ({ message, open, description }: InformationWithProgressBackDropProps) => {
  return (
    <Backdrop open={open} sx={{ zIndex: (theme: Theme) => theme.zIndex.drawer + 1, padding: 2 }}>
      <Box sx={{ display: 'flex', flexDirection: 'column' }}>
        <Box sx={{ display: 'flex', justifyContent: 'center' }}>
          <CircularProgress sx={{ color: 'common.white' }} data-testid="progress"></CircularProgress>
        </Box>
        <Box sx={{ marginTop: '30px', color: 'common.white', display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center' }}>
          <Typography fontFamily={'Open Sans'} align="center" sx={{ color: 'common.white', mb: 2 }} variant="h5">
            {message}
          </Typography>
          {description && (
            <Typography fontFamily={'Open Sans'} align="center" sx={{ color: 'common.white' }} variant="subtitle1">
              {description}
            </Typography>
          )}
        </Box>
      </Box>
    </Backdrop>
  );
};
