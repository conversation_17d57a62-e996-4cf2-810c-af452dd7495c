import * as React from 'react';
import OutlinedInput from '@mui/material/OutlinedInput';
import MenuItem from '@mui/material/MenuItem';
import FormControl from '@mui/material/FormControl';
import ListItemText from '@mui/material/ListItemText';
import Select from '@mui/material/Select';
import Checkbox from '@mui/material/Checkbox';
import { t } from 'i18next';
import Box from '@mui/material/Box';
import DoneOutlinedIcon from '@mui/icons-material/DoneOutlined';
import { Typography } from '@mui/material';
import { useSelector } from 'react-redux';
import { RootState } from '../../../redux';

const ITEM_HEIGHT = 48;
const ITEM_PADDING_TOP = 8;
const MenuProps = {
  PaperProps: {
    style: {
      maxHeight: ITEM_HEIGHT * 4.7 + ITEM_PADDING_TOP,
      width: 250
    }
  }
};

interface optionInterface {
  name: string;
  value: string | number;
}
export interface HexInputSelectInterface {
  label?: string;
  placeholder?: string;
  multiple?: boolean;
  defaultValue: string;
  options: optionInterface[];
  onChange: (value: optionInterface) => void;
}

export const HexInputSelect = ({ label, placeholder, options, defaultValue, multiple, onChange }: HexInputSelectInterface) => {
  const [checked, setChecked] = React.useState('all');
  const projectCount = useSelector((state: RootState) => state.userProjectCount.projectCount);
  const handleChange = (event: any) => {
    onChange(event.target);
    setChecked(event.target.value);
  };

  return (
    <div>
      <FormControl sx={{ width: '100%' }}>
        <Select
          data-testid="accessType"
          multiple={multiple}
          sx={{
            boxShadow: 'none',
            '.MuiOutlinedInput-notchedOutline': { border: 0 },
            margin: 0,
            '.MuiSvgIcon-root ': {
              fill: projectCount === 0 ? '#D3D3D3 !important' : '#005072 !important'
            }
          }}
          onChange={handleChange}
          input={<OutlinedInput label={label ? t(label) : ''} />}
          MenuProps={MenuProps}
          displayEmpty={true}
          defaultValue={defaultValue}
          disabled={projectCount === 0}
          renderValue={(selected: any) => {
            // added this to take the label not the value
            const displayValue = options.find((option: any) => {
              return option.value === selected;
            });

            return (
              <Typography variant="body2" sx={{ color: 'primary.main', fontWeight: 700 }}>
                {selected ? (displayValue && displayValue.name ? t(displayValue.name) : '') : placeholder ? t(placeholder) : ''}
              </Typography>
            );
          }}
        >
          {options.map((opt) => (
            <MenuItem key={opt?.name} value={opt?.value}>
              {opt?.value !== checked && <Box sx={{ height: '42px', width: '42px' }}></Box>}
              {opt?.value === checked && <Checkbox icon={<></>} checkedIcon={<DoneOutlinedIcon sx={{ color: 'black' }} />} checked={opt?.value === checked} />}
              <ListItemText primary={t(opt?.name)} />
            </MenuItem>
          ))}
        </Select>
      </FormControl>
    </div>
  );
};
