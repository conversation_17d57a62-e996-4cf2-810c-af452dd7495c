import * as React from 'react';
import MenuItem from '@mui/material/MenuItem';
import ListItemText from '@mui/material/ListItemText';
import DoneOutlinedIcon from '@mui/icons-material/DoneOutlined';
import { Menu } from '@mui/material';
import { useState } from 'react';
import { t } from 'i18next';

interface optionInterface {
  name: string;
  value: string | number;
}
export interface HexInputSelectInterface {
  label?: string;
  placeholder?: string;
  showMobileSelect: boolean;
  multiple?: boolean;
  defaultValue: string;
  options: optionInterface[];
  onChange: (value: optionInterface) => void;
  onDialogClosed: () => void;
  anchorElement?: Element | null | undefined;
}

export const HexInputSelectMobile = ({ options, defaultValue, onChange, showMobileSelect, onDialogClosed, anchorElement }: HexInputSelectInterface) => {
  const [checked, setChecked] = useState(defaultValue);
  const handleClick = (opt: any) => {
    onChange(opt);
    setChecked(opt.value);
  };
  const onDialogClose = () => {
    onDialogClosed();
  };

  return (
    <>
      <Menu open={showMobileSelect} anchorEl={anchorElement} onClose={onDialogClose} data-testid="accessType">
        {options.map((opt) => (
          <MenuItem key={opt?.name} value={opt?.value} onClick={() => handleClick(opt)}>
            <DoneOutlinedIcon sx={{ color: 'text.primary', visibility: opt?.value === checked ? 'visible' : 'hidden', mr: 4 }} />
            <ListItemText primary={t(opt?.name)} />
          </MenuItem>
        ))}
      </Menu>
    </>
  );
};
