import React, { fireEvent, render, screen } from '@testing-library/react';
import { HexInputSelect } from './HexInputSelect';
import { Provider } from 'react-redux';
import { createStore } from 'redux';
import { describe, it, expect, vi } from 'vitest';
const mockReducer = (state = { userProjectCount: { projectCount: 2 } }) => {
  return state;
};
const mockStore = createStore(mockReducer);
const onChange = vi.fn();
const options = [
  { name: 'all', value: 'all' },
  { name: 'Owned', value: 'owned' },
  { name: 'Shared', value: 'shared' }
];
describe('HexInputSelect', () => {
  it('should render the input field', async () => {
    render(
      <Provider store={mockStore}>
        <HexInputSelect options={options} defaultValue={''} onChange={onChange} />
      </Provider>
    );
    expect(await screen.getByTestId('accessType')).toBeInTheDocument();
  });

  it('Should work for change event', async () => {
    render(
      <Provider store={mockStore}>
        <HexInputSelect options={options} defaultValue={''} onChange={onChange} />
      </Provider>
    );

    const selectComponent = screen.getByTestId('accessType');

    // Find the hidden input
    const hiddenInput = selectComponent.querySelector('input.MuiSelect-nativeInput');

    // Instead of firing a change event directly,
    // we need to simulate what happens when a user selects an option
    // manually update the hidden input's value
    if (hiddenInput) {
      Object.defineProperty(hiddenInput, 'value', {
        writable: true,
        value: 'shared'
      });
    }

    // Now dispatch a proper change event on the hidden input
    // This is what MUI does internally when an option is selected
    const changeEvent = new Event('change', { bubbles: true });
    hiddenInput?.dispatchEvent(changeEvent);

    // Simulate the selection by calling the handleChange method directly
    // by finding one of the option elements and accessing its value
    const sharedOption = options.find((opt) => opt.value === 'shared');
    if (sharedOption) {
      // Call the handleChange function with a mock event that includes the target
      const mockEvent = { target: { value: 'shared' } };
      fireEvent(hiddenInput as Element, new CustomEvent('change', { bubbles: true, detail: mockEvent }));
    }

    // Verify the callback was called
    expect(onChange).toHaveBeenCalled();
  });
});
