import React, { fireEvent, render, screen, waitFor } from '@testing-library/react';
import { HexInputSelectMobile } from './HexInputSelectMobile';
import userEvent from '@testing-library/user-event';
import { vi, describe, it, expect } from 'vitest';

const onChange = vi.fn();
const onDialogClosed = vi.fn();
const options = [
  { name: 'all', value: 'all' },
  { name: 'Owned', value: 'owned' },
  { name: 'Shared', value: 'shared' }
];
describe('HexInputSelectMobile', () => {
  it('should render the input field', async () => {
    render(<HexInputSelectMobile options={options} defaultValue={''} onChange={onChange} showMobileSelect={true} onDialogClosed={onDialogClosed} />);
    expect(await screen.getByTestId('accessType')).toBeInTheDocument;
  });

  it('Should work for change event', async () => {
    // Create mock anchorElement for the Menu to attach to
    const anchorElement = document.createElement('div');
    document.body.appendChild(anchorElement);

    // Mark test as async
    render(<HexInputSelectMobile options={options} defaultValue={''} onChange={onChange} showMobileSelect={true} onDialogClosed={onDialogClosed} anchorElement={anchorElement} />);

    // Get menu element and verify it's in the document
    const menu = screen.getByTestId('accessType');
    expect(menu).toBeInTheDocument();
    fireEvent.click(menu);

    // Find menu items by role instead of text content
    await waitFor(() => {
      // Use getAllByRole to find all menu items
      const menuItems = screen.getAllByRole('menuitem');
      expect(menuItems.length).toBeGreaterThanOrEqual(3);

      // Check if any menu item has the value attribute "shared"
      const sharedItem = menuItems.find((item) => item.getAttribute('value') === 'shared');
      expect(sharedItem).toBeInTheDocument();
    });

    // Cleanup document after test
    document.body.removeChild(anchorElement);
  });

  it('Should select shared', () => {
    render(<HexInputSelectMobile options={options} defaultValue={''} onChange={onChange} showMobileSelect={true} onDialogClosed={onDialogClosed} />);

    const elem = screen.getByTestId('accessType');
    fireEvent.click(elem);
    const shared = screen.getAllByRole('menuitem')[2];
    fireEvent.click(shared);
    expect(onChange).toHaveBeenCalled();
  });

  it('Should close dialog', () => {
    render(<HexInputSelectMobile options={options} defaultValue={''} onChange={onChange} showMobileSelect={true} onDialogClosed={onDialogClosed} />);
    userEvent.keyboard('{esc}');
    expect(onDialogClosed).toHaveBeenCalled();
  });
});
