import React, { ReactNode } from 'react';
import './HexProductCard.css';
import Button from '@mui/material/Button';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import { StatusBadge } from '@nexusui/components';

export interface HexProductCardProps {
  icon: ReactNode;
  heading: string;
  label: string;
  labelType: string;
  description: string;
  buttonText: string;
  action: () => void;
}

export const HexProductCard: React.FC<HexProductCardProps> = ({ icon, heading, label, labelType, description, buttonText, action }) => {
  return (
    <div className="product-card">
      <div className="product-card-header">
        <div className="product-card-icon">{icon}</div>
        <h2 className="product-card-heading">{heading}</h2>
      </div>
      <StatusBadge label={label} color={labelType} />
      <p className="product-card-description">{description}</p>
      <Button variant="contained" color="primary" endIcon={<ArrowForwardIcon />} onClick={action} data-testid={`${heading}-${buttonText}`}>
        {buttonText}
      </Button>
    </div>
  );
};
