import React, { useState } from 'react';
import { ConnectedNotificationsPanel, useConfigSignalR } from '@nexusui/connected-components';
import { BASE_APP_PATH } from '../../configuration/URL.config';

export default function Notification() {
  const [newNotification, setNewNotification] = useState<any | undefined>(undefined);
  const { signalRConnection } = useConfigSignalR(BASE_APP_PATH);

  React.useEffect(() => {
    const listenSignalR = () => {
      if (!signalRConnection) {
        return;
      }
      signalRConnection.on('newMessage', (comingMessage: any) => {
        setNewNotification(comingMessage);
      });
    };
    listenSignalR();
    return () => {
      signalRConnection && signalRConnection.off('newMessage');
    };
  }, [signalRConnection]);

  return (
    <ConnectedNotificationsPanel
      disablePortal
      open={true}
      notificationMessage={newNotification}
      onClose={() => {
        /* <PERSON>le close action here */
      }}
      sx={{
        position: 'absolute',
        top: 0,
        right: 0,
        height: '100vh',
        '& .MuiDrawer-paper': {
          position: 'relative'
        }
      }}
    />
  );
}
