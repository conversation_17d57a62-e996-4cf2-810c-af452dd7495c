.audit-data-format {
  padding: 5px;
  margin: 5px;
  word-wrap: break-word;
  white-space: break-spaces;
  max-height: 500px;
  overflow-y: scroll;
}
.audit-data-format .string {
  color: #284fb7;
}
.audit-data-format .key {
  color: #bd6662;
}
.audit-sub-header {
  display: flex;
  width: 100%;
  align-items: center;
  justify-content: space-between;
}
.audit-sub-header .right {
  text-align: left;
  padding-left: 35px;
}
.audit-sub-header .left {
  text-align: right;
}
.MuiDataGrid-columnHeader.MuiDataGrid-columnHeader--moving.actionHeader {
  background-color: transparent;
}
