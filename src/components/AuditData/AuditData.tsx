import * as React from 'react';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import './AuditData.css';
import { useEffect, useState, useRef, useCallback } from 'react';
import axios, { RawAxiosRequestHeaders } from 'axios';
import { useParams } from 'react-router-dom';
import { AuditDialog } from './AuditDialog';
import { BASE_URL, ORG_ID_LOCATOR } from '../../configuration/URL.config';
import jwtDecode from 'jwt-decode';
import { useTranslation } from 'react-i18next';
import { Link, Menu, IconButton, MenuItem, Button } from '@mui/material';
import MoreHorizIcon from '@mui/icons-material/MoreHoriz';
import { Audit, DEFAULT_API_PAGE_SIZE } from '@nexusplatform/core-react-components';
import { useHexAuth } from '@nexusplatform/react';

export const AuditData = () => {
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const { t } = useTranslation();
  const open = Boolean(anchorEl);
  let dataAudit: Audit.Data[] = [];
  const [selectedRow, setSelectedRow] = useState('');
  const [showAuditDialog, setShowAuditDialog] = useState(false);
  const [rows, setRows] = useState<Audit.Data[]>([]);
  const hexAuthToken = useRef('');
  const xmsContinuation = useRef('');
  const containerId = useParams().containerId;
  const documentName = useParams().title;
  const { getAccessTokenSilently } = useHexAuth();

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleDialogClose = () => {
    setShowAuditDialog(false);
  };

  const columns: GridColDef[] = [
    { field: 'operation', headerName: t('operation') as string, width: 220 },
    { field: 'time', headerName: t('time') as string, width: 200 },
    { field: 'userId', headerName: t('userId') as string, width: 500 },
    {
      field: 'datastring',
      headerName: 'Data',
      width: 400,
      renderCell: (datastring) => (
        <Link
          onClick={() => {
            setShowAuditDialog(true);
            setSelectedRow(datastring.value);
          }}
        >
          {datastring.value}
        </Link>
      )
    },
    {
      field: '',
      sortable: false,
      disableColumnMenu: true,
      headerClassName: 'actionHeader',
      renderCell: (datastring) => getActions(datastring)
    }
  ];

  const getActions = useCallback((datastring) => {
    return (
      <IconButton
        id="menu-button"
        aria-label="hex"
        data-testid="MoreHorizIcon"
        onClick={(event) => {
          event.stopPropagation();
          handleClick(event, datastring.row.datastring);
        }}
      >
        <MoreHorizIcon />
      </IconButton>
    );
  }, []);

  const menuItemsList = () => {
    const items: any[] = [];

    items.push({
      label: t('viewRawData'),
      getMenuContent: () => t('viewRawData'),
      onClick: () => {
        setShowAuditDialog(true);
      }
    });
    return items;
  };

  const menuItems = () => {
    return menuItemsList()?.map((x: any) => {
      return (
        <MenuItem
          key={x.label}
          data-test-id={x.label}
          onClick={() => {
            x.onClick && x.onClick();
            handleClose();
          }}
        >
          {x.getMenuContent()}
        </MenuItem>
      );
    });
  };

  const handleClick = (event: React.MouseEvent<HTMLElement>, datastring: string) => {
    setSelectedRow(datastring);
    setAnchorEl(event.currentTarget);
  };

  const getCustomHeaders = async () => {
    const JWT = await getAccessTokenSilently();
    if (JWT) {
      let customHeaders: RawAxiosRequestHeaders;
      hexAuthToken.current = JWT;

      if (xmsContinuation.current !== null && xmsContinuation.current && xmsContinuation.current !== 'Token not found') {
        customHeaders = {
          authorization: `Bearer ${JWT}`,
          'x-ms-continuation': xmsContinuation.current
        };
      } else {
        customHeaders = {
          authorization: `Bearer ${JWT}`
        };
      }
      return customHeaders;
    }
    return undefined;
  };

  async function getAuditData() {
    try {
      const customHeaders = await getCustomHeaders();
      const token = await getAccessTokenSilently();
      const decoded = jwtDecode<{ [key: string]: string }>(token as string);
      const organizationId = decoded[ORG_ID_LOCATOR];

      const queryParams = {
        pageSize: DEFAULT_API_PAGE_SIZE,
        sort: 'time:DESC',
        subject: containerId
      };
      const response = await axios({
        method: 'post',
        url: `${BASE_URL}/sdc/organization/${organizationId}/audit`,
        headers: customHeaders,
        params: queryParams
      });

      if (response) {
        dataAudit = response.data.content.map((item: Audit.ExtendedData) => {
          const userName = item.data.principal.identifier ? item.data.principal.identifier.replace(/undefined/gi, '').trim() : '';
          item['userId'] = userName || item.data.principal.id;
          item['datastring'] = JSON.stringify(item.data, undefined, 4);
          const Operation = JSON.stringify(item.dataschema);
          if (Operation) {
            if (Operation.includes('Created')) {
              item['operation'] = 'Create';
            } else if (Operation.includes('Read')) {
              item['operation'] = 'Read';
            } else if (Operation.includes('Deleted')) {
              item['operation'] = 'Delete';
            } else if (Operation.includes('Updated')) {
              item['operation'] = 'Update';
            }
          }
          return item;
        });
        setRows(dataAudit);
      }
    } catch (err) {
      console.log(err);
    }
  }

  useEffect(() => {
    getAuditData();
  }, []);

  return (
    <div>
      <div style={{ textAlign: 'center' }}>
        <h3>Audit Data</h3>
      </div>
      <div className="audit-sub-header">
        <div className="right">
          <h3>{documentName}</h3>
        </div>
        <div className="left">
          <Button onClick={() => getAuditData()} sx={{ m: 4 }} variant="contained" color="primary" data-testid="refresh">
            Click to Reload!
          </Button>
        </div>
      </div>
      <div style={{ height: 700, width: '100%', paddingLeft: '20px' }}>
        <DataGrid key="id" rows={rows} columns={columns} checkboxSelection />
      </div>
      <Menu
        id="long-menu"
        MenuListProps={{
          'aria-labelledby': 'long-button'
        }}
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
      >
        {menuItems()}
      </Menu>
      {showAuditDialog && (
        <AuditDialog
          showAuditDialog={showAuditDialog}
          onCancel={handleDialogClose}
          data={selectedRow}
          onCopy={() => {
            navigator.clipboard.writeText(selectedRow);
          }}
        />
      )}
    </div>
  );
};

export default AuditData;
