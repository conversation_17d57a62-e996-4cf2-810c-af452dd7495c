import React, { ReactNode } from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { AuditData } from './AuditData';
import { StoreProviderMock } from '../../utils/test-untils/mockUtils';
import * as reactRouterDom from 'react-router-dom';

const setupLocalMocks = () => {
  // Mock ResizeObserver locally
  if (!window.ResizeObserver) {
    window.ResizeObserver = class ResizeObserver {
      observe = vi.fn();
      unobserve = vi.fn();
      disconnect = vi.fn();
    };
  }

  // Mock Element.getBoundingClientRect locally if needed
  const originalGetBoundingClientRect = Element.prototype.getBoundingClientRect;
  Element.prototype.getBoundingClientRect = vi.fn().mockImplementation(function () {
    return {
      width: 1000,
      height: 500,
      top: 0,
      left: 0,
      right: 1000,
      bottom: 500,
      x: 0,
      y: 0
    };
  });

  // Return cleanup function
  return () => {
    Element.prototype.getBoundingClientRect = originalGetBoundingClientRect;
  };
};

// minimal DataGrid mock
vi.mock('@mui/x-data-grid', () => {
  interface DataGridProps {
    checkboxSelection?: boolean;
    [key: string]: any;
  }

  return {
    DataGrid: ({ checkboxSelection = false }: DataGridProps) => {
      return (
        <div data-testid="mui-data-grid" role="grid">
          <div role="header-row">Header Row</div>
          <div role="row">Data Row 1</div>
          {checkboxSelection && (
            <input
              type="checkbox"
              role="checkbox"
              aria-label="select all rows"
              onClick={(e) => {
                // Make the checkbox actually change state when clicked
                e.currentTarget.checked = !e.currentTarget.checked;
              }}
            />
          )}
        </div>
      );
    }
  };
});

// Setup mocks before tests run
beforeEach(() => {
  // Reset all mocks before each test
  vi.resetAllMocks();
  setupLocalMocks();

  // Manually restore and re-mock useParams for each test
  // This ensures the mock is properly applied when the component renders
  const mockUseParams = vi.fn().mockReturnValue({
    containerId: 'mock-container-id',
    title: 'Mock Document Title'
  });
  vi.spyOn(reactRouterDom, 'useParams').mockImplementation(mockUseParams);
});

// Single, consistent mock for react-router-dom
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual, // Keep the original functionality
    // Override just what we need
    useParams: vi.fn().mockReturnValue({
      containerId: 'mock-container-id',
      title: 'Mock Document Title'
    }),
    useNavigate: vi.fn().mockReturnValue(vi.fn()),
    useLocation: vi.fn().mockReturnValue({
      pathname: '/audit-data',
      search: '',
      hash: '',
      state: null,
      key: 'default'
    }),
    useRouteMatch: vi.fn().mockReturnValue({
      path: '/audit-data',
      url: '/audit-data'
    }),
    // Components
    BrowserRouter: ({ children }: { children: ReactNode }) => <div data-testid="router-wrapper">{children}</div>,
    Routes: ({ children }: { children: ReactNode }) => <div data-testid="routes-wrapper">{children}</div>,
    Route: ({ path, element }: { path: string; element: ReactNode }) => <div data-testid={`route-${path}`}>{element}</div>,
    Link: ({ to, children, ...rest }: { to: string; children: ReactNode; [key: string]: any }) => (
      <a href={to} {...rest}>
        {children}
      </a>
    ),
    Outlet: () => <div data-testid="outlet" />
  };
});

// Mock axios
vi.mock('axios', () => {
  const getMock = vi.fn().mockResolvedValue({ data: {} });
  const postMock = vi.fn().mockResolvedValue({
    data: {
      content: [
        {
          id: 'audit-1',
          time: '2023-04-01T12:00:00Z',
          data: {
            principal: {
              id: 'user-1',
              identifier: '<EMAIL>'
            }
          },
          dataschema: 'Created'
        },
        {
          id: 'audit-2',
          time: '2023-04-02T14:00:00Z',
          data: {
            principal: {
              id: 'user-2',
              identifier: '<EMAIL>'
            }
          },
          dataschema: 'Updated'
        }
      ]
    }
  });

  return {
    __esModule: true,
    default: {
      get: getMock,
      post: postMock,
      create: vi.fn().mockReturnThis(),
      defaults: {
        headers: {
          common: {}
        }
      }
    },
    get: getMock,
    post: postMock
  };
});

// Now import axios AFTER mocking it
import axios from 'axios';

// Mock authentication
vi.mock('@nexusplatform/react', () => ({
  useHexAuth: () => ({
    getAccessTokenSilently: vi.fn().mockResolvedValue('mock-token')
  })
}));

// Mock JWT decode
vi.mock('jwt-decode', () => ({
  default: vi.fn().mockReturnValue({
    'urn:hexcloud:org': 'mock-org-id'
  })
}));

describe('API', () => {
  it('GET request', async () => {
    await render(
      <StoreProviderMock>
        <AuditData />
      </StoreProviderMock>
    );

    axios.get('/');
    expect(axios.get).toHaveBeenCalledWith('/');
  });
});

describe('AuditData', () => {
  it('should render page', async () => {
    const component = render(
      <StoreProviderMock>
        <AuditData />
      </StoreProviderMock>
    );
    expect(component.getByText('Audit Data')).toBeInTheDocument();
    expect(component.getByText('Click to Reload!')).toBeInTheDocument();
  });

  it('should render table', async () => {
    const component = render(
      <StoreProviderMock>
        <AuditData />
      </StoreProviderMock>
    );
    expect(component.getByRole('grid')).toBeInTheDocument();
    expect(component.getByRole('row')).toBeInTheDocument();
  });

  it('should check row checkbox', () => {
    render(
      <StoreProviderMock>
        <AuditData />
      </StoreProviderMock>
    );
    const checkboxVal = screen.getByRole('checkbox', {
      name: /select all rows/i
    });
    expect(checkboxVal).toBeInTheDocument();

    expect(checkboxVal).toHaveProperty('checked', false);
    fireEvent.click(checkboxVal!, { target: { checked: true } });
  });

  it('should check all the list', () => {
    render(
      <StoreProviderMock>
        <AuditData />
      </StoreProviderMock>
    );
    const checkboxVal = screen.getByRole('checkbox', {
      name: /select all rows/i
    });
    fireEvent.click(checkboxVal!, { target: { checked: true } });
  });
});
