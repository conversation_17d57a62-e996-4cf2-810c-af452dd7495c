import { Button, Dialog, DialogActions, DialogTitle } from '@mui/material';
import React from 'react';
import Grid from '@mui/material/Grid';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';

export interface IAuditDialogProps {
  showAuditDialog: boolean;
  onCopy: (selectedRow) => void;
  onCancel: () => void;
  data: string;
}

export const AuditDialog = ({ showAuditDialog, onCancel, onCopy, data }: IAuditDialogProps) => {
  function syntaxHighlight(json: any) {
    json = json.replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;');
    return {
      __html: json.replace(/("(\\u[a-zA-Z0-9]{4}|\\[^u]|[^\\"])*"(\s*:)?|\b(true|false|null)\b|-?\d+(?:\.\d*)?(?:[eE][+-]?\d+)?)/g, function (match: any) {
        let cls: string;
        if (/:$/.test(match)) {
          cls = 'key';
        } else {
          cls = 'string';
        }
        return '<span class="' + cls + '">' + match + '</span>';
      })
    };
  }
  return (
    <Dialog data-testid="Data-dialog" open={showAuditDialog} onClose={onCancel}>
      <DialogTitle>Data</DialogTitle>
      <Grid sx={{ p: 12, overflow: 'hidden' }} container spacing={6}>
        <pre className="audit-data-format" dangerouslySetInnerHTML={syntaxHighlight(data)}></pre>
      </Grid>

      <DialogActions sx={{ display: 'flex', justifyContent: 'space-between', flexWrap: 'wrap' }}>
        <Button onClick={onCopy} variant="outlined" data-testid="Dialog-action-Copy">
          <ContentCopyIcon sx={{ mr: 2 }} />
          Copy
        </Button>

        <Button onClick={onCancel} variant="contained" color="primary" sx={{ m: 0 }} data-testid="Dialog-action-Cancel">
          Close
        </Button>
      </DialogActions>
    </Dialog>
  );
};
