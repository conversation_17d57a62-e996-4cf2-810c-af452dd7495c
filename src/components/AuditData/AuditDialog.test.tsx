import React, { fireEvent, render, screen } from '@testing-library/react';
import { AuditDialog, IAuditDialogProps } from './AuditDialog';
import { describe, expect, it, vi } from 'vitest';

describe('AuditDialog', () => {
  const onCancel = vi.fn();
  const onCopy = vi.fn();
  const data = 'data';

  const prop: IAuditDialogProps = { onCancel: onCancel, showAuditDialog: true, onCopy: onCopy, data: data };

  it('should render Audit dialog', async () => {
    render(<AuditDialog {...prop} />);
    expect(await screen.getByText('Data')).toBeInTheDocument();
  });

  it('should fire close event', async () => {
    render(<AuditDialog {...prop} />);
    fireEvent.click(await screen.findByTestId('Dialog-action-Cancel'));
    expect(onCancel).toBeCalledTimes(1);
  });

  it('should fire copy event ', async () => {
    render(<AuditDialog {...prop} />);
    fireEvent.click(await screen.findByTestId('Dialog-action-Copy'));
    expect(onCopy).toBeCalledTimes(1);
  });
});
