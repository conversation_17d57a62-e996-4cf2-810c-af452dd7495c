import * as React from 'react';
import Breadcrumbs from '@mui/material/Breadcrumbs';
import Link from '@mui/material/Link';
import { useNavigate } from 'react-router-dom';
import { Typography } from '@mui/material';

import { styles } from './HexBreadCrumb.styles';

export interface Routes {
  name: string;
  path: string;
}

export interface HexBreadCrumbProps {
  routes: Routes[];
  onClick?: (a: Routes) => void;
}

function HexBreadCrumb(props: HexBreadCrumbProps) {
  const { routes, onClick } = props;
  const navigate = useNavigate();

  const handleClick = (route: Routes) => {
    onClick && onClick(route);
    navigate(route.path);
  };

  return (
    <Breadcrumbs maxItems={2} aria-label="breadcrumb">
      {routes?.map((item, key) => {
        const last = key == routes.length - 1;
        return last && item.path != '' ? (
          <Typography sx={styles.selectedbreadCrumbLink} key={key} color="text.primary">
            {item.name}
          </Typography>
        ) : (
          <Link sx={styles.breadCrumbLink} key={key} underline="hover" color="inherit" onClick={() => handleClick(item)}>
            {item.name}
          </Link>
        );
      })}
    </Breadcrumbs>
  );
}

export default HexBreadCrumb;
