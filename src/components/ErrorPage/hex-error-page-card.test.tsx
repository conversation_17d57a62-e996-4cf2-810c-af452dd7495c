import React, { act, render, screen, fireEvent } from '@testing-library/react';
import { HexErrorPageCard } from './hex-error-page-card';
import { vi, describe, it, expect } from 'vitest';

vi.mock('react-i18next', () => ({
  ...vi.importActual('react-i18next'),
  useTranslation: () => {
    return {
      t: (str: string) => str,
      i18n: {
        changeLanguage: () => new Promise<void>((resolve) => resolve())
      }
    };
  }
}));

describe('HexErrorPageCard test suit', () => {
  it('matches snapshot', async () => {
    act(() => {
      const tree = render(<HexErrorPageCard />);
      expect(tree).toMatchSnapshot();
    });
  });

  it('should invoke callback on re-try', async () => {
    const cb = vi.fn();

    await act(async () => {
      render(<HexErrorPageCard retryCallback={cb} />);
    });

    const reTryBtn = screen.getByTestId('errorPageTryAgainBtn');
    vi.spyOn(reTryBtn, 'click');
    fireEvent.click(reTryBtn);

    expect(cb).toBeCalled();
  });

  it('should show schema mismatch error and hide retry btn', async () => {
    const cb = vi.fn();

    await act(async () => {
      render(<HexErrorPageCard isSchemaMisMatch={true} retryCallback={cb} />);
    });

    const errorMessage = screen.getByTestId('errorPageText');
    expect(errorMessage).toHaveTextContent('schemaMisMarchErrorMsg');

    const reTryBtn = screen?.queryByTestId('errorPageTryAgainBtn');
    expect(reTryBtn).toBeNull();
  });
});
