// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`HexErrorPageCard test suit > matches snapshot 1`] = `
{
  "asFragment": [Function],
  "baseElement": <body>
    <div>
      <div
        class="MuiBox-root css-rnkh7f"
      >
        <div
          class="MuiBox-root css-0"
        >
          <svg
            aria-hidden="true"
            class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-cmg8oz-MuiSvgIcon-root"
            data-testid="WarningAmberIcon"
            focusable="false"
            viewBox="0 0 24 24"
          >
            <path
              d="M12 5.99 19.53 19H4.47zM12 2 1 21h22z"
            />
            <path
              d="M13 16h-2v2h2zm0-6h-2v5h2z"
            />
          </svg>
        </div>
        <div
          class="MuiBox-root css-mhwmks"
        >
          <div
            class="MuiBox-root css-0"
          >
            <h6
              class="MuiTypography-root MuiTypography-h6 css-hc862v-MuiTypography-root"
            >
              defaultErrorTitle
            </h6>
          </div>
          <div
            class="MuiBox-root css-1yuhvjn"
          >
            <div
              class="MuiCollapse-root MuiCollapse-vertical MuiCollapse-hidden css-cwrbtg-MuiCollapse-root"
              style="min-height: 0px;"
            >
              <div
                class="MuiCollapse-wrapper MuiCollapse-vertical css-1x6hinx-MuiCollapse-wrapper"
              >
                <div
                  class="MuiCollapse-wrapperInner MuiCollapse-vertical css-1i4ywhz-MuiCollapse-wrapperInner"
                >
                  <div
                    class="MuiPaper-root MuiPaper-elevation MuiPaper-rounded MuiPaper-elevation1 MuiCard-root css-tlgvzk-MuiPaper-root-MuiCard-root"
                    style="--Paper-shadow: 0px 2px 1px -1px rgba(0,0,0,0.2),0px 1px 1px 0px rgba(0,0,0,0.14),0px 1px 3px 0px rgba(0,0,0,0.12);"
                  >
                    <div
                      class="MuiCardContent-root css-1lt5qva-MuiCardContent-root"
                    >
                      <div
                        class="MuiBox-root css-lrt842"
                      >
                        <div
                          class="MuiBox-root css-i243rg"
                        >
                          <svg
                            aria-hidden="true"
                            class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-1umw9bq-MuiSvgIcon-root"
                            data-testid="CancelIcon"
                            focusable="false"
                            style="color: rgb(220, 54, 46); border-radius: 50%; width: 16px; height: 16px; padding: 1px; margin-right: 8px; margin-top: 2px;"
                            viewBox="0 0 24 24"
                          >
                            <path
                              d="M12 2C6.47 2 2 6.47 2 12s4.47 10 10 10 10-4.47 10-10S17.53 2 12 2m5 13.59L15.59 17 12 13.41 8.41 17 7 15.59 10.59 12 7 8.41 8.41 7 12 10.59 15.59 7 17 8.41 13.41 12z"
                            />
                          </svg>
                          <p
                            class="MuiTypography-root MuiTypography-body2 css-mp52pd-MuiTypography-root"
                          >
                            <strong>
                              Error:
                            </strong>
                             
                            defaultExpandedErrorMessage
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <span
              class="MuiTypography-root MuiTypography-body2 css-1nhfg1u-MuiTypography-root"
            >
              showDetails
            </span>
          </div>
          <div
            class="MuiBox-root css-1yuhvjn"
          >
            <p
              class="MuiTypography-root MuiTypography-body1 css-19evaj6-MuiTypography-root"
              data-testid="errorPageText"
            >
              defaultErrorMessage
            </p>
          </div>
          <div
            class="MuiBox-root css-1fe9bi5"
          >
            <button
              class="MuiButtonBase-root MuiButton-root MuiButton-contained MuiButton-containedPrimary MuiButton-sizeMedium MuiButton-containedSizeMedium MuiButton-colorPrimary MuiButton-root MuiButton-contained MuiButton-containedPrimary MuiButton-sizeMedium MuiButton-containedSizeMedium MuiButton-colorPrimary css-1abvrni-MuiButtonBase-root-MuiButton-root"
              data-testid="errorPageTryAgainBtn"
              tabindex="0"
              type="button"
            >
              tryAgainBtn
            </button>
            <button
              class="MuiButtonBase-root MuiButton-root MuiButton-contained MuiButton-containedPrimary MuiButton-sizeMedium MuiButton-containedSizeMedium MuiButton-colorPrimary MuiButton-root MuiButton-contained MuiButton-containedPrimary MuiButton-sizeMedium MuiButton-containedSizeMedium MuiButton-colorPrimary css-1y0i4uk-MuiButtonBase-root-MuiButton-root"
              data-testid="errorPageBackToHomeBtn"
              tabindex="0"
              type="button"
            >
              errorPage.backHomeBtm
            </button>
          </div>
        </div>
      </div>
    </div>
  </body>,
  "container": <div>
    <div
      class="MuiBox-root css-rnkh7f"
    >
      <div
        class="MuiBox-root css-0"
      >
        <svg
          aria-hidden="true"
          class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-cmg8oz-MuiSvgIcon-root"
          data-testid="WarningAmberIcon"
          focusable="false"
          viewBox="0 0 24 24"
        >
          <path
            d="M12 5.99 19.53 19H4.47zM12 2 1 21h22z"
          />
          <path
            d="M13 16h-2v2h2zm0-6h-2v5h2z"
          />
        </svg>
      </div>
      <div
        class="MuiBox-root css-mhwmks"
      >
        <div
          class="MuiBox-root css-0"
        >
          <h6
            class="MuiTypography-root MuiTypography-h6 css-hc862v-MuiTypography-root"
          >
            defaultErrorTitle
          </h6>
        </div>
        <div
          class="MuiBox-root css-1yuhvjn"
        >
          <div
            class="MuiCollapse-root MuiCollapse-vertical MuiCollapse-hidden css-cwrbtg-MuiCollapse-root"
            style="min-height: 0px;"
          >
            <div
              class="MuiCollapse-wrapper MuiCollapse-vertical css-1x6hinx-MuiCollapse-wrapper"
            >
              <div
                class="MuiCollapse-wrapperInner MuiCollapse-vertical css-1i4ywhz-MuiCollapse-wrapperInner"
              >
                <div
                  class="MuiPaper-root MuiPaper-elevation MuiPaper-rounded MuiPaper-elevation1 MuiCard-root css-tlgvzk-MuiPaper-root-MuiCard-root"
                  style="--Paper-shadow: 0px 2px 1px -1px rgba(0,0,0,0.2),0px 1px 1px 0px rgba(0,0,0,0.14),0px 1px 3px 0px rgba(0,0,0,0.12);"
                >
                  <div
                    class="MuiCardContent-root css-1lt5qva-MuiCardContent-root"
                  >
                    <div
                      class="MuiBox-root css-lrt842"
                    >
                      <div
                        class="MuiBox-root css-i243rg"
                      >
                        <svg
                          aria-hidden="true"
                          class="MuiSvgIcon-root MuiSvgIcon-fontSizeMedium css-1umw9bq-MuiSvgIcon-root"
                          data-testid="CancelIcon"
                          focusable="false"
                          style="color: rgb(220, 54, 46); border-radius: 50%; width: 16px; height: 16px; padding: 1px; margin-right: 8px; margin-top: 2px;"
                          viewBox="0 0 24 24"
                        >
                          <path
                            d="M12 2C6.47 2 2 6.47 2 12s4.47 10 10 10 10-4.47 10-10S17.53 2 12 2m5 13.59L15.59 17 12 13.41 8.41 17 7 15.59 10.59 12 7 8.41 8.41 7 12 10.59 15.59 7 17 8.41 13.41 12z"
                          />
                        </svg>
                        <p
                          class="MuiTypography-root MuiTypography-body2 css-mp52pd-MuiTypography-root"
                        >
                          <strong>
                            Error:
                          </strong>
                           
                          defaultExpandedErrorMessage
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <span
            class="MuiTypography-root MuiTypography-body2 css-1nhfg1u-MuiTypography-root"
          >
            showDetails
          </span>
        </div>
        <div
          class="MuiBox-root css-1yuhvjn"
        >
          <p
            class="MuiTypography-root MuiTypography-body1 css-19evaj6-MuiTypography-root"
            data-testid="errorPageText"
          >
            defaultErrorMessage
          </p>
        </div>
        <div
          class="MuiBox-root css-1fe9bi5"
        >
          <button
            class="MuiButtonBase-root MuiButton-root MuiButton-contained MuiButton-containedPrimary MuiButton-sizeMedium MuiButton-containedSizeMedium MuiButton-colorPrimary MuiButton-root MuiButton-contained MuiButton-containedPrimary MuiButton-sizeMedium MuiButton-containedSizeMedium MuiButton-colorPrimary css-1abvrni-MuiButtonBase-root-MuiButton-root"
            data-testid="errorPageTryAgainBtn"
            tabindex="0"
            type="button"
          >
            tryAgainBtn
          </button>
          <button
            class="MuiButtonBase-root MuiButton-root MuiButton-contained MuiButton-containedPrimary MuiButton-sizeMedium MuiButton-containedSizeMedium MuiButton-colorPrimary MuiButton-root MuiButton-contained MuiButton-containedPrimary MuiButton-sizeMedium MuiButton-containedSizeMedium MuiButton-colorPrimary css-1y0i4uk-MuiButtonBase-root-MuiButton-root"
            data-testid="errorPageBackToHomeBtn"
            tabindex="0"
            type="button"
          >
            errorPage.backHomeBtm
          </button>
        </div>
      </div>
    </div>
  </div>,
  "debug": [Function],
  "findAllByAltText": [Function],
  "findAllByDisplayValue": [Function],
  "findAllByLabelText": [Function],
  "findAllByPlaceholderText": [Function],
  "findAllByRole": [Function],
  "findAllByTestId": [Function],
  "findAllByText": [Function],
  "findAllByTitle": [Function],
  "findByAltText": [Function],
  "findByDisplayValue": [Function],
  "findByLabelText": [Function],
  "findByPlaceholderText": [Function],
  "findByRole": [Function],
  "findByTestId": [Function],
  "findByText": [Function],
  "findByTitle": [Function],
  "getAllByAltText": [Function],
  "getAllByDisplayValue": [Function],
  "getAllByLabelText": [Function],
  "getAllByPlaceholderText": [Function],
  "getAllByRole": [Function],
  "getAllByTestId": [Function],
  "getAllByText": [Function],
  "getAllByTitle": [Function],
  "getByAltText": [Function],
  "getByDisplayValue": [Function],
  "getByLabelText": [Function],
  "getByPlaceholderText": [Function],
  "getByRole": [Function],
  "getByTestId": [Function],
  "getByText": [Function],
  "getByTitle": [Function],
  "queryAllByAltText": [Function],
  "queryAllByDisplayValue": [Function],
  "queryAllByLabelText": [Function],
  "queryAllByPlaceholderText": [Function],
  "queryAllByRole": [Function],
  "queryAllByTestId": [Function],
  "queryAllByText": [Function],
  "queryAllByTitle": [Function],
  "queryByAltText": [Function],
  "queryByDisplayValue": [Function],
  "queryByLabelText": [Function],
  "queryByPlaceholderText": [Function],
  "queryByRole": [Function],
  "queryByTestId": [Function],
  "queryByText": [Function],
  "queryByTitle": [Function],
  "rerender": [Function],
  "unmount": [Function],
}
`;
