import { <PERSON>, <PERSON>po<PERSON>, <PERSON><PERSON>, Card, <PERSON>Content, Collapse } from '@mui/material';
import WarningAmberIcon from '@mui/icons-material/WarningAmber';
import { useTranslation } from 'react-i18next';
import React, { useState } from 'react';

import * as errorCardStyles from './hex-error-page-card.styles';
import CancelIcon from '@mui/icons-material/Cancel';
import { BASE_PATH } from '../../configuration/URL.config';

interface IHexError {
  errorMsg?: string;
  hasError?: boolean;
  expandedError?: string[];
  errorTitle?: string;
  retryCallback?: () => void;
  isSchemaMisMatch?: boolean;
}

export const HexErrorPageCard: React.FC<IHexError> = ({ errorTitle, errorMsg, hasError, expandedError, retryCallback, isSchemaMisMatch = false }) => {
  const { t } = useTranslation();
  const [showRawError, setShowRawError] = useState(false);
  const DEFAULT_EXPANDED_ERROR_MESSAGE = [t('defaultExpandedErrorMessage')];

  const getErrorTitle = () => {
    if (isSchemaMisMatch) {
      return t('schemaMismatchErrorTitle');
    } else if (errorTitle) {
      return t(errorTitle);
    } else {
      return t('defaultErrorTitle');
    }
  };

  const getErrorMsg = () => {
    if (isSchemaMisMatch) {
      return t('schemaMisMarchErrorMsg');
    } else if (errorMsg) {
      return t(errorMsg);
    } else {
      return t('defaultErrorMessage');
    }
  };

  return (
    <Box sx={{ margin: '10px', fontSize: '30px', textAlign: 'center' }}>
      <Box>
        <WarningAmberIcon sx={{ fontSize: 66, margin: '15px', color: '#D1D3D4' }} />
      </Box>
      <Box sx={{ fontSize: 24, margin: '5px' }}>
        <Box>
          <Typography fontFamily={'Open Sans'} variant="h6" sx={{ mb: 2 }}>
            {getErrorTitle()}
          </Typography>
        </Box>

        <Box sx={{ mt: 2 }}>
          <Collapse in={showRawError}>
            <Card sx={{ background: '#fcebeb', width: '100%' }}>
              <CardContent>
                <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center', width: 'fit-content', margin: '0 auto' }}>
                  {(expandedError || DEFAULT_EXPANDED_ERROR_MESSAGE).map((error, index) => (
                    <Box key={index} sx={{ display: 'flex', alignItems: 'center', marginBottom: '8px', textAlign: 'left' }}>
                      <CancelIcon
                        style={{
                          color: '#dc362e',
                          borderRadius: '50%',
                          width: '16px',
                          height: '16px',
                          padding: '1px',
                          marginRight: '8px',
                          marginTop: '2px'
                        }}
                      />
                      <Typography color="error" variant="body2">
                        <strong>Error:</strong> {error}
                      </Typography>
                    </Box>
                  ))}
                </Box>
              </CardContent>
            </Card>
          </Collapse>

          <Typography variant="body2" component="span" sx={{ textDecoration: 'underline', cursor: 'pointer' }} onClick={() => setShowRawError(!showRawError)}>
            {showRawError ? t('hide') : t('showDetails')}
          </Typography>
        </Box>

        <Box sx={{ mt: 2 }}>
          <Typography data-testid="errorPageText" sx={errorCardStyles.descriptionText}>
            {getErrorMsg()}
          </Typography>
        </Box>

        {!isSchemaMisMatch && !errorMsg && (
          <Box sx={{ margin: '24px', alignItems: 'center' }}>
            <Button data-testid="errorPageTryAgainBtn" variant="contained" onClick={retryCallback}>
              {t('tryAgainBtn')}
            </Button>
            <Button data-testid="errorPageBackToHomeBtn" variant="contained" onClick={() => window.location.assign(BASE_PATH)} sx={{ marginLeft: '16px' }}>
              {t('errorPage.backHomeBtm')}
            </Button>
          </Box>
        )}
      </Box>
    </Box>
  );
};
