import React from 'react';
import { describe, it, expect, vi } from 'vitest';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import { HexRenameFolder } from './HexRenameFolder';

const onClose = vi.fn();
const onUpdate = vi.fn();
describe('HexRenameProjectFolder', () => {
  it('should render rename project popup', () => {
    render(<HexRenameFolder onClose={onClose} onUpdate={onUpdate} show={true} instanceData={{ name: 'something' }} />);

    const popup = screen.getByTestId('rename-project');

    expect(popup).toBeInTheDocument();
  });
  it('should update on input', async () => {
    render(<HexRenameFolder onClose={onClose} onUpdate={onUpdate} show={true} instanceData={{ name: 'something' }} />);
    const textBox = screen.getByRole('textbox');
    expect(textBox).toBeInTheDocument();
    const acceptButtn = screen.getByRole('button', { name: /rename/i });
    fireEvent.change(textBox, { target: { value: 'something to test' } });

    fireEvent.click(acceptButtn);

    fireEvent.change(textBox, { target: { value: '' } });
    await waitFor(() => {
      const errorIcon = screen.getByTestId('ErrorOutlineIcon');
      expect(errorIcon).toBeInTheDocument();
    });
  });
  it('Should close popup', () => {
    render(<HexRenameFolder onClose={onClose} onUpdate={onUpdate} show={true} instanceData={{ name: 'something' }} />);
    const cancelButton = screen.getByRole('button', { name: /cancel/i });
    expect(cancelButton).toBeInTheDocument();
    fireEvent.click(cancelButton);
  });
});
