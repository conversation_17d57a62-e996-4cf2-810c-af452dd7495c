import React, { useEffect, useState } from 'react';
import Button from '@mui/material/Button';
import DialogActions from '@mui/material/DialogActions';
import DialogContent from '@mui/material/DialogContent';
import DialogTitle from '@mui/material/DialogTitle';
import { useTranslation } from 'react-i18next';
import { RenameDialogStyled } from '../../styles/RenameDialog.styled';
import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline';
import { InputAdornment, TextField } from '@mui/material';

interface RenameProject {
  show: boolean;
  instanceData: { name: string | undefined };
  onClose: () => void;
  onUpdate: (v: string) => void;
}

export const HexRenameFolder = ({ show, instanceData, onUpdate, onClose }: RenameProject) => {
  const { t } = useTranslation();

  const [error, setError] = useState<string>('');
  const [value, setValue] = useState<string>('');

  useEffect(() => {
    setValue(instanceData.name as string);
    setError('');
  }, [instanceData.name]);

  const handleClose = () => {
    onClose();
  };
  const handleChange = (e: any) => {
    if (!e.target.value) {
      setError(t('shouldNotBeBlank'));
    } else {
      if (e.target.value.length > 64) {
        e.target.value = value;
      }
      setError('');
    }

    setValue(e.target.value);
  };
  const handleAcceptClick = () => {
    onUpdate(value);
  };

  return (
    <div data-testid="rename-project">
      <RenameDialogStyled open={show} onClose={handleClose} className="rename-project-dialog">
        <DialogTitle className="title">{t('renameFolderProject')} </DialogTitle>
        <DialogContent>
          <div className="input-container">
            <TextField
              label={t('enterNewFolderProjectName')}
              InputProps={{
                endAdornment: error ? (
                  <InputAdornment position="end">
                    <ErrorOutlineIcon className="error icon" />
                  </InputAdornment>
                ) : (
                  <></>
                )
              }}
              defaultValue={instanceData.name}
              fullWidth
              error={!!error}
              placeholder={t('projectNameField')}
              onChange={handleChange}
            />
            <span className="error">{error}</span>
          </div>
        </DialogContent>
        <DialogActions sx={{ flexWrap: 'wrap' }}>
          <div className="cancel-button">
            <Button data-testid="close-popup" onClick={handleClose}>
              {t('cancel')}
            </Button>
          </div>
          <div className="accept-button">
            <Button onClick={handleAcceptClick} disabled={!!error || !value || value === instanceData.name}>
              {t('rename')}
            </Button>
          </div>
        </DialogActions>
      </RenameDialogStyled>
    </div>
  );
};
