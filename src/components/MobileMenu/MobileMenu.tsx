import React from 'react';
import { Dialog, Divider, List, ListItemButton, ListItemText, Typography, ListSubheader } from '@mui/material';
import { useTranslation } from 'react-i18next';
import FolderOpenOutlinedIcon from '@mui/icons-material/FolderOpenOutlined';
import StickyNote2OutlinedIcon from '@mui/icons-material/StickyNote2Outlined';
import EditNoteOutlinedIcon from '@mui/icons-material/EditNoteOutlined';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import { useNavigate } from 'react-router-dom';

export interface MobileMenuProps {
  showDialog: boolean;
  handleClose?: () => void;
  setIsProject: (val: boolean) => void;
  setIsDocument: (val: boolean) => void;
  setIsDrafts: (val: boolean) => void;
  setIsMenu: (val: boolean) => void;
  folders: any;
  setIsProjectCardClicked: (val: boolean) => void;
  setHeaderTitle: (val: string) => void;
  onClickProjectCard: (id: string, name: string) => void;
  handleNavigate: (url: string) => void;
}
export const MobileMenu: React.FC<MobileMenuProps> = ({
  showDialog,
  handleClose,
  setIsProject,
  setIsDocument,
  setIsDrafts,
  setIsMenu,
  folders,
  setIsProjectCardClicked,
  setHeaderTitle,
  onClickProjectCard,
  handleNavigate
}: MobileMenuProps) => {
  const { t } = useTranslation();
  const navigate = useNavigate();

  const handleProjects = () => {
    setIsMenu(false);
    sessionStorage.setItem('data', 'Projects');

    setIsProject(true);
    setIsDocument(false);
    setIsDrafts(false);
    setIsProjectCardClicked(false);
    setHeaderTitle(`${t('projects')}`);
    handleNavigate('/');
  };
  const handleDocuments = () => {
    setIsMenu(false);
    sessionStorage.setItem('data', 'Documents');
    setIsDocument(true);
    setIsProject(false);
    setIsDrafts(false);
    setIsProjectCardClicked(false);
    setHeaderTitle(`${t('allDocuments')}`);
    handleNavigate(`/`);
  };
  const handleDrafts = () => {
    setIsMenu(false);
    sessionStorage.setItem('data', 'Drafts');
    setIsDrafts(true);
    setIsDocument(false);
    setIsProject(false);
    setIsProjectCardClicked(false);
    setHeaderTitle(`${t('drafts')}`);
    handleNavigate('/');
  };
  return (
    <Dialog
      open={showDialog}
      onClose={handleClose}
      PaperProps={{
        sx: {
          width: '100%',
          height: '100%',
          margin: '0px',
          maxHeight: 'calc(100% - 1px)'
        }
      }}
    >
      <List>
        <ListItemButton onClick={handleClose}>
          <ArrowBackIcon sx={{ mr: 2, color: '#545559' }} />
          <ListItemText primary={t('back')} primaryTypographyProps={{ sx: { fontSize: 14, color: '#545559', fontWeight: 700 } }} />
        </ListItemButton>
        <Divider />
        <ListItemButton onClick={handleProjects}>
          <FolderOpenOutlinedIcon sx={{ mr: 2, color: '#747577' }} />
          <ListItemText primary={t('projects')} primaryTypographyProps={{ sx: { fontSize: 14, color: '#121212', fontWeight: 400 } }} />
        </ListItemButton>
        <ListItemButton onClick={handleDocuments}>
          <StickyNote2OutlinedIcon sx={{ mr: 2, color: '#747577' }} />
          <ListItemText primary={t('allDocuments')} primaryTypographyProps={{ sx: { fontSize: 14, color: '#121212', fontWeight: 400 } }} />
        </ListItemButton>
        <ListItemButton onClick={handleDrafts}>
          <EditNoteOutlinedIcon sx={{ mr: 2, color: '#747577' }} />
          <ListItemText primary={t('drafts')} primaryTypographyProps={{ sx: { fontSize: 14, color: '#121212', fontWeight: 400 } }} />
        </ListItemButton>
      </List>
      <Divider />
      <List
        subheader={
          <ListSubheader component={Typography} variant={'body2'} sx={{ fontWeight: 700, maxHeight: 40, backgroundColor: 'background.paper' }}>
            {t('projects')}
          </ListSubheader>
        }
      ></List>
      {folders.length < 0 ? (
        <Typography sx={{ color: 'grey.600', display: 'flex', paddingLeft: '15px' }} variant="body2">
          {t('noProjects')}
        </Typography>
      ) : (
        folders.map((folder: any) => (
          <ListItemButton key={folder?.id} onClick={() => onClickProjectCard(folder?.id, folder?.name)}>
            <ListItemText primary={folder?.name} primaryTypographyProps={{ noWrap: true, sx: { fontSize: 14 } }} />
          </ListItemButton>
        ))
      )}
    </Dialog>
  );
};
