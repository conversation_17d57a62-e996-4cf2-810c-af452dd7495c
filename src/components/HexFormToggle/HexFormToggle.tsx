import React from 'react';
import { Controller, ControllerProps, useFormContext } from 'react-hook-form';
import { FormControlLabel, Switch } from '@mui/material';
import { FormControlLabelProps } from '@mui/material';

export interface FormInputToggleProps extends Omit<FormControlLabelProps, 'control' | 'defaultValue'>, Omit<ControllerProps, 'control' | 'render' | 'name'> {
  name: string;
  control?: any;
}

/**
 * @param {FormInputToggleProps} props - provides the properties fo React component
 * @return {ReactNode} - provides the react component to be ingested
 **/
export const FormInputToggle: React.FC<FormInputToggleProps> = ({ name, rules, disabled, label }: FormInputToggleProps) => {
  const { control } = useFormContext();

  return (
    <Controller
      name={name}
      control={control}
      rules={rules}
      render={({ field: { ref, ...field } }) => (
        <FormControlLabel inputRef={ref} {...field} disabled={disabled} label={label} style={{ float: 'right' }} control={<Switch defaultChecked role="switch" />} />
      )}
    />
  );
};
