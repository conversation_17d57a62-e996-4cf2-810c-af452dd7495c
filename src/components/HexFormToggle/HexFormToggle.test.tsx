import React, { act, fireEvent, render } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import { FormProvider, useForm } from 'react-hook-form';
import { FormInputToggle, FormInputToggleProps } from './HexFormToggle';

interface StandardFormProps {
  onSubmit?: () => void;
}

function StandardForm({ onSubmit = (): void => undefined }: StandardFormProps) {
  type FormData = {
    autoSave: boolean;
  };
  const methods = useForm<FormData>({ defaultValues: { autoSave: true } });

  const formToggleProps: FormInputToggleProps = {
    name: 'autoSave',
    label: 'always On'
  };

  return (
    <FormProvider {...methods}>
      <form data-testid="form" onSubmit={methods.handleSubmit(onSubmit)}>
        <FormInputToggle {...formToggleProps} />
      </form>
    </FormProvider>
  );
}

describe('tests FormInputToggle', () => {
  it('should show the value with label', () => {
    const component = render(<StandardForm />);
    expect(component.getByLabelText('always On')).toBeInTheDocument;
  });

  it('should update value on click', async () => {
    const onSubmit = vi.fn();
    const component = render(<StandardForm onSubmit={onSubmit} />);

    const switchElement = component.getByRole('switch');
    fireEvent.click(switchElement);

    await act(async () => {
      fireEvent.submit(component.getByTestId('form'));
    });

    expect(onSubmit).toHaveBeenCalledTimes(1);
    expect(onSubmit).toHaveBeenCalledWith(
      {
        autoSave: false
      },
      expect.anything()
    );
  });
});
