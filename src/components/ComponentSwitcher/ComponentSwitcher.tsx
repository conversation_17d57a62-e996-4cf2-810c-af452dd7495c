import React from 'react';
import { useParams } from 'react-router-dom';
import AccountDropdown from '../AccountDropdown/AccountDropdown';
import Notification from '../Notification/Notification';
import AppSwitcher from '../AppSwitcher/AppSwitcher';
import DocumentsView from '../DocumentsView/DocumentsView';
import Connect from '../Connect/Connect';

const ComponentSwitcher: React.FC = () => {
  const { name } = useParams<{ name: string }>();
  const normalizedName = name?.toLowerCase();

  switch (normalizedName) {
    case 'appswitcher':
      return <AppSwitcher />;
    case 'accountdropdown':
      return <AccountDropdown />;
    case 'notification':
      return <Notification />;
    case 'documentsview':
      return <DocumentsView />;
    case 'connect':
      return <Connect />;
    default:
      return <div>Component not found</div>;
  }
};

export default ComponentSwitcher;
