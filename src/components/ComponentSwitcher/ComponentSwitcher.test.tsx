import React from 'react';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import { MemoryRouter, Route, Routes } from 'react-router-dom';
import ComponentSwitcher from './ComponentSwitcher';
import { vi, describe, it, expect } from 'vitest';

// Mock AppSwitcher component
vi.mock('../AppSwitcher/AppSwitcher', () => {
  return {
    __esModule: true,
    default: () => <div data-testid="appSwitcher">App Switcher</div>
  };
});

// Mock AccountDropdown component
vi.mock('../AccountDropdown/AccountDropdown', () => {
  return {
    __esModule: true,
    default: () => <div data-testid="accountDropdown">Account Dropdown</div>
  };
});

// Mock Notification component
vi.mock('../Notification/Notification', () => {
  return {
    __esModule: true,
    default: () => <div data-testid="notification">Notification</div>
  };
});

// Mock DocumentsView component
vi.mock('../DocumentsView/DocumentsView', () => {
  return {
    __esModule: true,
    default: () => <div data-testid="documentsView">Documents View</div>
  };
});

// Mock useHexAuth hook
vi.mock('@nexusplatform/react', () => ({
  useHexAuth: () => ({
    getAccessTokenSilently: vi.fn()
  })
}));

describe('ComponentSwitcher', () => {
  it('should render ConnectedAppSwictherComponent when route param is "appSwitcher"', () => {
    render(
      <MemoryRouter initialEntries={['/component/appSwitcher']}>
        <Routes>
          <Route path="/component/:name" element={<ComponentSwitcher />} />
        </Routes>
      </MemoryRouter>
    );

    // Check if the AppSwitcher component is rendered
    expect(screen.getByTestId('appSwitcher')).toBeInTheDocument();
  });

  it('should render AccountDropdown when route param is "accountdropdown"', () => {
    render(
      <MemoryRouter initialEntries={['/component/accountdropdown']}>
        <Routes>
          <Route path="/component/:name" element={<ComponentSwitcher />} />
        </Routes>
      </MemoryRouter>
    );

    // Check if the AccountDropdown component is rendered
    expect(screen.getByTestId('accountDropdown')).toBeInTheDocument();
  });

  it('should render Notification when route param is "notification"', () => {
    render(
      <MemoryRouter initialEntries={['/component/notification']}>
        <Routes>
          <Route path="/component/:name" element={<ComponentSwitcher />} />
        </Routes>
      </MemoryRouter>
    );

    // Check if the Notification component is rendered
    expect(screen.getByTestId('notification')).toBeInTheDocument();
  });

  it('should render DocumentsView when route param is "documentsview"', () => {
    render(
      <MemoryRouter initialEntries={['/component/documentsview']}>
        <Routes>
          <Route path="/component/:name" element={<ComponentSwitcher />} />
        </Routes>
      </MemoryRouter>
    );

    // Check if the DocumentsView component is rendered
    expect(screen.getByTestId('documentsView')).toBeInTheDocument();
  });

  it('should render "Component not found" for an unknown route param', () => {
    render(
      <MemoryRouter initialEntries={['/component/unknown']}>
        <Routes>
          <Route path="/component/:name" element={<ComponentSwitcher />} />
        </Routes>
      </MemoryRouter>
    );

    // Check if the fallback message is displayed
    expect(screen.getByText('Component not found')).toBeInTheDocument();
  });
});
