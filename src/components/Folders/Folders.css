.foldersContainerStyle {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
  align-items: flex-start;
  align-content: flex-start;
  gap: 20px;
  overflow-y: auto;
  margin-bottom: 10px;
  padding-top: 10px;
  /* height: calc(100vh - 230px); */
}

.folders-mobile-list {
  display: flex;
  width: 100%;
  /* padding-top: 16px; */
  /* max-height: calc(100vh - 392px); */
}

.hex-folder-tile-container {
  width: 100%;
}

@media only screen and (max-width: 900px) {
  .foldersContainerStyle {
    overflow-y: auto;
    /* height: calc(100vh - 307px); */
  }
}
