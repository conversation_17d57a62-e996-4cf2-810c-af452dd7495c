import React, { render, screen } from '@testing-library/react';
import { describe, it, expect, vi, beforeAll } from 'vitest';
import { FoldersComponent } from './Folders';
import theme from '@nexusui/theme';
import { Experimental_CssVarsProvider as CssVarsProvider } from '@mui/material/styles';
import { AuthProviderMock, StoreProviderMock } from '../../utils/test-untils/mockUtils';

vi.mock('@nexusplatform/react', () => ({
  useHexAuth: () => ({
    getAccessTokenSilently: vi.fn().mockResolvedValue('mock-token')
  })
}));

describe('<FoldersComponent/>', () => {
  beforeAll(() => {
    Object.defineProperty(window, 'matchMedia', {
      writable: true,
      value: vi.fn().mockImplementation((query) => ({
        matches: false,
        media: query,
        onchange: null,
        addListener: vi.fn(), // Deprecated
        removeListener: vi.fn(), // Deprecated
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
        dispatchEvent: vi.fn()
      }))
    });
  });
  const foldersList = [
    {
      id: '7951321c-383c-4074-92e3-d2906073a51e',
      organizationId: 'org_TXpDz8lg2K17UXJB',
      name: 'Project Test',
      description: 'Project ABC for Motors',
      documentCount: 4,
      tags: ['test', 'demo'],
      status: 'active',
      acl: [
        {
          userId: 'user123',
          role: 'owner',
          defaultDocumentAccess: 'readWrite',
          defaultCollaborator: true,
          documentCount: 4
        }
      ],
      createdBy: 'user123',
      createdByDate: '2022-07-01T08:35:33.477Z',
      lastModifiedBy: 'user123',
      lastModifiedByDate: '2022-07-01T08:35:33.477Z'
    },
    {
      id: '7951321c-383c-4074-92e3-d2906073a52d',
      organizationId: 'org_TXpDz8lg2K17UXJB',
      name: 'Project Test 2',
      description: 'Project ABC for Motors 2',
      documentCount: 4,
      tags: ['test', 'demo'],
      status: 'active',
      acl: [
        {
          userId: 'user123',
          role: 'collaborator',
          defaultDocumentAccess: 'readWrite',
          defaultCollaborator: true,
          documentCount: 4
        }
      ],
      createdBy: 'user123',
      createdByDate: '2022-07-01T08:35:33.477Z',
      lastModifiedBy: 'user123',
      lastModifiedByDate: '2022-07-01T08:35:33.477Z'
    }
  ];

  const onClickProjectCard = vi.fn();
  const handleDeleteProjectFolder = vi.fn();
  const handleRenameFolder = vi.fn();
  const getContainerWidth = vi.fn();
  it('should render projectcards', () => {
    render(
      <AuthProviderMock>
        <StoreProviderMock>
          <CssVarsProvider theme={theme}>
            <FoldersComponent
              foldersList={foldersList}
              onClickProjectCard={onClickProjectCard}
              handleDeleteProjectFolder={handleDeleteProjectFolder}
              handleRenameFolder={handleRenameFolder}
              containerHeight={100}
              containerWidth={getContainerWidth()}
              showListView={true}
              addEnabled={true}
              hexAuthToken={''}
              isProjectCardClicked={false}
            />
          </CssVarsProvider>
        </StoreProviderMock>
      </AuthProviderMock>
    );
    const cards = screen.queryAllByTestId('NexusComplexCard-root');
    expect(cards).toHaveLength(2);
  });

  it('should have Project Name', () => {
    render(
      <AuthProviderMock>
        <StoreProviderMock>
          <CssVarsProvider theme={theme}>
            <FoldersComponent
              foldersList={foldersList}
              onClickProjectCard={onClickProjectCard}
              handleDeleteProjectFolder={handleDeleteProjectFolder}
              handleRenameFolder={handleRenameFolder}
              containerHeight={100}
              containerWidth={getContainerWidth()}
              showListView={true}
              addEnabled={true}
              hexAuthToken={''}
              isProjectCardClicked={false}
            />
          </CssVarsProvider>
        </StoreProviderMock>
      </AuthProviderMock>
    );
    const elements = screen.queryAllByTestId('NexusComplexCard-textStackWrapper');
    expect(elements.length).toBeGreaterThan(1);
  });
});
