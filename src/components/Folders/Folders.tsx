import React, { useRef } from 'react';
import { Box, Grid } from '@mui/material';
import { FixedSizeList as ReactList } from 'react-window';
import { IFolderList } from '../../models/Folders';
import { FolderCard } from '../FolderCard/FolderCard';
import './Folders.css';

interface IFolderProps {
  foldersList: IFolderList[];
  onClickProjectCard: (id: string, projectName: string) => void;
  handleDeleteProjectFolder: (folder: IFolderList) => void;
  handleRenameFolder: (folder: IFolderList) => void;
  handleUnfollowProjectFolder: (folder: IFolderList) => void;
  containerWidth: number;
  containerHeight: number;
  showListView: boolean;
  addEnabled?: boolean;
  hexAuthToken: string;
  isProjectCardClicked: boolean;
}

/**
 * @param {IFolderProps} props - provides the properties fo React component
 * @return {ReactNode} - provides the react component to be ingested
 **/

export const FoldersComponent: React.FC<IFolderProps> = ({
  foldersList,
  onClickProjectCard,
  handleRenameFolder,
  containerWidth,
  containerHeight,
  showListView,
  addEnabled,
  hexAuthToken,
  isProjectCardClicked,
  handleDeleteProjectFolder,
  handleUnfollowProjectFolder
}: IFolderProps) => {
  const foldersRef = useRef<HTMLDivElement>(null);

  const getFolderListView = () => {
    return (
      <Box data-testid="folders-mobile-card-container" ref={foldersRef} sx={{ px: 6 }}>
        {foldersList.length !== 0 && (
          <>
            <ReactList height={containerHeight ? containerHeight - 60 : 0} itemCount={foldersList.length} itemSize={82} width={'100%'} className="folders-mobile-list">
              {({ index, style }) => {
                return (
                  <Box sx={style} role="listitem" key={`folder-list-item-${index}`}>
                    {getMobileProjectRow(index)}
                  </Box>
                );
              }}
            </ReactList>
          </>
        )}
      </Box>
    );
  };

  const getMobileProjectRow = (index: number) => {
    const folderCards: any[] = [];

    if (foldersList.length > 0 && foldersList[index]) {
      folderCards.push(
        <FolderCard
          key={`folder-card-${foldersList[index].id}`}
          id={foldersList[index].id ?? ''}
          handleRenameFolder={() => handleRenameFolder && handleRenameFolder(foldersList[index])}
          folder={foldersList[index]}
          onClickProjectCard={onClickProjectCard}
          hexAuthToken={hexAuthToken}
          isProjectCardClicked={isProjectCardClicked}
          handleDeleteProjectFolder={() => handleDeleteProjectFolder && handleDeleteProjectFolder(foldersList[index])}
          handleUnfollowProjectFolder={() => handleUnfollowProjectFolder && handleUnfollowProjectFolder(foldersList[index])}
          canUnfollow={foldersList[index].canUnfollow}
        />
      );
    }
    return (
      <Box sx={{ mb: 5 }} key={foldersList[index].id + '_' + index} className={'hex-foldersList-tile-mobile-card'} data-testid="Mobilecard">
        {folderCards}
      </Box>
    );
  };

  const getProjectRow = (rowIndex: number, cardPerRow: number) => {
    const folderCards: any = [];

    for (let i = 0; i < cardPerRow; i++) {
      const FOLDER_INDEX = cardPerRow * rowIndex + i;
      if (FOLDER_INDEX < foldersList.length) {
        folderCards.push(
          <Grid
            item
            className="hex-foldersList-tile-card-container"
            key={foldersList[FOLDER_INDEX].id + '' + (cardPerRow * rowIndex + i)}
            role="presentation"
            data-testid="presentation"
            sx={{ pl: '4rem', width: '100%', maxWidth: '360px' }}
          >
            <FolderCard
              id={foldersList[FOLDER_INDEX].id ?? ''}
              handleRenameFolder={() => handleRenameFolder && handleRenameFolder(foldersList[FOLDER_INDEX])}
              folder={foldersList[FOLDER_INDEX]}
              onClickProjectCard={onClickProjectCard}
              hexAuthToken={hexAuthToken}
              isProjectCardClicked={isProjectCardClicked}
              handleDeleteProjectFolder={() => handleDeleteProjectFolder && handleDeleteProjectFolder(foldersList[FOLDER_INDEX])}
              handleUnfollowProjectFolder={() => handleUnfollowProjectFolder && handleUnfollowProjectFolder(foldersList[FOLDER_INDEX])}
              canUnfollow={foldersList[FOLDER_INDEX].canUnfollow}
            />
          </Grid>
        );
      }
    }

    return (
      <Grid container spacing={4} key={rowIndex * cardPerRow} sx={{ height: '72px' }} wrap={'nowrap'}>
        {folderCards}
      </Grid>
    );
  };

  const getFolderTileView = () => {
    const CARDS_PER_ROW = Math.floor((containerWidth ? containerWidth - 32 : 0) / (342 + 10));
    const ROWS = Math.ceil((foldersList.length + 1) / CARDS_PER_ROW);

    return (
      <>
        <Box data-testid="folders-card-container" ref={foldersRef} sx={{ width: '100%', pl: 6, pt: 6 }}>
          {foldersList.length > 0 && (
            <ReactList overscanCount={4} height={containerHeight ? containerHeight - 53 : 0} itemCount={ROWS} itemSize={82} width={'100%'}>
              {({ index, style }) => {
                return (
                  <Box className="foldersContainerStyle" sx={style} data-testid="folders">
                    {getProjectRow(index, CARDS_PER_ROW)}
                  </Box>
                );
              }}
            </ReactList>
          )}
        </Box>
      </>
    );
  };

  return <>{showListView ? getFolderListView() : getFolderTileView()}</>;
};
