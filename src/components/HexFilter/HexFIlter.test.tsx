import React, { fireEvent, render, screen } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import { HexFilter } from './HexFilter';
import { ProjectGroupData } from '../../configuration/filters.config';
import { Provider } from 'react-redux';
import { createStore } from 'redux';

describe('<HexFilter/>', () => {
  const mockReducer = (state = { userProjectCount: { projectCount: 2 } }) => {
    return state;
  };
  const mockStore = createStore(mockReducer);
  const onfilter = vi.fn();
  const filterdata = {
    groupedData: ProjectGroupData
  };
  it('should click selected options and then deslsect it', async () => {
    render(
      <Provider store={mockStore}>
        <HexFilter {...filterdata} onFilterChange={onfilter} />
      </Provider>
    );
    const node = screen.getByTestId('selectTag').querySelector('input');
    node &&
      fireEvent.change(node, {
        target: { value: 'additiveManufacturing' }
      });
    expect(onfilter).toHaveBeenCalledWith(['additiveManufacturing']);
  });
  // Keeping the test here as we removed all as there are only 2 options now making the control weird to use...
  // it('should pass all elements when all clicked', async () => {
  //   render(<HexFilter {...filterdata} onFilterChange={onfilter} />);
  //   const node = screen.getByTestId('selectTag').querySelector('input');
  //   node &&
  //     fireEvent.change(node, {
  //       target: { value: 'all' }
  //     });

  //   expect(onfilter).toHaveBeenCalledWith(['all', 'additiveManufacturing', 'metroReports']);

  //   if (
  //     node &&
  //     fireEvent.change(node, {
  //       target: { value: 'all' }
  //     })
  //   ) {
  //     expect(onfilter).toHaveBeenCalledWith([]);
  //   }
  // });
});
