import React, { fireEvent, render, screen } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import { HexFilterMobile } from './HexFilterMobile';
import { ProjectGroupData } from '../../configuration/filters.config';
import userEvent from '@testing-library/user-event';

describe('<HexFilterMobile/>', () => {
  const onFilterChange = vi.fn();
  const onDialogClosed = vi.fn();
  const filterdata = {
    groupedData: ProjectGroupData
  };
  it('should click selected options and then deslsect it', async () => {
    render(<HexFilterMobile {...filterdata} onFilterChange={onFilterChange} showFilterMobile={true} onDialogClosed={onDialogClosed} />);
    const node = screen.getAllByTestId('selectTagMobile')[0];
    fireEvent.click(node);
    expect(onFilterChange).toHaveBeenCalledWith(['additiveManufacturing']);
  });
  it('should close dialog', async () => {
    render(<HexFilterMobile {...filterdata} onFilterChange={onFilterChange} showFilterMobile={true} onDialogClosed={onDialogClosed} />);
    userEvent.keyboard('{esc}');
    expect(onDialogClosed).toHaveBeenCalled();
  });
});
