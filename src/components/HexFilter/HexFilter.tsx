import React, { useEffect, useRef } from 'react';

// MUI components
import { Box } from '@mui/system';
import Checkbox from '@mui/material/Checkbox';
import { FormControl, Typography } from '@mui/material';
import ListItemText from '@mui/material/ListItemText';
import ListSubheader from '@mui/material/ListSubheader';
import MenuItem from '@mui/material/MenuItem';
import Select, { SelectChangeEvent } from '@mui/material/Select';
import DoneOutlinedIcon from '@mui/icons-material/DoneOutlined';

// Translations
import { t } from 'i18next';

// Models
import { IGroupedData } from '../../models/Project';
import { useSelector } from 'react-redux';
import { RootState } from '../../redux';

const ITEM_HEIGHT = 48;
const ITEM_PADDING_TOP = 8;
const MenuProps = {
  PaperProps: {
    style: {
      maxHeight: ITEM_HEIGHT * 7.5 + ITEM_PADDING_TOP,
      width: 250
    }
  }
};
interface HexFilterProps {
  groupedData: IGroupedData[];
  onFilterChange: (filter: string[]) => void;
  defaultFilterValues?: string[];
  isProject?: boolean;
}

export const HexFilter = ({ groupedData, onFilterChange, defaultFilterValues, isProject }: HexFilterProps) => {
  const projectCount = useSelector((state: RootState) => state.userProjectCount.projectCount);
  const filterValues = useRef<string[]>(defaultFilterValues || []);
  const totalFilters = useRef<number>(0);
  useEffect(() => {
    let totalVal = 0;
    groupedData.forEach((gd) => {
      gd.data.forEach(() => {
        totalVal += 1;
      });
    });
    totalFilters.current = totalVal;
  }, [groupedData]);

  useEffect(() => {
    if (!filterValues.current.length && defaultFilterValues?.length) {
      filterValues.current = defaultFilterValues;
    }
  }, [defaultFilterValues]);

  const clearFilter = () => {
    onFilterChange([]);
    filterValues.current = [];
  };

  const selectAll = () => {
    const filteredColl: string[] = [];
    groupedData.forEach((gd) =>
      gd.data.forEach((d) => {
        filteredColl.push(d.name);
      })
    );
    onFilterChange(filteredColl);
    filterValues.current = filteredColl;
  };
  const handleChange = (event: SelectChangeEvent<string[]>) => {
    if (filterValues.current.length < totalFilters.current && event.target.value.includes('all')) {
      selectAll();
    } else if (filterValues.current.length === totalFilters.current && event.target.value.includes('all')) {
      let values = [...event.target.value];

      if (values && values.filter) {
        values = values.filter((val: string) => {
          if (val) {
            return val.toLowerCase() !== 'all';
          }
        });
        const filteredColl: string[] = [];
        groupedData.forEach((gd) =>
          gd.data.forEach((d) => {
            if (values.includes(d.name)) {
              filteredColl.push(d.name);
            }
          })
        );
        onFilterChange(filteredColl);
        filterValues.current = filteredColl;
      }
    } else if (filterValues.current.includes('all') && !event.target.value.includes('all')) {
      clearFilter();
    } else if (totalFilters.current - 1 === event.target.value.length) {
      selectAll();
    } else {
      const values = event.target.value as string[];
      const filteredColl: string[] = [];
      groupedData.forEach((gd) =>
        gd.data.forEach((d) => {
          if (values.includes(d.name)) {
            filteredColl.push(d.name);
          }
        })
      );
      onFilterChange(filteredColl);
      filterValues.current = filteredColl;
    }
  };
  const renderSelectGroup = (grpData: IGroupedData, renderHeader = true) => {
    const items = grpData.data.map((p, i) => {
      const checked = filterValues.current.indexOf(p.name) > -1;
      if (p.name !== 'all') {
        return (
          <MenuItem key={i + p.name} value={p.name} data-test-id={i + p.name} disabled={p.name === 'all' ? true : false}>
            {!checked && <Box sx={{ height: '42px', width: '42px' }}></Box>}
            {checked && (
              <Checkbox
                data-testid="checked_tag"
                name="check"
                checked={filterValues.current.indexOf(p.name) > -1}
                icon={<></>}
                checkedIcon={<DoneOutlinedIcon sx={{ color: 'black' }} />}
              />
            )}
            <ListItemText primary={t(p.label)} />
          </MenuItem>
        );
      }
    });
    if (renderHeader) {
      return [<ListSubheader key={`header-${grpData.name}`}>{grpData.name}</ListSubheader>, items];
    } else {
      return [items];
    }
  };

  return (
    <>
      {!isProject && (
        <FormControl sx={{ m: 1, width: '100%' }}>
          <Select
            className="select"
            data-testid="selectTag"
            color="primary"
            displayEmpty
            labelId="demo-multiple-checkbox-label"
            id="demo-multiple-checkbox"
            sx={{
              boxShadow: 'none',
              '.MuiOutlinedInput-notchedOutline': { border: 0 },
              margin: 0,
              '.MuiSvgIcon-root ': {
                fill: projectCount === 0 ? '#D3D3D3' : '#005072'
              }
            }}
            renderValue={(selected) => {
              if (selected.length === 0) {
                return (
                  <Typography color={'primary'} fontWeight={700} fontSize={14}>
                    {t('projectType')}
                  </Typography>
                );
              }

              if (selected.some((val) => val.toLocaleLowerCase() === 'all')) {
                return (
                  <Typography color={'primary'} fontWeight={700} fontSize={14}>
                    {t('allProjectTypes')}
                  </Typography>
                );
              } else if (selected.length === 1) {
                return (
                  <Typography color={'primary'} fontWeight={700} fontSize={14}>
                    {t(selected[0])}
                  </Typography>
                );
              } else {
                return (
                  <Typography color={'primary'} fontWeight={700} fontSize={14}>
                    {t('{{number}}DocumentTypes', { number: selected.length })}
                  </Typography>
                );
              }
            }}
            multiple
            value={filterValues.current ? filterValues.current : undefined}
            onChange={handleChange}
            MenuProps={MenuProps}
            disabled={projectCount === 0}
          >
            {groupedData?.map((gd) => renderSelectGroup(gd, groupedData.length > 1))}
          </Select>
        </FormControl>
      )}
    </>
  );
};
