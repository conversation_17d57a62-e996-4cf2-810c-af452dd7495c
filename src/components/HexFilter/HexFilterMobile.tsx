import React, { useEffect, useRef } from 'react';

// MUI components
import { Menu } from '@mui/material';
import ListItemText from '@mui/material/ListItemText';
import ListSubheader from '@mui/material/ListSubheader';
import MenuItem from '@mui/material/MenuItem';
import DoneOutlinedIcon from '@mui/icons-material/DoneOutlined';

// Translations
import { t } from 'i18next';

// Models
import { IGroupedData } from '../../models/Project';

interface HexFilterProps {
  groupedData: IGroupedData[];
  onFilterChange: (filter: string[]) => void;
  defaultFilterValues?: string[];
  showFilterMobile: boolean;
  onDialogClosed: () => void;
  anchorElement?: Element | null | undefined;
}

export const HexFilterMobile = ({ groupedData, onFilterChange, defaultFilterValues, showFilterMobile, onDialogClosed, anchorElement }: HexFilterProps) => {
  const filterValues = useRef<string[]>(defaultFilterValues || []);
  const totalFilters = useRef<number>(0);
  useEffect(() => {
    let totalVal = 0;
    groupedData.forEach((gd) => {
      gd.data.forEach(() => {
        totalVal += 1;
      });
    });
    totalFilters.current = totalVal;
  }, [groupedData]);

  const addMap = (event: any, value: any) => {
    const value1 = filterValues.current.indexOf(value.name);
    if (value1 === -1) {
      filterValues.current.push(value.name);
      if (filterValues.current.length === groupedData[0].data.length - 1) {
        filterValues.current.push('all');
      }
      onFilterChange(filterValues.current);
    } else {
      filterValues.current.splice(value1, 1);
      if (filterValues.current.includes('all')) {
        const index = filterValues.current.indexOf('all');
        filterValues.current.splice(index, 1);
      }
    }
    onFilterChange(filterValues.current);
  };
  const onDialogClose = () => {
    onDialogClosed();
  };

  const renderSelectGroup = (grpData: IGroupedData, renderHeader = true) => {
    const items = grpData.data.map((p, i) => {
      const checked = filterValues.current.indexOf(p.name) > -1;
      if (p.name !== 'all') {
        return (
          <MenuItem
            onClick={(e) => addMap(e, p)}
            key={i + p.name}
            value={p.name}
            data-test-id={i + p.name}
            disabled={p.name === 'all' ? true : false}
            data-testid="selectTagMobile"
          >
            <DoneOutlinedIcon sx={{ color: 'text.primary', visibility: checked ? 'visible' : 'hidden', mr: 4 }} />
            <ListItemText primary={t(p.label)} />
          </MenuItem>
        );
      }
    });
    if (renderHeader) {
      return [
        <ListSubheader key={`header-${grpData.name}`} className="mobileHexFilter">
          {grpData.name}
        </ListSubheader>,
        items
      ];
    } else {
      return [items];
    }
  };

  return (
    <>
      <Menu open={showFilterMobile} anchorEl={anchorElement} onClose={onDialogClose}>
        {groupedData?.map((gd) => renderSelectGroup(gd, groupedData.length > 1))}
      </Menu>
    </>
  );
};
