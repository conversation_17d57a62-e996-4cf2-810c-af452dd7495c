/* eslint-disable require-jsdoc */
import React, { act, fireEvent, render } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import { FormProvider, useForm } from 'react-hook-form';
import { FormInputText, FormInputTextProps } from './HexFormInput';

type StandardFormProps = {
  onSubmit?: () => void;
};

function StandardForm({ onSubmit = (): void => undefined }: StandardFormProps) {
  type FormData = {
    project: string;
  };
  const methods = useForm<FormData>({ defaultValues: { project: 'Test Project' }, mode: 'onChange' });

  const formInputTextProps: FormInputTextProps = {
    name: 'project',
    label: 'Project Name'
  };

  return (
    <FormProvider {...methods}>
      <form data-testid="form" onSubmit={methods.handleSubmit(onSubmit)}>
        <FormInputText {...formInputTextProps} rules={{ required: 'ProjectName is required' }} />
      </form>
    </FormProvider>
  );
}

describe('tests FormInputText', () => {
  it('should show the text value with label', () => {
    const component = render(<StandardForm />);
    expect(component.getByDisplayValue('Test Project')).toBeInTheDocument;
    expect(component.getAllByLabelText('Project Name')).toBeInTheDocument;
  });

  it('should update values on blur', async () => {
    const onSubmit = vi.fn();
    const component = render(<StandardForm onSubmit={onSubmit} />);

    const inputElement = component.getByDisplayValue('Test Project');
    const expectedValue = 'Updated project';
    fireEvent.change(inputElement, { target: { value: expectedValue } });
    fireEvent.blur(inputElement);

    await act(async () => {
      fireEvent.submit(component.getByTestId('form'));
    });

    expect(onSubmit).toHaveBeenCalledTimes(1);
    expect(onSubmit).toHaveBeenCalledWith(
      {
        project: 'Updated project'
      },
      expect.anything()
    );
  });

  it('should show the validation message', async () => {
    const component = render(<StandardForm />);

    const inputElement = component.getByDisplayValue('Test Project');
    fireEvent.change(inputElement, { target: { value: '' } });
    fireEvent.blur(inputElement);

    expect(await component.findByText('ProjectName is required')).toBeInTheDocument;
  });
});
