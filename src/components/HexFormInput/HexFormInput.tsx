import React from 'react';
import { Controller, ControllerProps, useFormContext } from 'react-hook-form';
import { TextField } from '@mui/material';

export interface FormInputTextProps extends Omit<ControllerProps, 'control' | 'render' | 'name'> {
  name: string;
  label: string;
  setValue?: any;
}

/**
 * @param {IProjectCard} props - provides the properties fo React component
 * @return {ReactNode} - provides the react component to be ingested
 **/
export const FormInputText: React.FC<FormInputTextProps> = ({ name, label, rules, defaultValue }: FormInputTextProps) => {
  const { control } = useFormContext();

  return (
    <Controller
      rules={rules}
      control={control}
      name={name}
      defaultValue={defaultValue}
      render={({ field: { ref, ...field }, fieldState: { error } }) => (
        <TextField
          inputRef={ref}
          {...field}
          inputProps={{ 'aria-label': name }}
          helperText={error ? error.message : null}
          size="small"
          error={!!error}
          fullWidth
          label={label}
          variant="outlined"
          color="primary"
        />
      )}
    />
  );
};
