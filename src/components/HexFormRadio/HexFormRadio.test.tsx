import React, { act, fireEvent, render } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import { FormProvider, useForm } from 'react-hook-form';
import { FormInputRadio, FormInputRadioProps } from './HexFormRadio';

interface StandardFormProps {
  onSubmit?: () => void;
}

function StandardForm({ onSubmit = (): void => undefined }: StandardFormProps) {
  type FormData = {
    type: string;
  };
  const methods = useForm<FormData>({ defaultValues: { type: 'male' } });

  const formRadioProps: FormInputRadioProps = {
    name: 'type',
    options: [
      { value: 'male', label: 'Male' },
      { value: 'female', label: 'Female' }
    ],
    defaultValue: 'male',
    label: 'Gender'
  };

  return (
    <FormProvider {...methods}>
      <form data-testid="form" onSubmit={methods.handleSubmit(onSubmit)}>
        <FormInputRadio {...formRadioProps} />
      </form>
    </FormProvider>
  );
}

describe('tests FormInputRadio', () => {
  it('should show the value with label', () => {
    const component = render(<StandardForm />);
    expect(component.getByText('Gender')).toBeInTheDocument;
    expect(component.getByLabelText('Male')).toBeChecked();
    expect(component.getByLabelText('Female')).toBeInTheDocument;
  });

  it('should update value on click', async () => {
    const onSubmit = vi.fn();
    const component = render(<StandardForm onSubmit={onSubmit} />);

    const radio = component.getByLabelText('Female');
    fireEvent.click(radio);

    await act(async () => {
      fireEvent.submit(component.getByTestId('form'));
    });

    expect(onSubmit).toHaveBeenCalledTimes(1);
    expect(onSubmit).toHaveBeenCalledWith(
      {
        type: 'female'
      },
      expect.anything()
    );
  });
});
