import React from 'react';
import { FormControl, FormControlLabel, FormControlLabelProps, FormLabel, Radio, RadioGroup } from '@mui/material';
import { Controller, ControllerProps, useFormContext } from 'react-hook-form';

export interface FormInputRadioProps extends Omit<ControllerProps, 'control' | 'render' | 'name'> {
  name: string;
  options: Array<Omit<FormControlLabelProps, 'control'>>;
  defaultValue: string | number;
  label: string;
  control?: any;
}

/**
 * @param {FormInputRadioProps} props - provides the properties fo React component
 * @return {ReactNode} - provides the react component to be ingested
 **/
export const FormInputRadio: React.FC<FormInputRadioProps> = ({ name, label, options, defaultValue }: FormInputRadioProps) => {
  const { control } = useFormContext();
  const generateRadioOptions = () => {
    return options.map((option, i) => <FormControlLabel {...option} key={option.value as string} control={<Radio />} />);
  };

  return (
    <FormControl component="fieldset" data-testid="text">
      <FormLabel component="legend">{label}</FormLabel>
      <Controller
        name={name}
        control={control}
        render={({ field }) => (
          <RadioGroup {...field} defaultValue={defaultValue} role="radiogroup">
            {generateRadioOptions()}
          </RadioGroup>
        )}
      />
    </FormControl>
  );
};
