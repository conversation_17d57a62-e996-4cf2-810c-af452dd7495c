import styled from '@emotion/styled';
import Dialog from '@mui/material/Dialog';

export const ShareDialogStyled = styled(Dialog)`
  font-family: Open Sans;
  letter-spacing: 0em;
  line-height: 24px;

  .MuiPaper-root {
    /* max-width: 699px; */
    padding: 24px;
  }
  .MuiDialogContent-root {
    padding: 0px !important;
  }

  .radio-buttons-control {
    font-size: 14px;
    font-weight: 400;
  }

  .radio-button {
    padding: 10px;
  }

  .email-validator-main {
    align-items: baseline;
    text-align: center;
    margin-top: 16px !important;
  }

  .email-validator {
    border: 1px solid #85cddb;
    border-radius: 4px;
    padding: 8px 12px 8px 12px;
    display: flex;
    flex-wrap: wrap;
  }

  .permission-selector {
    font-size: 12px;
    font-weight: 700;
    line-height: 16px;
  }

  .permission-selector > div {
    padding: 5px;
    font-size: 12px;
  }

  .menu-item {
    font-size: 12px !important;
  }

  .sharing-option {
    /* padding: 10px 0px; */
    font-size: 14px;
    font-weight: 400;
    height: 64px;
    display: flex;
    align-items: center;
  }

  .sharing-option.border {
    border-bottom: 1px solid #e6e6e6;
  }

  .sharing-option {
    & > span {
      margin-left: 25px;
      cursor: pointer;
    }
  }

  .title {
    font-size: 18px !important;
    font-weight: bold !important;
    padding: 0px !important;
  }

  .warning-grid {
    align-items: center;
    font-size: 14px !important;
    padding: 16px;
    margin-top: 16px !important;
    background: rgba(255, 237, 210, 0.24);
    border-radius: 8px;
  }

  .warning-grid .icon {
    color: #ed8b00;
  }

  .privacy-grid {
    align-items: center;
    font-size: 14px;
    color: #949494;
    padding: 16px;
    /* margin-top: 16px !important; */
  }

  .privacy-grid .icon {
    color: #949494;
    text-align: center;
  }

  .dialog-close {
    float: right;
    cursor: pointer;
  }

  .shared-with-main {
    font-size: 14px;
    font-weight: bold;
    color: #949494;
    font-family: 'Open Sans';
    align-items: center;
    /* padding: 20px; */
    margin-top: 20px;
  }

  .shared-with-main .icon {
    text-align: center;
  }

  .user-image-container {
    display: flex;
  }

  .user-image {
    text-align: center;
    border-radius: 100px;
    padding: 8px;
    width: 20px;
    height: 20px;
    color: #fff;
    font-size: 12px;
    background: #e5e5e5;
  }

  .user-image.active {
    background: #005072;
  }

  .shared-user {
    align-items: center;
    padding: 0px 10px;
    font-size: 14px;
  }

  .shared-user-actions {
    font-size: 12px;
    align-items: center;
    & > path {
      font-size: 18px;
    }
  }

  fieldset {
    border: none !important;
    outline: none !important;
  }

  .invite-button {
    width: 97px !important;
    height: 40px;
    background: #005072;
    border-radius: 8px !important;
    margin: 0px;
  }

  .shared-user-actions {
    .item {
      flex: 50%;
    }

    .icon {
      cursor: pointer;
    }
  }
`;

export const sharingOptionRadioButton = {
  '& .MuiSvgIcon-root': {
    fontSize: 14
  },
  color: '#85CDDB',
  '&.Mui-checked': {
    color: '#005072'
  }
};
