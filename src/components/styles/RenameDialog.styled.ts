import styled from '@emotion/styled';
import Dialog from '@mui/material/Dialog';

export const RenameDialogStyled = styled(Dialog)`
  .MuiPaper-root {
    padding: 32px;
    width: 100%;
    font-family: 'Open Sans';
    font-style: normal;
    line-height: 16px;
  }
  .MuiTypography-root,
  .MuiDialogContent-root {
    padding: 0px;
  }
  .title {
    font-size: 20px !important;
    font-weight: bold !important;
    padding: 0px !important;
  }

  .dialog-close {
    float: right;
    cursor: pointer;
  }

  .input-container {
    padding: 10px 0px 4px;
    font-size: 14px;
  }

  .input-container {
    .label {
      margin-bottom: 10px;
    }

    input {
      font-size: 14px;
      padding: 10px;
    }
  }

  .MuiDialogActions-root {
    padding: 0px !important;
    margin-top: 15px;
    display: flex;
    align-items: center;
  }

  .MuiDialogActions-root {
    display: flex;
    justify-content: space-between;
  }
  .error {
    color: red;
  }
  .accept-button {
    margin: 0px;
  }
`;
