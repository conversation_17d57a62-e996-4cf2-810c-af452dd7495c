import React from 'react';
import { Typography } from '@mui/material';
import { styles } from './HexEmptyProject.styles';
import { IFolderList } from '../../models/Folders';
import { Box } from '@mui/material';
import { useTranslation } from 'react-i18next';

export interface IHexEmptyFolders {
  folderName?: string;
  foldersList: Array<IFolderList>;
  containerHeight: number | undefined;
}
export const HexEmptyFolder: React.FC<IHexEmptyFolders> = ({ folderName, foldersList, containerHeight }: IHexEmptyFolders) => {
  const { t } = useTranslation();
  const projectMessage = `${t('noSearchResultFor')}`;

  return folderName && foldersList.length == 0 ? (
    <div style={styles.hexEmptyContainer} data-testid="empty">
      <Typography fontFamily={'Open Sans'} variant="h5">
        {projectMessage}&quot;{folderName}&quot;
      </Typography>
    </div>
  ) : (
    <Box sx={styles.hexContainer} data-testid="empty" height={containerHeight ? containerHeight - 57 : 0}>
      <Typography fontFamily={'Open Sans'} variant="subtitle1" fontWeight={700} paddingBottom={'10px'} data-testid="no-document">
        {t('noDocuments')}
      </Typography>
    </Box>
  );
};
