import React from 'react';
import { But<PERSON>, Typography, useMediaQuery } from '@mui/material';
import { styles } from './HexEmptyProject.styles';
import { IProject } from '../../models/Project';
import { Box } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { CONTACT_SALES_URL, HELP_URL, JOIN_COMMUNITY_URL, METROLOGY_REPORTING_URL, NEXUS_APPS_URL, NEXUS_COMPUTE_URL } from '../../configuration/URL.config';
import FolderIcon from '@nexusui/icons/Folder';
import { HexProductCard, HexProductCardProps } from '../HexProductCard/HexProductCard';
import LaunchOutlinedIcon from '@mui/icons-material/LaunchOutlined';
import ThreeDWhiteboardIcon from '@nexusui/branding/ThreeDWhiteboard';
import NexusComputeIcon from '@nexusui/branding/NexusCompute';
import MetrologyReportingIcon from '@nexusui/branding/MetrologyReporting';
import Join<PERSON>he<PERSON>ommunityIcon from '/src/assets/join_community.svg';
import ContactSalesIcon from '/src/assets/contact_sales.svg';
import GetHelpIcon from '/src/assets/get_help.svg';
import { IPlainCard, PlainCard } from '@nexusui/components';
import { ArrowForward } from '@mui/icons-material';
export interface IHexEmptyProjects {
  projectName?: string;
  projects: Array<IProject>;
  containerHeight: number | undefined;
  createDemoProject: () => void;
}
/**
 * @param {IProjectCard} props - provides the properties fo React component
 * @return {ReactNode} - provides the react component to be ingested
 **/
export const HexEmptyProject: React.FC<IHexEmptyProjects> = ({ projectName, projects, containerHeight, createDemoProject }: IHexEmptyProjects) => {
  const { t } = useTranslation();
  const projectMessage = `${t('noSearchResultFor')}`;
  const isMobile = useMediaQuery('(max-width:900px)');
  const openExternalLink = (url: string) => () => window.open(url, '_blank');

  const productCards: HexProductCardProps[] = [
    {
      icon: <ThreeDWhiteboardIcon />,
      heading: '3D Whiteboard',
      label: t('demo'),
      labelType: 'success',
      description: t('whiteboardDescription'),
      buttonText: t('openDemo'),
      action: createDemoProject
    },
    {
      icon: <NexusComputeIcon />,
      heading: 'Nexus Compute',
      label: t('freeTrial'),
      labelType: 'info',
      description: t('computeDescription'),
      buttonText: t('startTrial'),
      action: openExternalLink(NEXUS_COMPUTE_URL)
    },
    {
      icon: <MetrologyReportingIcon />,
      heading: 'Metrology Reporting',
      label: t('freeTrial'),
      labelType: 'info',
      description: t('metrolgyDescription'),
      buttonText: t('startTrial'),
      action: openExternalLink(METROLOGY_REPORTING_URL)
    }
  ];

  const plainCardProps: IPlainCard[] = [
    {
      dense: false,
      headline: t('joinTheCommunity'),
      content: t('communityDescription'),
      color: 'default',
      icon: <img src={JoinTheCommunityIcon} alt="Join the community" height="48" width="48" />,
      actionButtons: [
        <Button data-testid="browseCommunities" key="community" variant="text" endIcon={<ArrowForward />} sx={{ pl: 0 }} onClick={openExternalLink(JOIN_COMMUNITY_URL)}>
          {t('browseCommunities')}
        </Button>
      ]
    },
    {
      dense: false,
      headline: t('contactSales'),
      content: t('contactSalesDescription'),
      color: 'default',
      icon: <img src={ContactSalesIcon} alt="Contact sales" height="48" width="48" />,
      actionButtons: [
        <Button data-testid="getConnected" key="contact-sales" variant="text" endIcon={<ArrowForward />} sx={{ pl: 0 }} onClick={openExternalLink(CONTACT_SALES_URL)}>
          {t('getConnected')}
        </Button>
      ]
    },
    {
      dense: false,
      headline: t('getHelp'),
      content: t('helpDescription'),
      color: 'default',
      icon: <img src={GetHelpIcon} alt="Get help" height="48" width="48" />,
      actionButtons: [
        <Button data-testid="createAticket" key="support-request" variant="text" endIcon={<ArrowForward />} sx={{ pl: 0 }} onClick={openExternalLink(HELP_URL)}>
          {t('createTicket')}
        </Button>
      ]
    }
  ];
  return projectName && projects.length == 0 ? (
    <div style={styles.hexEmptyContainer} data-testid="empty">
      <Typography fontFamily={'Open Sans'} variant="h5">
        {projectMessage}&quot;{projectName}&quot;
      </Typography>
    </div>
  ) : (
    <>
      <Box sx={styles.hexContainer} data-testid="empty">
        <FolderIcon style={{ fontSize: '60px', color: '#D3D3D3', marginBottom: '10px' }} />
        <Typography fontFamily={'Open Sans'} variant="subtitle1" fontWeight={700} paddingBottom={'10px'} data-testid="no-document">
          {t('noProjectsHeading')}
        </Typography>
        <Box sx={{ marginBottom: '40px' }}>
          <Typography data-testid="no-document-content" sx={styles.hexText}>
            {t('noProjectsSubText')}
          </Typography>
        </Box>
        <Box>
          <Box sx={styles.headingContainer}>
            <Typography variant="h5" sx={styles.heading} data-testid="try-out-nexus">
              {t('tryOutNexus')}
            </Typography>
            <Button variant="text" endIcon={<LaunchOutlinedIcon />} onClick={openExternalLink(NEXUS_APPS_URL)} sx={styles.heading} data-testid="findMoreapps">
              {t('findMoreapps')}
            </Button>
          </Box>
          <Box sx={styles.hexHelpfulResourcesContainer}>
            {productCards.map((cardProps, index) => (
              <HexProductCard key={index} {...cardProps} />
            ))}
          </Box>
          <Box sx={styles.headingContainer}>
            <Typography variant="h5" sx={styles.heading} data-testid="helpful-resources">
              {t('helpfulResources')}
            </Typography>
          </Box>
        </Box>
        <Box sx={styles.hexHelpfulResourcesContainer}>
          {plainCardProps.map((props, index) => (
            <PlainCard key={index} {...props} sx={styles.hexHelpfulResourcesCard} />
          ))}
        </Box>
      </Box>
    </>
  );
};
