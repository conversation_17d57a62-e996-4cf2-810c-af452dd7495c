import { SxProps, Theme } from '@mui/material';

export const styles: SxProps<Theme> | any = {
  hexEmptyContainer: {
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    color: 'text.primary',
    mt: 2
  },
  hexContainer: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    margin: '2% 12% 12% 12%'
  },
  hexText: {
    fontWeight: '400',
    fontSize: '14px',
    color: 'text.secondary',
    width: '100%',
    textAlign: 'center',
    paddingBottom: '30px'
  },
  hexTileContainer: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: '10px',
    marginTop: '10px',
    width: '100%'
  },
  hexMobileContainer: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'column',
    padding: '30px',
    backgroundColor: '#F3F3F3',
    height: 'calc(100vh - 340px)'
  },
  hexDesktopContainer: {
    width: '650px',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'column',
    borderRadius: '8px',
    padding: '60px 90px 60px 90px',
    height: '400px'
  },
  hexHelpfulResourcesContainer: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    marginBottom: '40px',
    boxSizing: 'border-box',
    '@media (min-width: 600px)': {
      gap: '20px'
    },
    '@media (min-width: 1100px)': {
      flexDirection: 'row',
      justifyContent: 'space-between',
      display: 'flex',
      gap: '20px'
    }
  },
  hexHelpfulResourcesCard: {
    width: '100%',
    height: '100%',
    marginBottom: '24px',
    borderColor: (theme: Theme) => theme.palette.divider,
    color: (theme: Theme) => theme.palette.text.primary,
    backgroundColor: (theme: Theme) => (theme.palette.mode !== 'dark' ? `rgba(0, 0, 0, 0.04)` : theme.palette.grey[700]),

    '@media (min-width: 1200px)': {
      width: '300px',
      height: '350px',
      padding: '12px'
    }
  },
  headingContainer: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: '100%',
    marginBottom: '10px'
  },

  heading: {
    fontWeight: 'bold',
    textAlign: 'left',
    marginBottom: '10px'
  },

  Container: {
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'space-between',
    width: '100%'
  }
};
