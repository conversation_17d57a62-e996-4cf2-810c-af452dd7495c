import React, { render, screen } from '@testing-library/react';
import { vi, describe, it, expect } from 'vitest';
import { HexEmptyProject } from './HexEmptyProject';
import { IProject } from '../../models/Project';

vi.mock('@nexusplatform/react', () => ({
  useHexAuth: () => ({
    getAccessTokenSilently: vi.fn().mockResolvedValue('mock-token')
  })
}));

describe('<HexEmptyProject/>', () => {
  const projects: IProject[] = [];
  const containerHeight = 0;
  Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: vi.fn().mockImplementation((query) => ({
      matches: false,
      media: query,
      onchange: null,
      addListener: vi.fn(), // Deprecated
      removeListener: vi.fn(), // Deprecated
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
      dispatchEvent: vi.fn()
    }))
  });

  it('should render component', () => {
    render(<HexEmptyProject projectName="prjectname" projects={projects} containerHeight={containerHeight} />);
    const card = screen.getByTestId('empty');
    expect(card).toBeInTheDocument();
  });
  it('display helpful resources section', () => {
    render(<HexEmptyProject projectName="" projects={projects} containerHeight={containerHeight} />);
    const helpfulResources = screen.getByTestId('helpful-resources');
    expect(helpfulResources).toBeInTheDocument();
  });

  it('display try out nexus section', () => {
    render(<HexEmptyProject projectName="" projects={projects} containerHeight={containerHeight} />);
    const tryOutNexus = screen.getByTestId('try-out-nexus');
    expect(tryOutNexus).toBeInTheDocument();
  });
});
