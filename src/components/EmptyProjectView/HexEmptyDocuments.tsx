import React, { useContext } from 'react';
import { Typography, Button, useMediaQuery } from '@mui/material';
import { styles } from './HexEmptyProject.styles';
import { IProject } from '../../models/Project';
import { Box } from '@mui/material';
import { TileComponent } from '../TileComponent/TileComponent';
import { useTranslation } from 'react-i18next';
import { BASE_APP_PATH, SILENT_AUTH } from '../../configuration/URL.config';
import grey from '/src/assets/images/greyTile.svg';
import green from '/src/assets/images/greenTile.svg';
import blue from '/src/assets/images/blueTile.svg';
import brown from '/src/assets/images/brownTile.svg';
import red from '/src/assets/images/redTile.svg';
import NoDocument from '/src/assets/images/NoDocument.svg';
import CardMedia from '/src/assets/images/CardMedia.svg';
import { TagContext } from '../../App';
import { isProductionSoftwareApplication } from '../../utils/IsPointSolution';

import materialsConnect from '/src/assets/images/materialsConnectTile.svg';
import whiteBoard from '/src/assets/images/whiteBoardTile.svg';
export interface IHexEmptyDocuments {
  projectName?: string;
  projects?: Array<IProject>;
  containerHeight?: number | undefined;
  createDemoProject?: () => void;
}
/**
 * @param {IHexEmptyDocuments} props - provides the properties fo React component
 * @return {ReactNode} - provides the react component to be ingested
 **/
export const HexEmptyDocuments: React.FC<IHexEmptyDocuments> = ({ projectName, projects, containerHeight, createDemoProject }: IHexEmptyDocuments) => {
  const { t } = useTranslation();
  const projectMessage = `${t('noSearchResultFor')}`;
  const isMobile = useMediaQuery('(max-width:900px)');
  const selectedApp = useContext<string>(TagContext);

  return projectName && projects?.length == 0 ? (
    <div style={styles.hexEmptyContainer} data-testid="empty">
      <Typography fontFamily={'Open Sans'} variant="h5">
        {projectMessage}&quot;{projectName}&quot;
      </Typography>
    </div>
  ) : (
    <>
      {isMobile && (
        <Box sx={styles.hexMobileContainer}>
          <Typography fontFamily={'Open Sans'} variant="subtitle2" fontWeight={600}>
            {t('noDocumentsText')}
          </Typography>
          <Typography fontFamily={'Open Sans'} variant="body2" fontWeight={400} margin="10px 0px 20px 0px">
            {t('noDocumentsBody')}
          </Typography>
          {!isProductionSoftwareApplication(selectedApp) && (
            <Button variant="outlined" onClick={createDemoProject} color={'secondary'}>
              {t('createSampleDocument')}
            </Button>
          )}
        </Box>
      )}
      <Box sx={styles.hexContainer} data-testid="empty" height={containerHeight ? containerHeight - 57 : 0}>
        {!isMobile && (
          <Box sx={styles.hexDesktopContainer} style={{ backgroundImage: `url(${NoDocument})` }}>
            <Typography fontFamily={'Open Sans'} variant="h6" fontWeight={600} color={'#FFFFFF'} paddingBottom={'10px'} data-testid="noDocumentText">
              {t('noDocumentsText')}
            </Typography>
            <Typography fontFamily={'Open Sans'} variant="subtitle2" fontWeight={400} color={'#FFFFFF'} paddingBottom={'20px'}>
              {t('noDocumentsBody')}
            </Typography>
            <Button variant="outlined" onClick={createDemoProject} sx={{ color: '#FFFFFF', borderColor: '#FFFFFF' }}>
              {t('createSampleDocument')}
            </Button>
          </Box>
        )}
        {!isMobile && (
          <Box sx={{ width: '100%', display: 'flex', justifyContent: 'center', alignItems: 'center', flexDirection: 'column' }}>
            <Typography fontFamily={'Open Sans'} variant="h6" fontWeight={700} sx={{ marginTop: '30px', marginBottom: '10px' }}>
              {t('exploreNexusSolutions')}
            </Typography>
            <Box data-testid="tile-container">
              <Box sx={styles.hexTileContainer}>
                <TileComponent
                  title={t('AdditiveTileText')}
                  link={`${BASE_APP_PATH}/home/<USER>/solutions-for-additive-manufacturing${SILENT_AUTH}`}
                  Description={t('learnMore')}
                  style={{ backgroundImage: `url(${CardMedia})` }}
                />
                <TileComponent
                  title={t('MetrologyTileText')}
                  link={`${BASE_APP_PATH}/home/<USER>/metrology-reporting${SILENT_AUTH}`}
                  Description={t('learnMore')}
                  style={{ backgroundImage: `url(${green})` }}
                />
                <TileComponent
                  title={t('MetrologyAssetManagerTileText')}
                  link={`${BASE_APP_PATH}/home/<USER>/metrology-asset-manager${SILENT_AUTH}`}
                  Description={t('learnMore')}
                  style={{ backgroundImage: `url(${blue})` }}
                />
              </Box>
              <Box sx={styles.hexTileContainer}>
                <TileComponent
                  title={t('MaterialsEnrichTileText')}
                  link={`${BASE_APP_PATH}/home/<USER>/nexus-materials-enrich${SILENT_AUTH}`}
                  Description={t('learnMore')}
                  style={{ backgroundImage: `url(${brown})` }}
                />
                <TileComponent
                  title={t('ComputeTileText')}
                  link={`${BASE_APP_PATH}/home/<USER>/nexus-compute${SILENT_AUTH}`}
                  Description={t('learnMore')}
                  style={{ backgroundImage: `url(${red})` }}
                />
                <TileComponent
                  title={t('MaterialsConnectTileText')}
                  link={`${BASE_APP_PATH}/home/<USER>/nexus-materials-connect${SILENT_AUTH}`}
                  Description={t('learnMore')}
                  style={{ backgroundImage: `url(${materialsConnect})` }}
                />
              </Box>
              <Box sx={styles.hexTileContainer}>
                <TileComponent
                  title={t('3dWhiteBoardTileText')}
                  link={`${BASE_APP_PATH}/home/<USER>/3d-whiteboard${SILENT_AUTH}`}
                  Description={t('learnMore')}
                  style={{ backgroundImage: `url(${whiteBoard})` }}
                />
              </Box>
              <Box sx={styles.hexTileContainer}>
                <TileComponent title={t('MetrologyMentorTileText')} Description={t('comingSoon')} style={{ backgroundImage: `url(${grey})` }} />
                <TileComponent title={t('MetrologyExecuteTileText')} Description={t('comingSoon')} style={{ backgroundImage: `url(${grey})` }} />
                <TileComponent title={t('CuttingAndBendingLibrariesTileText')} Description={t('comingSoon')} style={{ backgroundImage: `url(${grey})` }} />
              </Box>
              <Box sx={styles.hexTileContainer}>
                <TileComponent title={t('PlasticTileText')} Description={t('comingSoon')} style={{ backgroundImage: `url(${grey})` }} />
                <TileComponent title={t('SheetMetalTileText')} Description={t('comingSoon')} style={{ backgroundImage: `url(${grey})` }} />
                <TileComponent title={t('AdamsCarTileText')} Description={t('comingSoon')} style={{ backgroundImage: `url(${grey})` }} />
              </Box>
              <Box sx={styles.hexTileContainer}>
                <TileComponent title={t('ProplanAiTileText')} Description={t('comingSoon')} style={{ backgroundImage: `url(${grey})` }} />
                <TileComponent title={''} Description={t('manyMoreTileText')} style={{ backgroundImage: `url(${grey})` }} />
              </Box>
            </Box>
          </Box>
        )}
      </Box>
    </>
  );
};
