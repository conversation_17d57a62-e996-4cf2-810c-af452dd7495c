import { render } from '@testing-library/react';
import React from 'react';
import { TileComponent, TileComponentProps } from './TileComponent';

describe('TileComponent', () => {
  const props: TileComponentProps = {
    title: 'Additive Manufacturing Solution',
    link: 'https://dev.nexus.hexagon.com/home/<USER>/simufact-additive/',
    Description: 'Learn More'
  };
  it('should render text', async () => {
    const component = render(<TileComponent {...props} />);
    expect(component.getByText('Additive Manufacturing Solution')).toBeInTheDocument;
    expect(component.getByText('Learn More')).toBeInTheDocument;
  });
});
