import React from 'react';
import { Typography, Link, Box } from '@mui/material';
import './TileComponent.css';

export interface TileComponentProps {
  title: string;
  link?: string;
  Description: string;
  style?: any;
}

export const TileComponent: React.FC<TileComponentProps> = ({ title, link, Description, style }: TileComponentProps) => {
  return (
    <Box className="hex-card-container" style={style}>
      <Box sx={{ paddingTop: '25px', height: '120px' }}>
        <Typography fontWeight={400} color={'#FFFFFF'} width={'75%'} fontSize={'16px'} fontFamily={'Open Sans'} marginBottom="15px">
          {title}
        </Typography>
        <Link fontWeight={700} color={'#FFFFFF'} fontFamily={'Open Sans'} href={link} target="_blank" underline="none" variant="body2">
          {Description}
        </Link>
      </Box>
    </Box>
  );
};
