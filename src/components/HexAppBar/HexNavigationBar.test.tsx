import React, { act, fireEvent, render, screen } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import HexNavigationBar from './HexNavigationBar';
import { BrowserRouter } from 'react-router-dom';

const mockedUsedNavigate = vi.fn();

vi.mock('react-router-dom', () => ({
  ...(vi.importActual('react-router-dom') as any),
  useNavigate: () => mockedUsedNavigate
}));

vi.mock('@nexusplatform/react', () => ({
  useHexAuth: () => ({
    getAccessTokenSilently: vi.fn().mockResolvedValue('mock-token')
  })
}));

// TODO: Need to fix some cases are failing
describe.skip('<HexNavigationBar/>', () => {
  const data = {
    title: 'string',
    user: {
      given_name: 'suren',
      family_name: 'm',
      sub: 'waad|2y3odJ3LX5IsK3CXluD5Ia6M0dQNWAAaMLpliBF_eOA',
      status: 'active'
    },
    logout: vi.fn(),
    getAccessTokenSilently: vi.fn(),
    viewSwitcher: false,
    setViewSwitcher: vi.fn()
  };
  it('test for AppBar', async () => {
    await act(async () => {
      const component = render(
        <BrowserRouter>
          <HexNavigationBar {...data} />
        </BrowserRouter>
      );
    });
    const component = screen.getByTestId('Appbar');
    expect(component).toBeInTheDocument;
  });
  it('test for Avatar', async () => {
    render(
      <BrowserRouter>
        <HexNavigationBar {...data} />
      </BrowserRouter>
    );
    const avatar = screen.getByTestId('avatar');
    fireEvent.click(avatar);
  });

  // it('test language button', async () => {
  //   render(
  //     <BrowserRouter>
  //       <HexNavigationBar {...data} />
  //     </BrowserRouter>
  //   );
  //   const lang = screen.getByTestId('langChange');
  //   fireEvent.click(lang);
  //   const englishOptn = await screen.findByTestId('language-menu-0');
  //   fireEvent.click(englishOptn);
  // });
  it('test Acount dropdown', async () => {
    render(
      <BrowserRouter>
        <HexNavigationBar {...data} />
      </BrowserRouter>
    );
    const avatar = screen.getByTestId('avatar');
    fireEvent.click(avatar);
    expect(screen.getByTestId('test-accountDropdown')).toBeInTheDocument();
  });
  it('should Navigate to organization', async () => {
    render(
      <BrowserRouter>
        <HexNavigationBar {...data} />
      </BrowserRouter>
    );
    const avatar = screen.getByTestId('avatar');
    fireEvent.click(avatar);
    const manageOrgBtn = screen.getByTestId('Manage Organizations-title');
    expect(manageOrgBtn).toBeInTheDocument();
    fireEvent.click(manageOrgBtn);
    expect(mockedUsedNavigate).toBeCalled();
  });

  it('should Signout', async () => {
    render(
      <BrowserRouter>
        <HexNavigationBar {...data} />
      </BrowserRouter>
    );

    const avatar = screen.getByTestId('avatar');
    fireEvent.click(avatar);
    const signoutBtn = screen.getByTestId('signOut-btn');
    expect(signoutBtn).toBeInTheDocument();
    fireEvent.click(signoutBtn);
    expect(data.logout).toBeCalled();
  });
});
