import React, { useCallback, useEffect, useLayoutEffect, useMemo, useState } from 'react';
import { AppBar, Menu, MenuItem, Switch, Tooltip, Badge, Typography, Divider, useTheme } from '@mui/material';
import nexusByHexagonDark from '/src/assets/images/nexus_by_hexagon_dark.svg';
import nexusByHexagonLight from '/src/assets/images/nexus_by_hexagon_light.svg';
import { NavBar, ActionGroup, StatusAvatar } from '@nexusui/components';
import { useNavigate, useSearchParams, useLocation } from 'react-router-dom';
import { OidcStandardClaims as User } from 'oidc-client-ts';
import { ApplicationVariant, AppTagData } from '../../utils/AppVariant';
import { PLASTIC_APPLICATIONS, PLATFORM_APP_VERSION } from '../../configuration/application.confg';
import { Apps, Language } from '@mui/icons-material';
import { BASE_APP_PATH, BASE_URL, ORGANIZATION_NAME } from '../../configuration/URL.config';
import { useTranslation } from 'react-i18next';
import { cleanSensitiveInfo, languageDict, LanguageOption } from '../../utils/helper';
import { LanguageType } from '@nexusui/theme';
import LightModeOutlinedIcon from '@mui/icons-material/LightModeOutlined';
import DarkModeOutlinedIcon from '@mui/icons-material/DarkModeOutlined';
import { useDispatch, useSelector } from 'react-redux';
import { useFlags } from 'launchdarkly-react-client-sdk';
import { changeLanguage, changeTheme } from '../../redux/slices/userProfile';
import { RootState } from '../../redux';
import NotificationsNoneIcon from '@mui/icons-material/NotificationsNone';
import { ConnectedAccountDropdown, ConnectedAppSwitcher, ConnectedNotificationsPanel, INotification, useAuthContext, useConfigSignalR } from '@nexusui/connected-components';
import { useColorScheme } from '@mui/material/styles';
import HelpOutlineIcon from '@mui/icons-material/HelpOutline';
import { styles } from './HexNavigation.styles';
import IntentoPlaceholder from '../LanguageSelector/IntentoPlaceHolder';
import { isProductionSoftwareApplication } from '../../utils/IsPointSolution';
import { debounce } from '../../utils/Debounce';
import axios, { RawAxiosRequestHeaders } from 'axios';
import { AMS_AUDIENCE } from '../../configuration/AUTH.config';
import { useHexAuth } from '@nexusplatform/react';

export interface IHexNavBar {
  title?: string;
  user: User;
  logout?: () => void;
  viewSwitcher: boolean;
  setViewSwitcher: (v: boolean) => void;
}

/**
 * @param {name} name - provides the user full name
 * @return {string} - provides the user name initials
 **/
export const stringAvatar = (name: string) => {
  if (name.split(' ').length >= 2)
    return {
      children: name ? `${name.split(' ')[0][0] ?? ''}${name.split(' ')[1][0] ?? ''}` : ''
    };
  else {
    return {
      children: name ? `${name.split(' ')[0][0] ?? ''}` : ''
    };
  }
};

const HexNavigationBar = (props: IHexNavBar) => {
  const location = useLocation();
  const language = useSelector<RootState, LanguageType>((state) => state.userProfile.language);
  const themeMode = useSelector((state: RootState) => state.userProfile.theme);
  const { mode, setMode } = useColorScheme();
  const dispatch = useDispatch();
  const authContext = useAuthContext();
  const { getAccessTokenSilently } = useHexAuth();

  const { user, logout, viewSwitcher, setViewSwitcher } = props;

  const [anchorElAppSwitcher, setAnchorElAppSwitcher] = React.useState<HTMLButtonElement | null>(null);
  const openAppSwitcher = Boolean(anchorElAppSwitcher);

  const appBarElevation = 1;
  const navigate = useNavigate();

  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const [helpIconAnchorE2, setHelpIconAnchorE2] = React.useState<null | HTMLElement>(null);
  const [languageAnchorEl, setLanguageAnchorEl] = React.useState<null | HTMLElement>(null);

  const languageMenuOpen = Boolean(languageAnchorEl);

  const { t, i18n } = useTranslation();

  const [languages, setLanguages] = React.useState<LanguageOption[]>([]);
  const open = Boolean(anchorEl);
  const openHelpIcon = Boolean(helpIconAnchorE2);
  const defaultUserOrgName = user[ORGANIZATION_NAME];

  const [unreadNumber, setUnreadNumber] = useState(0);
  const [newNotification, setNewNotification] = useState<INotification | undefined>(undefined);

  const { signalRConnection } = useConfigSignalR(BASE_APP_PATH);
  const theme = useTheme();
  const iconColor = { dark: 'grey.400', light: 'grey.600' };
  const [isMobile, setIsMobile] = useState<boolean>(window.innerWidth < 900);

  useEffect(() => {
    const listenSignalR = () => {
      if (!signalRConnection) {
        return;
      }
      signalRConnection.on('newMessage', (comingMessage: any) => {
        setNewNotification(comingMessage);
      });
    };
    listenSignalR();
    return () => {
      signalRConnection && signalRConnection.off('newMessage');
    };
  }, [signalRConnection]);

  const [notificationsAnchorEl, setNotificationsAnchorEl] = useState<HTMLButtonElement | null>(null);
  const [query] = useSearchParams();
  const appTag = query.get('app');
  const urlParams = new URLSearchParams(new URL(window.location.href.toString()).search);
  const appVariantId = urlParams.get('appVariant');
  const [openNotificationPanel, setOpenNotificationPanel] = useState<boolean>(false);
  const [urls, setUrls] = useState({
    communityForumsUrl: '',
    documentationUrl: '',
    requestHelpUrl: ''
  });

  const {
    platformChangeLanguageButtonVisible,
    platformKoreanLanguagesMenuItemVisible,
    platformJapaneseLanguageMenuItemVisible,
    platformProjectFolderViewButtonVisible,
    platformInAppNotificationButtonVisible,
    platformIntentoWidgetVisible
  } = useFlags();

  // For extra language control
  useEffect(() => {
    let languages: LanguageOption[] = languageDict;
    if (platformKoreanLanguagesMenuItemVisible) {
      languages = [...languages, { value: 'ko', title: 'Korean' }];
    }
    if (platformJapaneseLanguageMenuItemVisible) {
      languages = [...languages, { value: 'ja', title: 'Japanese' }];
    }
    setLanguages(languages);
  }, [platformKoreanLanguagesMenuItemVisible, platformJapaneseLanguageMenuItemVisible]);

  useEffect(() => {
    const toggle = sessionStorage.getItem('toggle');
    if (toggle == 'true') {
      setViewSwitcher(true);
    }
  }, [setViewSwitcher]);

  const handleClick = (event: React.MouseEvent<any>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleHelpIconClick = (event: React.MouseEvent<any>) => {
    setHelpIconAnchorE2(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleHelpIconClose = () => {
    setHelpIconAnchorE2(null);
  };

  const handleLogout = () => {
    cleanSensitiveInfo();
    const urlBeforeLogout: string = window.location.href;
    if (urlBeforeLogout.indexOf('filter=') !== -1) {
      window.sessionStorage.setItem('platformRedirectUrl', urlBeforeLogout);
    }
    if (logout) {
      logout();
    }
  };

  const handleAppSwitcherOpen = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorElAppSwitcher(event.currentTarget);
  };

  const handleAppSwitcherClose = () => {
    setAnchorElAppSwitcher(null);
  };

  const handleCloseLanguage = () => {
    setLanguageAnchorEl(null);
  };

  const onChangeLanguage = (option: LanguageOption) => {
    const language = option.value;
    setLanguageAnchorEl(null);
    i18n.changeLanguage(language);
    dispatch(changeLanguage(language));
  };

  const handleSelectLanguageClicked = (event: React.MouseEvent<HTMLElement>) => {
    setLanguageAnchorEl(event.currentTarget);
  };

  const handleNotificationsClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setNotificationsAnchorEl(event.currentTarget);
    setOpenNotificationPanel((prevState) => !prevState);
  };
  const onClose = () => {
    setNotificationsAnchorEl(null);
    setOpenNotificationPanel((prevState) => !prevState);
  };

  const handleThemeClick = () => {
    setMode(mode === 'light' ? 'dark' : 'light');
    dispatch(changeTheme(themeMode === 'light' ? 'dark' : 'light'));
  };

  interface MenuOptions {
    label: string;
    icon: any;
    onClick: any;
  }

  const menus: MenuOptions[] = [
    {
      label: t('changeTheme'),
      icon: mode === 'light' ? <DarkModeOutlinedIcon /> : <LightModeOutlinedIcon />,
      onClick: handleThemeClick
    }
  ];
  const getActions = () => {
    if (appTag) {
      menus.push({
        label: 'Help Menu',
        icon: <HelpOutlineIcon sx={{ color: iconColor.light, [theme.getColorSchemeSelector('dark')]: { color: iconColor.dark } }} />,
        onClick: handleHelpIconClick
      });
    }
    if (platformInAppNotificationButtonVisible) {
      menus.push({
        label: t('notifications'),
        icon: (
          <Badge color="secondary" badgeContent={unreadNumber}>
            <NotificationsNoneIcon />
          </Badge>
        ),
        onClick: handleNotificationsClick
      });
    }
    if (platformChangeLanguageButtonVisible) {
      menus.push(
        {
          label: '',
          icon: <IntentoPlaceholder />,
          onClick: () => {
            /* Placeholder for Intento widget interaction */
          }
        },
        ...(!platformIntentoWidgetVisible
          ? [
              {
                label: t('changeLanguage'),
                icon: <Language />,
                onClick: handleSelectLanguageClicked
              }
            ]
          : [])
      );

      // TODO: Temporary fix for the app switcher menu item deu to limitation of CEF pop up windows
      // This will be removed when Plastics apps are allowing CEF pop up windows
      if (PLASTIC_APPLICATIONS && !(appTag && Object.prototype.hasOwnProperty.call(PLASTIC_APPLICATIONS, appTag))) {
        menus.push({
          label: t('viewAllAPPs'),
          icon: <Apps />,
          onClick: handleAppSwitcherOpen
        });
      }
    } else {
      menus.push({
        label: t('viewAllAPPs'),
        icon: <Apps />,
        onClick: handleAppSwitcherOpen
      });
    }

    return menus;
  };

  const handleSwitch = (event: React.ChangeEvent<HTMLInputElement>) => {
    sessionStorage.setItem('toggle', event.target.checked.toString());
    const pathname = location.pathname;
    if (pathname?.includes('/projectfolders/')) {
      if (!event.target.checked) {
        setViewSwitcher(event.target.checked);
        navigate('/');
      }
    }
    setViewSwitcher(event.target.checked);
  };

  const onNotificationsReadStatusChange = (unreadCount: number) => {
    setUnreadNumber(unreadCount);
  };

  const onOrganizationSwitch = (org) => {
    handleLogout();
  };

  useLayoutEffect(() => {
    const updateView = () => {
      const isMobileView = window.innerWidth < 900;
      setIsMobile(isMobileView);
    };
    const debounceMe = debounce(updateView, 250);
    window.addEventListener('resize', debounceMe);
    updateView();

    return () => window.removeEventListener('resize', debounceMe);
  }, []);

  const helpMenu = useMemo(
    () => (
      <Menu
        anchorEl={helpIconAnchorE2}
        id="help-menu"
        open={openHelpIcon}
        elevation={7}
        sx={{ mt: 1.5 }}
        onClose={handleHelpIconClose}
        onClick={handleHelpIconClose}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      >
        <MenuItem onClick={() => window.open(urls.documentationUrl)}>Documentation</MenuItem>
        <MenuItem onClick={() => window.open(urls.communityForumsUrl)}>Community Forums</MenuItem>
        <Divider />
        <MenuItem onClick={() => window.open(urls.requestHelpUrl)}>Request Help</MenuItem>
      </Menu>
    ),
    [helpIconAnchorE2, open]
  );

  useEffect(() => {
    async function fetchHelpUrls() {
      let helpUrls: { communityForumsUrl?: string; documentationUrl?: string; requestHelpUrl?: string } = {
        communityForumsUrl: '',
        documentationUrl: '',
        requestHelpUrl: ''
      };
      if (appTag) {
        if (appVariantId) {
          // Fetch variant-level URLs
          const appVariantData = await getAppVariantData(appTag, appVariantId);
          if (appVariantData) {
            helpUrls = {
              communityForumsUrl: appVariantData.communityForumsUrl,
              documentationUrl: appVariantData.documentationUrl,
              requestHelpUrl: appVariantData.requestHelpUrl
            };
          }
        }

        // If any URL is missing, fetch appTag-level URLs
        if (!helpUrls.communityForumsUrl || !helpUrls.documentationUrl || !helpUrls.requestHelpUrl) {
          const appTagUrls = await getAppTagData(appTag);
          if (appTagUrls) {
            helpUrls = {
              communityForumsUrl: helpUrls.communityForumsUrl || appTagUrls.communityForumsUrl,
              documentationUrl: helpUrls.documentationUrl || appTagUrls.documentationUrl,
              requestHelpUrl: helpUrls.requestHelpUrl || appTagUrls.requestHelpUrl
            };
          }
        }
        // Set URLs, falling back to defaults if necessary
        setUrls({
          communityForumsUrl: helpUrls.communityForumsUrl || 'https://nexus.hexagon.com/community',
          documentationUrl: helpUrls.documentationUrl || `${BASE_URL.replace('/api', '')}/documentationcenter/en-US/bundle`,
          requestHelpUrl: helpUrls.requestHelpUrl || `${BASE_URL.replace('/api', '')}/home/<USER>/support`
        });
      }
    }
    fetchHelpUrls();
  }, [appTag, appVariantId]);

  const getAmsCustomHeaders = async () => {
    let amsCustomHeaders: RawAxiosRequestHeaders;
    const JWT = await getAccessTokenSilently({ audience: AMS_AUDIENCE });
    if (JWT) {
      amsCustomHeaders = {
        authorization: `Bearer ${JWT}`
      };
      return amsCustomHeaders;
    }
    return undefined;
  };

  const getAppTagData = useCallback(async (appTag) => {
    try {
      const amsCustomHeaders = await getAmsCustomHeaders();
      const result = await axios.get(BASE_URL + '/ams/applications', {
        params: {
          q: `appTag:${appTag}`
        },
        headers: amsCustomHeaders
      });
      if (result.status == 200) {
        const appTagData: AppTagData = result.data[0];
        return appTagData;
      } else {
        return undefined;
      }
    } catch (e) {
      console.error('Error fetching app tag data:', e);
      return undefined;
    }
  }, []);

  const getAppVariantData = useCallback(async (appTag, appVariantId) => {
    try {
      const amsCustomHeaders = await getAmsCustomHeaders();
      const result = await axios.get(BASE_URL + `/ams/applications/${appTag}/variants/${appVariantId}`, { headers: amsCustomHeaders });
      if (result.status == 200) {
        const appVariantData: ApplicationVariant = result.data;
        return appVariantData;
      } else {
        return undefined;
      }
    } catch (e) {
      console.error('Error fetching app variant data:', e);
      return undefined;
    }
  }, []);

  return (
    <AppBar sx={styles.headerAppBar} position="relative" color={'inherit'} data-testid="Appbar" elevation={appBarElevation}>
      <NavBar appLogo={<img src={mode === 'dark' ? nexusByHexagonLight : nexusByHexagonDark} height={36} width={'auto'} alt={t('nexusByHexagon')} style={{ display: 'block' }} />}>
        {platformProjectFolderViewButtonVisible && !location.pathname.includes('/manage-extensions/') && !(appTag && isProductionSoftwareApplication(appTag as string)) && (
          <Tooltip title={t('toggleTooltip')}>
            <Switch checked={viewSwitcher} onChange={handleSwitch} />
          </Tooltip>
        )}
        {!location.pathname.includes('/manage-extensions/') && <ActionGroup sx={styles.intentoButton} max={isMobile ? 3 : 4} actions={getActions()} />}
        <Menu anchorEl={languageAnchorEl} open={languageMenuOpen} onClose={handleCloseLanguage}>
          {languages.map((option) => (
            <MenuItem key={option.value} value={option.value} selected={language === option.value} onClick={() => onChangeLanguage(option)}>
              {option.title}
            </MenuItem>
          ))}
        </Menu>
        <Tooltip title={defaultUserOrgName}>
          <Typography translate="no" noWrap variant="body2" sx={{ ml: 2, maxWidth: '133px', textOverflow: 'ellipsis', overflow: 'hidden', fontWeight: '700', fontSize: '1rem' }}>
            {defaultUserOrgName}
          </Typography>
        </Tooltip>
        <ConnectedAppSwitcher data-testid="appSwitcher" open={openAppSwitcher} anchorEl={anchorElAppSwitcher} onClose={handleAppSwitcherClose} />
        <StatusAvatar
          data-testid="avatar"
          firstName={authContext.userDetails?.given_name}
          lastName={authContext.userDetails?.family_name}
          onClick={handleClick}
          sx={{ ml: 4, cursor: 'pointer' }}
        />
        <ConnectedAccountDropdown
          version={PLATFORM_APP_VERSION}
          open={open}
          anchorEl={anchorEl}
          onClose={handleClose}
          onSignOut={handleLogout}
          onMenuItemClick={handleClose}
          data-testid="test-accountDropdown"
          sx={{ width: '300px' }}
          onOrganizationSwitch={onOrganizationSwitch}
        />
        <ConnectedNotificationsPanel
          disablePortal
          anchorEl={notificationsAnchorEl}
          open={openNotificationPanel}
          notificationMessage={newNotification}
          onClose={onClose}
          onNotificationsReadStatusChange={onNotificationsReadStatusChange}
        />
      </NavBar>
      {helpMenu}
    </AppBar>
  );
};

export default HexNavigationBar;
