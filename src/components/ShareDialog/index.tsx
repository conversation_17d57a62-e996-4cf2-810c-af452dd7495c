import React, { useEffect, useMemo, useState, useCallback, useRef } from 'react';
import { IShareUser, ShareDialog, Snackbar } from '@nexusui/components';
import { IPermissionLevel } from '@nexusui/components/core/ShareDialog/model';
import { IDocumentAcl } from '@nexusplatform/core-react-components';
import { IUser } from '@fluidframework/azure-client';
import {
  getFolderInstance,
  getSDCInstance,
  removeSDCUserOrOrgAcl,
  updateProjectUser,
  updateSDCUserOrOrgAcl,
  removeProjectUser,
  getProjectUserDetails,
  getCurrentOrganizationUsers
} from '../../services/shareDialog.service';
import { useTranslation } from 'react-i18next';
import { AxiosResponse } from 'axios';
import { t } from 'i18next';
import { useFlags } from 'launchdarkly-react-client-sdk';
import { IFolderList } from '../../models/Folders';
import { ORGANIZATION_NAME, ORG_ID_LOCATOR } from '../../configuration/URL.config';
import jwtDecode from 'jwt-decode';
import DomainIcon from '@mui/icons-material/Domain';
import { RawAxiosRequestHeaders } from 'axios';
import { AMS_AUDIENCE } from '../../configuration/AUTH.config';
import debounce from 'lodash/debounce';
import { useHexAuth } from '@nexusplatform/react';

interface ShareDialogComponentProps {
  show: boolean;
  authToken: string;
  onClose: () => void;
  instanceId: string;
  isProjectOrSDC?: string;
}

const defaultPermissionLevels: IPermissionLevel[] = [{ value: 'readWrite', label: t('editor') }];
const defaultPermissionLevelsForShare: IPermissionLevel[] = [
  { value: 'readWrite', label: t('editor') },
  { value: 'readOnly', label: t('viewer') }
];

const permissionLevelsForShareOrganization: IPermissionLevel[] = [
  { value: 'noAccess', label: t('noAccess') },
  { value: 'readOnly', label: t('viewer') },
  { value: 'readWrite', label: t('editor') }
];

const defaultProjectPermissionLevels: IPermissionLevel[] = [
  { value: 'readWrite', label: t('editor') },
  { value: 'readOnly', label: t('viewer') }
];

const projectPermissionLevelsOrganization: IPermissionLevel[] = [
  { value: 'noAccess', label: t('noAccess') },
  { value: 'readOnly', label: t('viewer') },
  { value: 'readWrite', label: t('editor') }
];

function ShareDialogComponent(props: ShareDialogComponentProps): JSX.Element {
  const { show, onClose, instanceId, authToken, isProjectOrSDC = 'SDC' } = props;
  const [invitedUsers, setInvitedUsers] = useState<IShareUser[]>([]);
  const [sharedWith, setSharedWith] = useState<Array<any>>([]);
  const [loader, setLoader] = useState(false);
  const [instanceData, setInstanceData] = useState<any>(null);
  const { t } = useTranslation();
  const [inviteMessage, setInviteMessage] = useState<string>(isProjectOrSDC == 'Project' ? t('inviteMessageProject') : t('inviteMessage'));
  const { platformDocumentSharePermissionButtonDisable, platformShareToOrganizationOptionVisible } = useFlags();
  const userIdsRef: any = useRef();
  const orgShareAccessRef = useRef<string>('');
  const loggedInUserRef = useRef<string>('');
  const [showOrgShare, setShowOrgShare] = useState<boolean>(false);
  const organizationNameRef = useRef('');
  const organizationIdRef = useRef<string>('');
  const [orgUsers, setOrgUsers] = useState<IShareUser[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const { getAccessTokenSilently } = useHexAuth();

  useEffect(() => {
    const decoded = jwtDecode<{ [key: string]: string }>(authToken as string);
    const organizationName = decoded[ORGANIZATION_NAME];
    const organizationId = decoded[ORG_ID_LOCATOR];
    organizationNameRef.current = organizationName;
    organizationIdRef.current = organizationId;
    loggedInUserRef.current = decoded.sub;
  }, [show, authToken]);

  let unresolvedUsers: string[];
  let blackListedUsers: string[];
  const customHeaders = useMemo(() => {
    return {
      authorization: `Bearer ${authToken}`
    };
  }, [authToken]);

  const getOrgUsers = useCallback(
    async (keyword?: string) => {
      setIsLoading(true);
      const JWT = await getAccessTokenSilently({ audience: AMS_AUDIENCE });
      const customHeaders: RawAxiosRequestHeaders = {
        authorization: `Bearer ${JWT}`
      };
      try {
        const fetchedOrgUsers = await getCurrentOrganizationUsers(customHeaders, keyword);
        const initialUsers: IShareUser[] = fetchedOrgUsers.data
          .filter((i) => i.status === 'Active')
          .map((user) => {
            return {
              key: user.id,
              id: user.id,
              email: user.email,
              avatar: user.avatar,
              firstName: user.firstName,
              lastName: user.lastName
            };
          });
        setOrgUsers(initialUsers);
      } catch (error: any) {
        Snackbar.error(error.response?.data?.message ?? error.message, { enableClose: true });
      } finally {
        setIsLoading(false);
      }
    },
    [getAccessTokenSilently]
  );

  const getInstance = useCallback(
    async (sdcInstanceId: string): Promise<void> => {
      setLoader(true);
      try {
        const data: any = await getSDCInstance(sdcInstanceId, customHeaders);
        const orgAcl = data?.acl?.filter((item: { organizationId: any }) => item.organizationId);
        orgShareAccessRef.current = orgAcl[0]?.access;
        setInstanceData(data);
      } catch (error: any) {
        Snackbar.error(error.response?.data?.message ?? error.message, { enableClose: true });
      } finally {
        setLoader(false);
      }
    },
    [customHeaders]
  );

  const getFolder = useCallback(
    async (folderInstanceId: string): Promise<void> => {
      if (isProjectOrSDC == 'Project') {
        setLoader(true);
        try {
          const data: IFolderList = await getFolderInstance(folderInstanceId, customHeaders);
          const res = data!.acl.map((item) => {
            return {
              ...item,
              access: item.defaultDocumentAccess
            };
          });
          data.acl = res;
          setInstanceData(data);
          getOrgUsers();
        } catch (error: any) {
          Snackbar.error(error.response?.data?.message ?? error.message, { enableClose: true });
        } finally {
          setLoader(false);
        }
      }
    },
    [customHeaders, isProjectOrSDC, getOrgUsers]
  );
  const updateUser = async (userId: string, permission: string, iId: string): Promise<void> => {
    setLoader(true);
    const users = sharedWith
      .filter((u) => u.id === userId)
      .map((u) => (isProjectOrSDC == 'Project' ? { email: u.email, access: permission, role: 'collaborator' } : { email: u.email, access: permission }));
    try {
      if (isProjectOrSDC == 'SDC' && instanceId) {
        await updateSDCUserOrOrgAcl(instanceId, users, customHeaders);
        await getInstance(instanceId);
      }
      if (isProjectOrSDC == 'Project' && instanceId) {
        await updateProjectUser(instanceId, users, true, true, customHeaders);
        await getFolder(instanceId);
      }
    } catch (error: any) {
      Snackbar.error(error.response?.data?.message ?? error.message ?? error.message, { enableClose: true });
    } finally {
      setLoader(false);
    }
  };

  const removeUser = async (userId: string, iId: string): Promise<void> => {
    setLoader(true);
    try {
      if (!iId) {
        return;
      }
      if (isProjectOrSDC == 'SDC') {
        const queryParams = { mode: 'hard' }; // passing the optional mode as "hard" to permanently remove the user Acl object from Acl array when remove option is choosen
        const data = { userIds: [userId] };
        await removeSDCUserOrOrgAcl(data, iId, customHeaders, queryParams);
        const acl = instanceData.acl.filter((u: { userId: string }) => u.userId !== userId);
        setInstanceData({ ...instanceData, acl });
        setSharedWith(sharedWith.filter((user) => user.id !== userId));
      } else {
        const response = await removeProjectUser(userId, iId, customHeaders);
        const acl = instanceData.acl.filter((u: { userId: string }) => u.userId !== userId);
        setInstanceData({ ...instanceData, acl });
        if (response.status === 206) {
          Snackbar.info(`${t('cannotShare') + ' ' + instanceData.name + ' ' + t('project')}`, { enableClose: true });
        } else {
          setSharedWith(sharedWith.filter((user) => user.id !== userId));
        }
      }
    } finally {
      setLoader(false);
    }
  };

  const updateOrgShare = async (permission: string, instanceId: string): Promise<void> => {
    setLoader(true);
    if (orgShareAccessRef.current !== permission) {
      orgShareAccessRef.current = permission;
      try {
        if (orgShareAccessRef.current && instanceId) {
          const orgshareAcl = [
            {
              type: 'organization',
              access: permission,
              organizationId: organizationIdRef.current
            }
          ];
          let response: AxiosResponse<any, any>;
          // if permission change is noAccess for org share then do a soft delete by changing org Acl access to noAccess
          if (permission !== 'noAccess') {
            response = await updateSDCUserOrOrgAcl(instanceId, orgshareAcl, customHeaders);
          } else {
            const data = { organizationId: organizationIdRef.current };
            response = await removeSDCUserOrOrgAcl(data, instanceId, customHeaders);
          }

          const responsePermission = response.data.resolved[0].access;
          if (response.status === 200) {
            if (responsePermission === 'readWrite') {
              Snackbar.success(`${t('shareDocumentOrgLevel') + ' ' + t('editor') + ' ' + t('access')}`, { enableClose: true });
            } else if (responsePermission === 'readOnly') {
              Snackbar.success(`${t('shareDocumentOrgLevel') + ' ' + t('viewer') + ' ' + t('access')}`, { enableClose: true });
            } else if (responsePermission === 'noAccess') {
              Snackbar.success(`${t('shareDocumentOrgLevel') + ' ' + t('noAccess')}`, { enableClose: true });
            }
          }
          await getInstance(instanceId);
        }
      } catch (error: any) {
        Snackbar.error(t('errorShareDocumentOrgLevel'), { enableClose: true });
      } finally {
        setLoader(false);
      }
    }
  };

  const onInvite = async (emails: string[], permission: string): Promise<void> => {
    const newUsers: IShareUser[] = emails
      .map((email) => {
        return {
          id: email,
          email,
          avatar: '',
          firstName: '',
          lastName: '',
          permissionLevel: permission,
          permissionEditable: true,
          permissionLevels: defaultPermissionLevels
        };
      })
      .filter((nu) => !invitedUsers.find((iu) => iu.email === nu.email));
    setLoader(true);
    const users = emails.map((email) => {
      const userObject = isProjectOrSDC == 'Project' ? { email: email, access: permission, role: 'collaborator' } : { email: email, access: permission };
      return userObject;
    });
    try {
      isProjectOrSDC == 'Project' ? setInviteMessage(t('inviteMessageProject')) : setInviteMessage(t('inviteMessage'));
      const response = (
        isProjectOrSDC == 'Project' ? await updateProjectUser(instanceId, users, true, true, customHeaders) : await updateSDCUserOrOrgAcl(instanceId, users, customHeaders)
      ) as AxiosResponse;

      if (response.data.unresolved.length > 0 || (response.data.blacklisted && response.data.blacklisted.length > 0)) {
        unresolvedUsers = response.data.unresolved.map((ele: string) => ele);
        if (response.data.blacklisted && response.data.blacklisted.length > 0) {
          blackListedUsers = response.data.blacklisted;
          blackListedUsers.length > 0 && setInviteMessage(t('blacklistedMessage') + `(${blackListedUsers.join(', ')}). ` + t('resendInviteMsg'));
        }
        unresolvedUsers.length > 0 &&
          (!blackListedUsers || (blackListedUsers && blackListedUsers.length === 0)) &&
          (isProjectOrSDC == 'Project'
            ? setInviteMessage(`${unresolvedUsers.join(', ')} ` + t('unresolvedInviteMessageProject'))
            : setInviteMessage(`${unresolvedUsers.join(', ')} ` + t('unresolvedInviteMessage')));
      }
      if (instanceId && isProjectOrSDC == 'SDC') await getInstance(instanceId);
      if (instanceId && isProjectOrSDC == 'Project') await getFolder(instanceId);
      if (!blackListedUsers || (blackListedUsers && blackListedUsers.length === 0)) {
        setInvitedUsers([...invitedUsers, ...newUsers]);
      }
    } catch (error: any) {
      Snackbar.error(isProjectOrSDC == 'Project' ? t('errorShareProject') : t('errorShareDocument'), { enableClose: true });
    } finally {
      setLoader(false);
    }
  };

  const getUserDetails = useCallback(
    async (userAccess: Array<IDocumentAcl>, instanceId: string): Promise<void> => {
      setLoader(true);
      try {
        const data = await getProjectUserDetails(instanceId, customHeaders);
        if (data.users) {
          setSharedWith(
            data.users
              .filter((user: IUser) => userAccess.some((usr: IDocumentAcl) => user.id === usr.userId))
              .map((user: IUser) => {
                const access = userAccess.find((usr: IDocumentAcl) => user.id === usr.userId)?.access;
                return {
                  ...user,
                  access
                };
              })
          );
        }
      } catch (error: any) {
        Snackbar.error(error.response?.data?.message ?? error.message ?? error.message, { enableClose: true });
      } finally {
        setLoader(false);
      }
    },
    [customHeaders]
  );

  const handleUserRemoved = (id: string): void => {
    removeUser(id, instanceId || '');
  };

  const handleUserPermissionChange = (id: string, permission: string): void => {
    updateUser(id, permission, instanceId);
    setSharedWith((current) => {
      const newUsers = [...current];
      const ind = current.findIndex((i) => i.id === id);
      newUsers.splice(ind, 1, { ...current[ind], access: permission });
      return newUsers;
    });
  };

  const handleUserResend = (id: string, permission: string): void => {
    updateUser(id, permission, instanceId);
  };

  const handleOrgPermissionChange = (permission) => {
    updateOrgShare(permission, instanceId);
  };

  // Added this here to handle the get InstanceData after adding a user
  useEffect(() => {
    if (instanceData && instanceData.acl && instanceData.acl.length > 0) {
      const userIds = instanceData.acl.filter((user: IDocumentAcl) => {
        return user.userId && user.userId !== instanceData.createdBy; // remove the org & owner Acl objects
      });
      userIdsRef.current = userIds;

      // check if the loggedIn user is the owner of the SDC document already shared with him & also if the document orgId, user orgId matches if he belongs to multiple orgs to show or hide org Share option
      if (loggedInUserRef.current === instanceData.createdBy && organizationIdRef.current === instanceData.organizationId) {
        setShowOrgShare(true);
      }

      if (userIds && userIds.length > 0) {
        getUserDetails(userIds, instanceData.id);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [instanceData]);

  useEffect(() => {
    if (sharedWith) {
      // if the loggedIn User is not the owner of the document then remove the users Acl object with noAccess if present in Acl array
      if (loggedInUserRef?.current !== instanceData?.createdBy) {
        // Indexes of User Acls to remove
        const indexes = sharedWith.reduce((acc, obj, i) => {
          if (obj.access === 'noAccess') {
            acc.push(i);
          }
          return acc;
        }, []);

        indexes.sort((a, b) => b - a); // Sort in descending order to avoid index shifting

        for (const index of indexes) {
          sharedWith.splice(index, 1); // Remove one item at the specified index
        }
      }

      const users = sharedWith.map((s) => {
        return {
          email: s.email,
          permissionLevel: s.access,
          id: s.id,
          avatar: '',
          firstName: '',
          lastName: '',
          permissionEditable: true,
          permissionLevels: showOrgShare
            ? permissionLevelsForShareOrganization.filter((ele: IPermissionLevel) => ele.value === s.access) // only owner should be able to see the shared users with noAccess
            : defaultPermissionLevelsForShare.filter((ele: IPermissionLevel) => ele.value === s.access),
          editable: true
        };
      });
      setInvitedUsers(users);
    }
  }, [sharedWith]);

  useEffect(() => {
    if (isProjectOrSDC == 'SDC' && instanceId) {
      getInstance(instanceId);
      setSharedWith([]);
    } else if (isProjectOrSDC == 'Project' && instanceId) {
      getFolder(instanceId);
      setSharedWith([]);
    }
    setSharedWith([]);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [instanceId]);

  let permissionLevels;
  if (isProjectOrSDC == 'Project') {
    permissionLevels = defaultProjectPermissionLevels;
  } else if (platformDocumentSharePermissionButtonDisable) {
    permissionLevels = defaultPermissionLevelsForShare;
  } else {
    permissionLevels = defaultPermissionLevels;
  }

  const handleSearch = async (keyword: string) => {
    if (keyword.length > 0) {
      await getOrgUsers(keyword);
    } else {
      getOrgUsers();
    }
  };

  return (
    <ShareDialog
      open={show}
      onClose={onClose}
      inviteConfig={{
        availableUsers: orgUsers,
        loading: isLoading,
        onInvite,
        onSearch: debounce(handleSearch, 500),
        canEditInvitePermission: !!platformDocumentSharePermissionButtonDisable,
        initialPermissionLevel: 'readWrite',
        inviteMessage: inviteMessage,
        permissionLevels: permissionLevels
      }}
      onUserRemoved={handleUserRemoved}
      users={invitedUsers}
      onUserPermissionChange={handleUserPermissionChange}
      loading={loader}
      onUserResend={handleUserResend}
      orgShareConfig={{
        visible: !!platformShareToOrganizationOptionVisible,
        editable: showOrgShare && !!platformShareToOrganizationOptionVisible,
        permissionLevel: orgShareAccessRef.current ? orgShareAccessRef.current : 'noAccess',
        orgOptions: [
          {
            value: organizationNameRef.current,
            label: t('anyoneFrom') + ` ${organizationNameRef.current}`,
            icon: <DomainIcon />
          }
        ],
        selectedOrg: organizationNameRef.current,
        permissionLevels: isProjectOrSDC == 'Project' ? projectPermissionLevelsOrganization : permissionLevelsForShareOrganization,
        onPermissionChange: (permission: string) => {
          handleOrgPermissionChange(permission);
        }
      }}
    />
  );
}

export default ShareDialogComponent;
