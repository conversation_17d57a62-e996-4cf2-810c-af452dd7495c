import { <PERSON>, Button, Icon<PERSON>utton, <PERSON>ack, Typography, Paper, Container, Switch, Menu, MenuItem } from '@mui/material';
import React, { useState, useEffect, useCallback } from 'react';
import { useParams } from 'react-router-dom';
import { DataGrid, GridColDef } from '@mui/x-data-grid';
import { EmptyState, ButtonTabs, ButtonTab, ConfirmationDialog, Snackbar } from '@nexusui/components';
import EditIcon from '@mui/icons-material/Edit';
import { BASE_URL } from '../configuration/URL.config';
import axios, { RawAxiosRequestHeaders } from 'axios';
import { AMS_AUDIENCE } from '../configuration/AUTH.config';
import { MapExtensionDialog } from './Extensions/MapExtensionDialog';
import AddIcon from '@mui/icons-material/Add';
import SettingsIcon from '@mui/icons-material/Settings';
import DeleteOutlineIcon from '@mui/icons-material/DeleteOutline';
import TocIcon from '@mui/icons-material/Toc';
import { AddBaseExtensionDialog } from './Extensions/AddBaseExtensionDialog';
import { VariantDialog } from './Extensions/VariantDialog';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import MoreVertIcon from '@mui/icons-material/MoreVert';
import { IExtension, IVariant } from '../configuration/extensions';
import { EditExtensionDialog } from './Extensions/EditExtensionDialog';
import { useTranslation } from 'react-i18next';
import { useHexAuth } from '@nexusplatform/react';

const ManageExtensions: React.FC = () => {
  const appName = useParams().selectedApp ?? '';
  const [appExtensions, setAppExtensions] = useState<IExtension[]>([]);
  const [showMapExtensionDialog, setShowMapExtensionDialog] = useState(false);
  const [showAddBaseExtensionDialog, setShowAddBaseExtensionDialog] = useState(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [baseExtensions, setBaseExtensions] = useState<IExtension[]>([]);
  const [selectedTab, setSelectedTab] = useState(0);
  const [showVariantDialog, setShowVariantDialog] = useState(false);
  const [variants, setVariants] = useState<IVariant[]>([]);
  const [showEditExtensionDialog, setShowEditExtensionDialog] = useState(false);
  const [selectedRow, setSelectedRow] = useState('');
  const [selectedVariant, setSelectedVariant] = useState<IVariant>();
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const { t } = useTranslation();
  const open = Boolean(anchorEl);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [selectedExtension, setSelectedExtension] = useState<IExtension>();
  const [allAppExtensions, setAllAppExtensions] = useState<IExtension[]>([]);
  const { getAccessTokenSilently } = useHexAuth();

  useEffect(() => {
    getAllAppExtensions();
    getAppExtensions();
    getBaseExtensions();
    getAppVariants();
    document.title = `Nexus`;
  }, []);

  function ExtensionsEmptyState() {
    return (
      <Container sx={{ height: 'calc(100vh - 450px)' }}>
        <EmptyState
          icon={<TocIcon sx={{ color: 'grey.300', fontSize: 72 }} />}
          header="Connect Nexus Apps and Extensions"
          description="These actions will appear in the Nexus Apps & Extensions list when you open the Nexus window in this app"
          actions={[
            <Button
              key="add-action-button"
              variant={'contained'}
              color={'primary'}
              onClick={() => {
                selectedTab === 0 ? setShowMapExtensionDialog(true) : setShowAddBaseExtensionDialog(true);
              }}
            >
              Add Action
            </Button>
          ]}
        />
      </Container>
    );
  }

  const handleClick = (event: React.MouseEvent<HTMLElement>, variant: IVariant) => {
    setSelectedVariant(variant);
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setShowMapExtensionDialog(false);
    setShowAddBaseExtensionDialog(false);
    setShowVariantDialog(false);
    setShowEditExtensionDialog(false);
    setAnchorEl(null);
    setShowDeleteDialog(false);
  };

  const handleButtonChange = (event: React.ChangeEvent<Record<string, never>>, newValue: number) => {
    setSelectedTab(newValue);
  };

  const handleDeleteVariant = async () => {
    const customHeaders = await getAmsCustomHeaders();
    const response = await axios.delete<any>(BASE_URL + `/ams/applications/${appName}/variants/${selectedVariant?.id}`, {
      headers: {
        ...customHeaders
      }
    });
    if (response.status === 200) {
      setSelectedTab(0);
      setVariants([...variants.filter((variant) => variant.id !== selectedVariant?.id)]);
      setShowDeleteDialog(false);
      Snackbar.success(`${t('variantDeleted')}`, { enableClose: true });
    } else {
      Snackbar.error(`${t('variantDeletedError')}`, { enableClose: true });
    }
    handleClose();
  };

  const handleDeleteAppExtension = async () => {
    const customHeaders = await getAmsCustomHeaders();
    const response = await axios.delete<any>(BASE_URL + `/ams/applications/${appName}/extensions/${selectedExtension?.id}`, {
      headers: {
        ...customHeaders
      }
    });
    if (response.status === 200) {
      setAppExtensions([...appExtensions.filter((ext) => ext.id !== selectedExtension?.id)]);
      setShowDeleteDialog(false);
      Snackbar.success(`${t('appExtensionsDeleted')}`, { enableClose: true });
    } else {
      Snackbar.error(`${t('appExtensionsDeletedError')}`, { enableClose: true });
    }
    handleClose();
  };
  const getAllAppExtensions = async () => {
    const customHeaders = await getAmsCustomHeaders();
    const allApps = await axios.get(BASE_URL + `/ams/applications/extensions?appTags=worknc,espritedge,designer,radanquoting`, { headers: customHeaders });
    const allAppExtensions = allApps.data;
    setAllAppExtensions(allAppExtensions);
  };

  const handleDeleteBaseExtension = async () => {
    const customHeaders = await getAmsCustomHeaders();
    if (allAppExtensions.some((ext) => ext.baseId === selectedExtension?.id)) {
      Snackbar.error(`${t('baseExtensionsDeleteError')}`, { enableClose: true });
      setShowDeleteDialog(false);
      return;
    }
    const response = await axios.delete<any>(BASE_URL + `/ams/applications/base/extensions/${selectedExtension?.id}`, {
      headers: {
        ...customHeaders
      }
    });
    if (response.status === 200) {
      setBaseExtensions([...baseExtensions.filter((ext) => ext.id !== selectedExtension?.id)]);
      setShowDeleteDialog(false);
      Snackbar.success(`${t('baseExtensionsDeleted')}`, { enableClose: true });
    } else {
      Snackbar.error(`${t('baseExtensionsDeletedError')}`, { enableClose: true });
    }
    handleClose();
  };

  const handleToggleChange = async (id: string) => {
    const extensionId = id;
    const variant = variants[selectedTab - 1];
    const customHeaders = await getAmsCustomHeaders();
    const extensionIndex = variant.extensions.findIndex((ext) => ext.id === extensionId);
    if (extensionIndex !== -1) {
      variant.extensions.splice(extensionIndex, 1);
    } else {
      variant.extensions.push({ id: extensionId, enabled: true });
    }
    const payloadData = {
      extensions: variant.extensions
    };
    const response = await axios.put<any>(BASE_URL + `/ams/applications/${appName}/variants/${variant.id}`, payloadData, {
      headers: {
        ...customHeaders
      }
    });
    if (response.status === 200) {
      setVariants([...variants]);
      Snackbar.success(`${t('variantUpdated')}`, { enableClose: true });
    } else {
      Snackbar.error(`${t('variantUpdatedError')}`, { enableClose: true });
    }
  };

  const handleToggleChangeSupport = async (id: string) => {
    const extensionId = id;
    const variant = variants[selectedTab - 1];
    const customHeaders = await getAmsCustomHeaders();
    const extensionIndex = variant.extensions.findIndex((ext) => ext.id === extensionId);
    if (extensionIndex !== -1) {
      variant.extensions[extensionIndex].enabled = !variant.extensions[extensionIndex].enabled;
    } else {
      Snackbar.error(`${t('variantUpdatedError')}`, { enableClose: true });
    }
    const payloadData = {
      extensions: variant.extensions
    };
    const response = await axios.put<any>(BASE_URL + `/ams/applications/${appName}/variants/${variant.id}`, payloadData, {
      headers: {
        ...customHeaders
      }
    });
    if (response.status === 200) {
      setVariants([...variants]);
      Snackbar.success(`${t('variantUpdated')}`, { enableClose: true });
    } else {
      Snackbar.error(`${t('variantUpdatedError')}`, { enableClose: true });
    }
  };

  const columns: GridColDef[] = [
    {
      field: 'show',
      headerName: 'Show/Hide',
      renderCell: (params) => (
        <Switch checked={!!variants[selectedTab - 1]?.extensions?.some((ext) => ext.id === params.row.id)} onChange={() => handleToggleChange(params.row.id)} />
      )
    },
    { field: 'name', headerName: 'Name' as string, flex: 1 },
    { field: 'description', headerName: 'Description' as string, flex: 1 },
    { field: 'openIn', headerName: 'Open In' as string, flex: 1 },
    { field: 'tag', headerName: 'Tag' as string, flex: 1 },
    {
      field: 'url',
      headerName: 'Url',
      flex: 1
    },
    {
      field: 'support',
      headerName: 'Supported',
      renderCell: (params) => (
        <Switch
          checked={!!variants[selectedTab - 1]?.extensions?.some((ext) => ext.id === params.row.id && ext.enabled)}
          onChange={() => handleToggleChangeSupport(params.row.id)}
        />
      )
    },
    {
      field: 'actions',
      headerName: '',
      disableColumnMenu: true,
      headerClassName: 'actions-header',
      sortable: false,
      align: 'center',
      minWidth: 100,
      renderCell: (params) => {
        return (
          <Stack direction="row">
            <IconButton
              onClick={() => {
                setShowEditExtensionDialog(true);
                setSelectedRow(params.row.id);
              }}
            >
              <EditIcon />
            </IconButton>
            <IconButton
              sx={{ color: 'red' }}
              onClick={() => {
                setSelectedExtension(params.row);
                setShowDeleteDialog(true);
              }}
            >
              <DeleteOutlineIcon />
            </IconButton>
          </Stack>
        );
      }
    }
  ];

  const onClickAddExtension = async (data) => {
    const payloadData = {
      baseId: data.id,
      overrides: { description: data.description, tag: data.tag, url: data.url, openIn: data.windowType }
    };
    const customHeaders = await getAmsCustomHeaders();
    setShowMapExtensionDialog(false);
    const response = await axios.post<any>(BASE_URL + `/ams/applications/${appName}/extensions`, payloadData, {
      headers: {
        ...customHeaders
      }
    });
    if (response.status === 201) {
      getAppExtensions();
      setShowMapExtensionDialog(false);
      Snackbar.success(`${t('appExtensionsAdded')}`, { enableClose: true });
    } else {
      Snackbar.error(`${t('appExtensionsAddedError')}`, { enableClose: true });
    }
  };

  const onClickAddBaseExtension = async (data) => {
    const payloadData = {
      name: data.name,
      description: data.description,
      tag: data.tag,
      url: data.url,
      openIn: data.windowType
    };
    const customHeaders = await getAmsCustomHeaders();
    setShowAddBaseExtensionDialog(false);
    const response = await axios.post<any>(BASE_URL + `/ams/applications/base/extensions`, payloadData, {
      headers: {
        ...customHeaders
      }
    });
    if (response.status === 201) {
      const newExtension = response.data;
      setBaseExtensions([...baseExtensions, newExtension]);
      setShowAddBaseExtensionDialog(false);
      Snackbar.success(`${t('baseExtensionsAdded')}`, { enableClose: true });
    } else {
      Snackbar.error(`${t('baseExtensionsAddedError')}`, { enableClose: true });
    }
  };

  const onClickAddVariant = async (data) => {
    const payloadData = {
      name: data.name,
      description: data.description,
      extensions: appExtensions.map((ext) => ({ id: ext.id, enabled: true })),
      documentationUrl: data.documentationUrl,
      communityForumsUrl: data.communityForumsUrl,
      requestHelpUrl: data.requestHelpUrl
    };
    const customHeaders = await getAmsCustomHeaders();
    setShowVariantDialog(false);
    const response = await axios.post<any>(BASE_URL + `/ams/applications/${appName}/variants`, payloadData, {
      headers: {
        ...customHeaders
      }
    });
    if (response.status === 201) {
      const newVariant = response.data;
      setVariants([...variants, newVariant]);
      setShowVariantDialog(false);
      Snackbar.success(`${t('variantAdded')}`, { enableClose: true });
    } else {
      Snackbar.error(`${t('variantAddedError')}`, { enableClose: true });
    }
  };

  const onEditVariant = async (data) => {
    const payloadData = {
      name: data.name,
      description: data.description,
      extensions: variants[selectedTab - 1]?.extensions.map((ext) => ({ id: ext.id, enabled: ext.enabled })),
      documentationUrl: data.documentationUrl,
      communityForumsUrl: data.communityForumsUrl,
      requestHelpUrl: data.requestHelpUrl
    };
    const customHeaders = await getAmsCustomHeaders();
    setShowVariantDialog(false);
    const response = await axios.put<any>(BASE_URL + `/ams/applications/${appName}/variants/${selectedVariant?.id}`, payloadData, {
      headers: {
        ...customHeaders
      }
    });
    if (response.status === 200) {
      const updatedVariant = response.data;
      setVariants([...variants.filter((variant) => variant.id !== selectedVariant?.id), updatedVariant]);
      setShowVariantDialog(false);
      setAnchorEl(null);
      Snackbar.success(`${t('variantUpdated')}`, { enableClose: true });
    } else {
      Snackbar.error(`${t('variantUpdatedError')}`, { enableClose: true });
    }
  };

  const onClickEditExtension = async (data) => {
    const selectedRowData: any = appExtensions.find((ext) => ext.id === selectedRow);
    const payloadData = {
      baseId: selectedRowData.baseId,
      overrides: { name: data.name, description: data.description, tag: data.tag, url: data.url, openIn: data.windowType }
    };
    const customHeaders = await getAmsCustomHeaders();
    setShowEditExtensionDialog(false);
    const response = await axios.put<any>(BASE_URL + `/ams/applications/${appName}/extensions/${selectedRow}`, payloadData, {
      headers: {
        ...customHeaders
      }
    });
    if (response.status === 200) {
      getAppExtensions();
      setShowEditExtensionDialog(false);
      Snackbar.success(`${t('appExtensionsUpdated')}`, { enableClose: true });
    } else {
      Snackbar.error(`${t('appExtensionsUpdatedError')}`, { enableClose: true });
    }
  };

  const onUpdateBaseExtension = async (data) => {
    const payloadData = {
      name: data.name,
      description: data.description,
      tag: data.tag,
      url: data.url,
      openIn: data.windowType
    };
    const customHeaders = await getAmsCustomHeaders();
    setShowEditExtensionDialog(false);
    const response = await axios.put<any>(BASE_URL + `/ams/applications/base/extensions/${selectedRow}`, payloadData, {
      headers: {
        ...customHeaders
      }
    });
    if (response.status === 200) {
      setBaseExtensions([...baseExtensions.filter((ext) => ext.id !== selectedRow), response.data]);
      setShowEditExtensionDialog(false);
      Snackbar.success(`${t('baseExtensionsUpdated')}`, { enableClose: true });
    } else {
      Snackbar.error(`${t('baseExtensionsUpdatedError')}`, { enableClose: true });
    }
  };

  const getAmsCustomHeaders = async () => {
    let amsCustomHeaders: RawAxiosRequestHeaders;
    const JWT = await getAccessTokenSilently({ audience: AMS_AUDIENCE });
    if (JWT) {
      amsCustomHeaders = {
        authorization: `Bearer ${JWT}`
      };
      return amsCustomHeaders;
    }
    return undefined;
  };
  const getAppExtensions = useCallback(async () => {
    const amsCustomHeaders = await getAmsCustomHeaders();
    const result = await axios.get(BASE_URL + `/ams/applications/extensions?appTags=${appName}`, { headers: amsCustomHeaders });
    const appExtensions = result.data;
    setAppExtensions(appExtensions);
    setIsLoading(false);
  }, []);

  const getBaseExtensions = useCallback(async () => {
    const amsCustomHeaders = await getAmsCustomHeaders();
    const result = await axios.get(BASE_URL + `/ams/applications/base/extensions`, { headers: amsCustomHeaders });
    const baseExtensions = result.data.data;
    setBaseExtensions(baseExtensions);
  }, []);

  const getAppVariants = useCallback(async () => {
    const amsCustomHeaders = await getAmsCustomHeaders();
    const result = await axios.get(BASE_URL + `/ams/applications/${appName}/variants`, { headers: amsCustomHeaders });
    const variants = result.data;
    setVariants(variants);
  }, []);

  return (
    <>
      <>
        <Box sx={{ pt: 10, pl: 13 }}>
          <Typography variant="h6">
            {t('allApps')}/{appName.toUpperCase()}
          </Typography>
        </Box>

        <Box sx={{ p: 13, pt: 10 }}>
          <Paper>
            <Box padding={'10px'}>
              <ButtonTabs value={selectedTab} onChange={handleButtonChange} selectionFollowsFocus>
                <ButtonTab label={`All actions`} value={0} />

                {variants.map((variant, index) => (
                  <ButtonTab key={index} label={variant.name} value={index + 1} onClick={() => setSelectedTab(index + 1)} />
                ))}

                <IconButton
                  onClick={() => {
                    setShowVariantDialog(true);
                    setSelectedVariant(undefined);
                  }}
                  data-testid="AddVariantIcon"
                >
                  <AddIcon />
                </IconButton>
                <ButtonTab label={<SettingsIcon />} value={10} sx={{ position: 'absolute', right: '0' }} />
              </ButtonTabs>
            </Box>
            {selectedTab === 0 && (
              <div style={{ height: 'calc(100vh - 300px)', width: '100%' }}>
                <Box sx={{ p: 2 }}>
                  <Button variant="contained" onClick={() => setShowMapExtensionDialog(true)}>
                    Add Extension
                  </Button>
                  <div style={{ height: 'calc(100vh - 350px)', width: '100%' }}>
                    <DataGrid
                      rows={appExtensions}
                      columns={columns}
                      loading={isLoading}
                      pageSize={10}
                      rowsPerPageOptions={[10]}
                      disableSelectionOnClick={true}
                      components={{
                        NoRowsOverlay: ExtensionsEmptyState
                      }}
                      columnVisibilityModel={{ show: false, support: false }}
                    />
                  </div>
                </Box>
              </div>
            )}
            {selectedTab === 10 && (
              <div style={{ height: 'calc(100vh - 300px)', width: '100%' }}>
                <Box sx={{ p: 2 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', padding: '10px' }}>
                    <Typography variant="h6">All apps and extensions default set here</Typography>
                    <Button variant="outlined" onClick={() => setShowAddBaseExtensionDialog(true)}>
                      Add Base Extension
                    </Button>
                  </Box>
                  <div style={{ height: 'calc(100vh - 350px)', width: '100%' }}>
                    <DataGrid
                      rows={baseExtensions}
                      columns={columns}
                      loading={isLoading}
                      pageSize={10}
                      rowsPerPageOptions={[10]}
                      disableSelectionOnClick={true}
                      components={{
                        NoRowsOverlay: ExtensionsEmptyState
                      }}
                      columnVisibilityModel={{ show: false, support: false }}
                    />
                  </div>
                </Box>
              </div>
            )}
            {selectedTab !== 0 && selectedTab !== 10 && (
              <div style={{ height: 'calc(100vh - 300px)', width: '100%' }}>
                <Box sx={{ p: 2 }}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', padding: '10px' }}>
                    <Stack direction="column">
                      <Typography variant="h6">{t('description')}</Typography>
                      <Typography variant="body1">{variants[selectedTab - 1].description}</Typography>
                    </Stack>
                    <Stack direction="column">
                      <Typography variant="h6">{t('variantId')}</Typography>
                      <Stack direction="row" alignItems="center" gap={1}>
                        <Typography variant="body1">{variants[selectedTab - 1].id}</Typography>
                        <IconButton color="primary" onClick={() => navigator.clipboard.writeText(variants[selectedTab - 1].id)}>
                          <ContentCopyIcon />
                          <Typography variant="body1">{t('copy')}</Typography>
                        </IconButton>
                      </Stack>
                    </Stack>
                    <IconButton onClick={(e) => handleClick(e, variants[selectedTab - 1])}>
                      <MoreVertIcon />
                    </IconButton>
                  </Box>
                  <div style={{ height: 'calc(100vh - 400px)', width: '100%' }}>
                    <DataGrid
                      rows={appExtensions}
                      columns={columns}
                      loading={isLoading}
                      pageSize={10}
                      rowsPerPageOptions={[10]}
                      disableSelectionOnClick={true}
                      columnVisibilityModel={{ actions: false }}
                    />
                  </div>
                </Box>
              </div>
            )}
          </Paper>
        </Box>
      </>
      <Menu
        id="variant-menu"
        MenuListProps={{
          'aria-labelledby': 'long-button'
        }}
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
      >
        <MenuItem
          onClick={() => {
            setShowVariantDialog(true);
          }}
        >
          {t('edit')}
        </MenuItem>
        <MenuItem
          onClick={() => {
            setShowDeleteDialog(true);
          }}
        >
          {t('delete')}
        </MenuItem>
      </Menu>
      {showMapExtensionDialog && <MapExtensionDialog showMapExtensionDialog={showMapExtensionDialog} onCancel={handleClose} onSave={onClickAddExtension} data={baseExtensions} />}
      {showAddBaseExtensionDialog && <AddBaseExtensionDialog showAddBaseExtensionDialog={showAddBaseExtensionDialog} onCancel={handleClose} onAdd={onClickAddBaseExtension} />}
      {showVariantDialog && (
        <VariantDialog showVariantDialog={showVariantDialog} onCancel={handleClose} onAdd={selectedVariant ? onEditVariant : onClickAddVariant} selectedVariant={selectedVariant} />
      )}
      {showEditExtensionDialog && (
        <EditExtensionDialog
          showEditExtensionDialog={showEditExtensionDialog}
          onCancel={handleClose}
          onUpdate={selectedTab === 0 ? onClickEditExtension : onUpdateBaseExtension}
          data={selectedTab === 0 ? appExtensions.find((ext) => ext.id === selectedRow) : baseExtensions.find((ext) => ext.id === selectedRow)}
        />
      )}
      {showDeleteDialog && (
        <ConfirmationDialog
          open={showDeleteDialog}
          title={`${t('delete')} ${selectedTab === 0 || selectedTab === 10 ? selectedExtension?.name : selectedVariant?.name}`}
          confirmAction={{
            color: 'error',
            children: `${t('delete')}`
          }}
          onClose={handleClose}
          onConfirm={selectedTab === 0 ? handleDeleteAppExtension : selectedTab === 10 ? handleDeleteBaseExtension : handleDeleteVariant}
          onCancel={() => setShowDeleteDialog(false)}
        >
          {t('deleteConfirmationExtensions')}
        </ConfirmationDialog>
      )}
    </>
  );
};

export default ManageExtensions;
