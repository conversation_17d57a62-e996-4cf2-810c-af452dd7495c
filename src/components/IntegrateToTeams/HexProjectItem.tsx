import { <PERSON>dr<PERSON>, Box, Button, <PERSON>, CardContent, CardMedia, Tooltip, Typography } from '@mui/material';
import { useTranslation } from 'react-i18next';

import { styles } from './HexProjectItem.styles';
import HexagonLogo from '/src/assets/hexagon_logo.svg';
import { DateTimeFormatter } from '@nexusplatform/core-react-components';
import { IProject } from '../../models/Project';
import { useSelector } from 'react-redux';
import { RootState } from '../../redux';
import React from 'react';
import { ShareIcon } from './ShareIcon';

export interface IHexProjectItem {
  project: IProject;
  selected: boolean;
  shareToMeeting: (id: string) => void;
}

export const HexProjectItem = (props: IHexProjectItem) => {
  const themeMode = useSelector<RootState, 'light' | 'dark'>((state) => state.userProfile.theme);
  const { project, selected, shareToMeeting } = props;
  const { t } = useTranslation();

  const handleShare = () => {
    shareToMeeting(project.id!);
  };

  const getCreatedDateString = (date: string | undefined) => {
    return `${t('Created')} ${DateTimeFormatter.getCreatedDate(date)}`;
  };

  return (
    <>
      <Card sx={styles.card}>
        <Box sx={styles.cardMediaDark}>
          <CardMedia src={project.thumbnail ? project.thumbnail : HexagonLogo} component="img" sx={project.thumbnail ? styles.thumbnail : styles.thumbnailLogo} />
        </Box>
        <Box sx={{ height: '103px' }}>
          <Box sx={styles.description}>
            <CardContent sx={{ padding: '16px 16px 5px 0px' }}>
              <Tooltip title={project.project}>
                <Typography sx={styles.projectTitle} variant="h5" component="div">
                  {project.project}
                </Typography>
              </Tooltip>
              <Box sx={styles.projectDescrptionContainer}>
                <Typography sx={styles.projectCreation}>{getCreatedDateString(project.createdByDate)}</Typography>
              </Box>
            </CardContent>
            <Box sx={styles.actions} onClick={handleShare}>
              <Button sx={styles.actionBtn}>
                <Box sx={styles.actionBtnShare}>
                  <ShareIcon themeMode={themeMode} />
                  <Typography sx={styles.actionBtnTxt}>{t('teamsWhiteboard.shareToMeeting')}</Typography>
                </Box>
              </Button>
            </Box>
          </Box>
        </Box>
      </Card>
      <Backdrop open={selected} sx={styles.cardbackdrop}>
        <Typography sx={styles.nowSharingTxt}>{t('teamsWhiteboard.nowSharing')}</Typography>
      </Backdrop>
    </>
  );
};
