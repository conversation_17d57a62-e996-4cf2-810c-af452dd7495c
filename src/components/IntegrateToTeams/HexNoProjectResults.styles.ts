import { SxProps, Theme } from '@mui/material';
export const styles: SxProps<Theme> | any = {
  emptyIcon: {
    marginTop: '60px',
    height: '50px',
    width: '50px'
  },
  page: {
    backgroundColor: (theme: Theme) => (theme.palette.mode !== 'dark' ? '#f5f5f5' : '#201F1F'),
    width: '300px',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center'
  },
  noResultsTitle: {
    height: '32px',
    width: '100%',
    fontFamily: 'Segoe UI',
    fontStyle: 'normal',
    fontWeight: 600,
    fontSize: '20px !important',
    lineHeight: '32px !important',
    color: (theme: Theme) => (theme.palette.mode === 'dark' ? theme.palette.background.default : '#242424'),
    textAlign: 'center',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    whiteSpace: 'nowrap'
  },
  noResultsDescription: {
    height: '48px',
    width: '217px',
    fontFamily: 'Segoe UI',
    fontStyle: 'normal',
    fontWeight: 400,
    fontSize: '16px !important',
    lineHeight: '24px !important',
    color: (theme: Theme) => (theme.palette.mode === 'dark' ? theme.palette.background.default : '#242424'),
    textAlign: 'center',
    marginLeft: '14px',
    marginTop: '8px'
  }
};
