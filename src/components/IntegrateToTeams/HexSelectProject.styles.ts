import { SxProps, Theme } from '@mui/material';

export const styles: SxProps<Theme> | any = {
  select: {
    // backgroundColor: '#201F1F',
    color: (theme: Theme) => theme.palette.common.white,
    height: '100vh',
    backgroundColor: (theme: Theme) => (theme.palette.mode !== 'dark' ? '#f5f5f5' : '#201F1F')
  },
  loadingSelect: {
    display: 'flex',
    backgroundColor: (theme: Theme) => theme.palette.background.default,
    color: (theme: Theme) => (theme.palette.mode !== 'dark' ? '#D6D6D6' : theme.palette.grey[700]),
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    height: '100vh',
    width: '100%'
  },
  inputSearch: {
    fontFamily: 'Open Sans',
    fontWeight: '400',
    fontSize: '14px',
    height: '32px',
    padding: '0px 8px 0px 0px !important'
  },
  loadingProject: {
    marginTop: '30px',
    color: (theme: Theme) => theme.palette.text.primary,
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center'
  },

  autoComplete: {
    width: '270px',
    height: '32px',
    marginBottom: '18px',
    '& .MuiOutlinedInput-root': {
      backgroundColor: (theme: Theme) => (theme.palette.mode === 'dark' ? '#292929' : '#FFFFFF'),
      color: (theme: Theme) => (theme.palette.mode === 'dark' ? '#D6D6D6' : theme.palette.grey[700]),
      '& .MuiOutlinedInput-notchedOutline': {
        border: 'none'
      },
      '&.MuiOutlinedInput-root .MuiAutocomplete-input': {
        padding: '0px !important',
        marginLeft: '12px !important'
      },
      '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
        borderBottom: '2px solid #5B5FC7 !important'
      },
      '&.Mui-focused': {
        backgroundColor: (theme: Theme) => (theme.palette.mode === 'dark' ? '#292929' : '#FFFFFF'),
        color: (theme: Theme) => (theme.palette.mode === 'dark' ? '#D6D6D6' : theme.palette.grey[700]),
        '& .MuiOutlinedInput-notchedOutline': {
          borderBottom: '2px solid #5B5FC7 !important'
        },
        '& .MuiAutocomplete-clearIndicator': {
          color: (theme: Theme) => (theme.palette.mode !== 'dark' ? '#D6D6D6' : theme.palette.grey[700]),
          backgroundColor: (theme: Theme) => (theme.palette.mode === 'dark' ? '#D6D6D6' : theme.palette.grey[700])
        }
      },
      '& .MuiAutocomplete-clearIndicator:hover': {
        color: (theme: Theme) => (theme.palette.mode !== 'dark' ? '#D6D6D6' : theme.palette.grey[700]),
        backgroundColor: (theme: Theme) => (theme.palette.mode === 'dark' ? '#D6D6D6' : theme.palette.grey[700])
      }
    }
  }
};
