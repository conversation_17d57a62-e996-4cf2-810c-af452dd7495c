import { SxProps, Theme } from '@mui/material';

export const styles: SxProps<Theme> | any = {
  page: {
    backgroundColor: (theme: Theme) => theme.palette.common.black,
    height: '400px',
    display: 'flex',
    flexDirection: 'column',
    paddingTop: '60px',
    paddingLeft: '50px'
  },

  gettingStarted: {
    width: '416px',
    height: '24px',
    fontFamily: 'Segoe UI',
    fontStyle: 'normal',
    fontWeight: '700',
    fontSize: '18px !important',
    lineHeight: '24px !important',
    color: (theme: Theme) => theme.palette.common.white,
    marginRight: '92px',
    marginBottom: '30px'
  },

  listItem: {
    marginBottom: '24px',
    paddingLeft: '0px'
  },

  listItemTxt: {
    fontFamily: 'Segoe UI',
    fontStyle: 'normal',
    fontWeight: '600',
    fontSize: '12px !important',
    lineHeight: '16px !important',
    color: '#FFFFFF'
  }
};
