import { useNavigate, useSearchParams } from 'react-router-dom';
import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Box, Typography, <PERSON><PERSON>, Card, CardHeader, CardContent, CardActions } from '@mui/material';
import { styles } from './Stage.styles';
import { ThreeDWhiteboard } from '@nexusui/branding';
import { useAuth } from 'react-oidc-context';
import * as microsoftTeams from '@microsoft/teams-js';

import { subDomain } from './teams-js-hooks/utils/basePath';
import React from 'react';
import { inTeams } from './teams-js-hooks/utils/inTeams';
import { useTeamsContainer } from './useTeamsContainer';

const Stage = () => {
  const { teamsContainer } = useTeamsContainer();
  const [loggedIn, setLoggedIn] = useState(false);

  const navigate = useNavigate();
  const { t } = useTranslation();
  const [query] = useSearchParams();
  const { isAuthenticated, isLoading } = useAuth();

  const getAccessToken = async () => {
    return await microsoftTeams.authentication.authenticate({
      url: window.location.origin + subDomain + '/authstart?inTeams=true',
      width: 600,
      height: 535
    });
  };

  const authenticate = async () => {
    try {
      await getAccessToken();
      setLoggedIn(true);
    } catch (ex) {
      console.log(ex);
    }
  };

  useEffect(() => {
    const instancid = query?.get('instanceId');
    if (isAuthenticated && instancid) {
      console.log('isAuthenticated', isAuthenticated);
      console.log('instancid', instancid);
      navigate({
        pathname: `/whiteboard/${instancid}?inTeams=true`
      });
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isAuthenticated]);

  useEffect(() => {
    if (loggedIn && inTeams() && teamsContainer && teamsContainer.initialObjects) {
      const documentId = teamsContainer.initialObjects.editorMap?.getText();
      console.log('documentId', documentId);
      window.location.reload();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [loggedIn, teamsContainer]);

  const handleLogin = () => {
    authenticate();
  };

  const handleCreateAccount = () => {
    window.open(`${window.location.origin}`, '_blank');
  };
  return (
    <>
      {!isLoading && !isAuthenticated && (
        <Box sx={styles.boxContainer}>
          <Card sx={styles.cardContainer} variant="outlined">
            <CardHeader
              sx={styles.cardHeader}
              avatar={<ThreeDWhiteboard height={24} width={24} />}
              title={t('teamsWhiteboard.wbLabel')}
              titleTypographyProps={{ fontSize: '12px', fontFamily: 'Segoe UI' }}
            />
            <CardContent sx={styles.cardContent}>
              <Typography gutterBottom fontFamily={'Segoe UI'} fontSize={'24px'} fontWeight={700}>
                {t('teamsWhiteboard.sharingModel', { USER_NAME: query.get('user') })}
              </Typography>
              <Typography fontFamily={'Segoe UI'} fontSize={'12px'} lineHeight={'16px'} variant="body2">
                {t('teamsWhiteboard.signInToNexus', { USER_NAME: query.get('user') })}
              </Typography>
            </CardContent>
            <CardActions sx={styles.cardActions}>
              <Button onClick={handleLogin} sx={styles.primaryButton} size="small" variant="contained">
                <Typography fontFamily={'Segoe UI'} fontSize={'13px'}>
                  {t('teamsWhiteboard.login')}
                </Typography>
              </Button>
              <Button onClick={handleCreateAccount} sx={styles.secondaryButton} size="small" variant="outlined">
                <Typography fontFamily={'Segoe UI'} fontSize={'13px'}>
                  {t('teamsWhiteboard.createAccount')}
                </Typography>
              </Button>
            </CardActions>
          </Card>
        </Box>
      )}
    </>
  );
};

export default Stage;
