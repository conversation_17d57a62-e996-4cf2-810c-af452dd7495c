import React from 'react';
import { useMicrosoftTeamsAuthenticate } from './Authentication/useMicrosoftTeamsAuthenticate';
import { HexSelectProject } from './HexSelectProject';
import { Typography, Button } from '@mui/material';
import { Box } from '@mui/system';
import { t } from 'i18next';
import { styles } from './SidePanel.styles';

const SidePanel = () => {
  const { isAuthenticatedFromTeams, authenticate } = useMicrosoftTeamsAuthenticate();

  const handleLogin = () => {
    authenticate();
  };

  const handleCreateAccount = () => {
    window.open(`${window.location.origin}`, '_blank');
  };

  return (
    <>
      {isAuthenticatedFromTeams ? (
        <HexSelectProject />
      ) : (
        <Box sx={styles.page}>
          <Box sx={styles.innerBox}>
            <Typography gutterBottom fontFamily={'Segoe UI'} color={'#FFFFFF'} fontSize={'20px'} fontWeight={600} lineHeight={'32px'} textAlign={'center'}>
              {t('teamsWhiteboard.authHeader')}
            </Typography>
            <Typography gutterBottom fontFamily={'Segoe UI'} color={'#D6D6D6'} fontSize={'16px'} fontWeight={400} lineHeight={'24px'} textAlign={'center'}>
              {t('teamsWhiteboard.authBodyText')}
            </Typography>
            <Box sx={styles.cardActions}>
              <Button onClick={handleLogin} sx={styles.primaryButton} size="small" variant="contained">
                <Typography fontFamily={'Segoe UI'} fontSize={'13px'}>
                  {t('teamsWhiteboard.login')}
                </Typography>
              </Button>
              <Button onClick={handleCreateAccount} sx={styles.secondaryButton} size="small" variant="outlined">
                <Typography fontFamily={'Segoe UI'} fontSize={'13px'}>
                  {t('teamsWhiteboard.createAccount')}
                </Typography>
              </Button>
            </Box>
          </Box>
        </Box>
      )}
    </>
  );
};

export default SidePanel;
