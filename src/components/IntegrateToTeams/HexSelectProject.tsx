import React from 'react';
import { FixedSizeList as ReactList } from 'react-window';
import { t } from 'i18next';
import AutoComplete from '@mui/material/Autocomplete';
import * as microsoftTeams from '@microsoft/teams-js';

import { subDomain } from './teams-js-hooks/utils/basePath';
import { inTeams } from './teams-js-hooks/utils/inTeams';
import { ContainerType, retrieveSdcInstances } from '@nexusplatform/core';
import { useState, useEffect, CSSProperties, useCallback } from 'react';
import { IProject } from '../../models/Project';
import { BASE_URL as URL } from '../../configuration/URL.config';
import { Box, CircularProgress, TextField } from '@mui/material';
import { styles } from './HexSelectProject.styles';
import { HexProjectItem } from './HexProjectItem';
import { debounce } from '../../utils/Debounce';
import { HexNoProjectResults } from './HexNoProjectResults';
import { HexEmptyProjects } from './HexEmptyProjects';
import { fetchThumbnails } from '../../services/DownloadThumbnails';
import { useTeamsContainer } from './useTeamsContainer';
import { SharedString } from '@fluidframework/sequence';
import CancelIcon from '@mui/icons-material/Cancel';
import { useHexAuth } from '@nexusplatform/react';
import { useSelector } from 'react-redux';
import { RootState } from '../../redux';

export const HexSelectProject = () => {
  const { teamsContainer } = useTeamsContainer();
  const user = useSelector((state: RootState) => state.userProfile.user);
  const [hexProjects, setHexProjects] = useState<Array<IProject>>([]);
  const keyword = sessionStorage.getItem('projectName');
  const [search, setSearch] = useState<string>(keyword !== null && keyword !== 'undefined' ? keyword : '');
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [selectedId, setSelectedId] = useState<string>('');
  const [isFocused, setIsFocused] = useState<boolean>(false);
  const { getAccessTokenSilently } = useHexAuth();

  useEffect(() => {
    console.log('teamsContainer useEffect', teamsContainer);
    if (teamsContainer) {
      teamsContainer.initialObjects.editorMap.on('sequenceDelta', () => {
        console.log('Container changed', teamsContainer.initialObjects.editorMap.getText());
        setSelectedId(teamsContainer.initialObjects.editorMap.getText());
      });
    }
  }, [teamsContainer]);

  const getCustomHeaders = async () => {
    const JWT = await getAccessTokenSilently();
    const customHeaders = {
      authorization: `Bearer ${JWT}`
    };
    return customHeaders;
  };

  const getHexProjects = useCallback(async (queryParams?: any) => {
    try {
      setIsLoading(true);
      const customHeaders = await getCustomHeaders();
      const response = await retrieveSdcInstances(URL, customHeaders ?? null, queryParams, undefined, 'v2', ContainerType.All);
      if (response) {
        const projectsToPass = response.data.content.map((project: any) => {
          return {
            ...project,
            thumbnail: project.thumbnailRef && project.thumbnailRef.sas ? project.thumbnailRef.sas : null,
            canDelete: user?.id === project.createdBy,
            createdBy: project.createdBy
          };
        });
        const updatedProjects = await fetchThumbnails([...projectsToPass], await getAccessTokenSilently());
        setHexProjects([...updatedProjects]);
      }
    } catch (err) {
      console.error('Failed to retrieve projects:', err);
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    setIsLoading(true);
    const queryParams: any = { pageSize: 30, orderBy: 'createdByDate', orderDirection: 'DESC' };
    getHexProjects(queryParams);
  }, [getHexProjects]);
  const checkAppSharingState = () => {
    microsoftTeams.meeting.getAppContentStageSharingState((error, result) => {
      if (!error && !result?.isAppSharing) {
        setSelectedId('');
      }
    });
  };

  useEffect(() => {
    // Right now there is no way to get notified when the app is no longer sharing to stage.
    const sharingInterval = setInterval(checkAppSharingState, 3000);
    return () => clearInterval(sharingInterval);
  }, []);

  const handleClickProject = async (id: string | undefined) => {
    try {
      const userName = user?.firstName ?? user?.email;
      if (id) {
        setSelectedId(id);
        console.log('Container', teamsContainer);
        const editorMap = teamsContainer?.initialObjects?.editorMap as SharedString;
        if (editorMap) {
          try {
            editorMap.getLength() > 0 && editorMap.removeText(0, editorMap.getLength());
          } catch (err) {
            console.log(err);
          }
          editorMap.insertText(0, id);
          console.log('updated container', teamsContainer?.initialObjects?.editorMap?.getText());
        }
        if (inTeams()) {
          microsoftTeams.meeting.shareAppContentToStage((error, result) => {
            if (!error) {
              console.log('Started sharing to stage');
            } else {
              console.warn('shareAppContentToStage failed', error);
            }
          }, `${window.location.origin + subDomain}/stage?inTeams=true&instanceId=${id}&user=${userName}`);
        }
      }
    } catch (err) {
      console.error(err);
    }
  };

  const getProjectItems = (index: number, style: CSSProperties | undefined) => {
    if (hexProjects.length > 0) {
      const project = hexProjects[index];

      if (project) {
        return (
          <Box style={style} role="listItem" data-testid="project-select">
            <HexProjectItem key={project.id} project={project} selected={selectedId === project.id} shareToMeeting={handleClickProject} />
          </Box>
        );
      }
    }
  };

  // const handleChange = (event: any) => {
  //   setIsLoading(true);
  //   setSearch(event.target.value);
  // };

  const getBySearchValue = async (searchBy = '') => {
    if (searchBy !== '') {
      setHexProjects([]);
      const queryParams: any = { pageSize: 30, tags: '', project: search || searchBy, accessType: '', orderBy: 'createdByDate', orderDirection: 'DESC' };
      await getHexProjects(queryParams);
    } else {
      setIsFocused(false);
      const queryParams: any = { pageSize: 30, orderBy: 'createdByDate', orderDirection: 'DESC' };
      getHexProjects(queryParams);
      setSearch('');
    }
  };

  const handleSearchChange = useCallback(
    debounce((event: any) => {
      event.preventDefault();
      setSearch(event.target.value);
      getBySearchValue(event.target.value);
      sessionStorage.setItem('projectName', event.target.value);
    }, 1000),
    []
  );
  const showNoList = () => (search !== '' ? <HexNoProjectResults searchText={search} /> : <HexEmptyProjects />);

  return (
    <Box sx={styles.select}>
      {isLoading ? (
        <>
          <Box sx={styles.loadingSelect}>
            <Box>
              <CircularProgress disableShrink />
            </Box>
            <Box sx={styles.loadingProject}>{t('loadingYourProjects')}</Box>
          </Box>
        </>
      ) : (
        <>
          <Box sx={{ height: '100%' }}>
            <AutoComplete
              fullWidth
              freeSolo
              options={[]}
              id="teams-search-project"
              data-testid="teams-search-project"
              onChange={handleSearchChange}
              value={search}
              renderInput={(params) => (
                <TextField
                  {...params}
                  placeholder={t('teamsWhiteboard.searchForProjects')}
                  id="teams-input-search"
                  data-testid="teams-input-search"
                  value={search}
                  onChange={handleSearchChange}
                  onFocus={() => setIsFocused(true)}
                  onBlur={() => setIsFocused(false)}
                  InputProps={{
                    ...params.InputProps,
                    sx: styles.inputSearch,
                    endAdornment: (
                      <div
                        onClick={() => {
                          setSearch('');
                          getBySearchValue();
                          setIsFocused(false);
                          sessionStorage.removeItem('projectName');
                        }}
                      >
                        {isFocused || params.inputProps.value ? <CancelIcon /> : null}
                      </div>
                    )
                  }}
                  sx={styles.autoComplete}
                />
              )}
            />
            {!isLoading &&
              teamsContainer &&
              (hexProjects.length > 0 ? (
                <div style={{ overflow: 'auto' }}>
                  <style>
                    {`
          /* Customize the scroll bar */
          ::-webkit-scrollbar {
            width: 6px;
            border-radius: 4px;
          }
  
          ::-webkit-scrollbar-track {
            background: #292929;
          }
  
          ::-webkit-scrollbar-thumb {
            background: #D1D3D4;
            border-radius: 4px;
          }
        `}
                  </style>
                  <ReactList height={window.innerHeight} itemCount={hexProjects.length} itemSize={240} width={'100%'}>
                    {({ index, style }) => {
                      return <>{getProjectItems(index, style)}</>;
                    }}
                  </ReactList>
                </div>
              ) : (
                showNoList()
              ))}
          </Box>
        </>
      )}
    </Box>
  );
};
