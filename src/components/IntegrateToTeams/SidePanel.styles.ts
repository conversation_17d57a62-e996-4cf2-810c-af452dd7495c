import { SxProps, Theme } from '@mui/material';

export const styles: SxProps<Theme> | any = {
  page: {
    backgroundColor: '#201F1F',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    height: '100vh',
    width: '100%'
  },
  innerBox: {
    width: '246px',
    gap: '24px',
    justifyContent: 'center',
    alignItems: 'center'
  },
  cardActions: {
    display: 'inline-flex',
    marginTop: '16px',
    gap: '8px',
    fontFamily: 'Segoe UI',
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%'
  },
  primaryButton: {
    color: '#FFFFFF',
    backgroundColor: '#5B5FC7',
    borderRadius: '4px',
    minWidth: '106px',
    fontSize: '13px',
    height: '28px',
    paddingLeft: '8px',
    paddingRight: '8px',
    paddingTop: '5px',
    paddingBottom: '5px',
    alignItems: 'center',
    display: 'inline-flex'
  },
  secondaryButton: {
    color: '#5B5FC7',
    borderRadius: '4px',
    border: '1px solid #5B5FC7',
    fontSize: '13px',
    height: '28px',
    paddingLeft: '8px',
    paddingRight: '8px',
    paddingTop: '5px',
    paddingBottom: '5px',
    alignItems: 'center',
    display: 'inline-flex'
  }
};
