import React, { useEffect, useState } from 'react';
import * as microsoftTeams from '@microsoft/teams-js';
import { v4 } from 'uuid';
import { AUTH_CONFIG } from '../../../configuration/AUTH.config';
import { subDomain } from '../teams-js-hooks/utils/basePath';
import { Box, CircularProgress, Theme } from '@mui/material';

const AuthStart = () => {
  const [initialized, setInitialized] = useState(false);

  useEffect(() => {
    if (!initialized) {
      microsoftTeams.app
        .initialize()
        .then(() => {
          console.log('Started Authentication');
          console.log(`Redirect: ${window.location.origin + subDomain}/authend?inTeams=true`);
          const queryParams = {
            client_id: AUTH_CONFIG.clientID,
            response_type: 'code',
            scope: 'openid',
            redirect_uri: window.location.origin + subDomain + '/authend?inTeams=true',
            state: v4()
          };
          const authorizeEndpoint = `${AUTH_CONFIG.issuer}/protocol/openid-connect/auth?` + objectToQueryString(queryParams);
          window.location.assign(authorizeEndpoint);
          setInitialized(true);
        })
        .catch((error: any) => console.error(error));
    } else {
      setInitialized(true);
    }
  }, [initialized]);

  function objectToQueryString(obj: any) {
    const str = [];
    for (const p in obj)
      if (Object.prototype.hasOwnProperty.call(obj, p)) {
        str.push(encodeURIComponent(p) + '=' + encodeURIComponent(obj[p]));
      }
    return str.join('&');
  }

  return (
    <>
      <Box
        sx={{
          display: 'flex',
          backgroundColor: (theme: Theme) => theme.palette.common.black,
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          height: '100vh',
          width: '100%'
        }}
      >
        <Box sx={{ display: 'flex', justifyContent: 'center' }}>
          <CircularProgress disableShrink />
        </Box>
        <Box sx={{ marginTop: '30px', color: 'common.white', display: 'flex', flexDirection: 'column', alignItems: 'center', justifyContent: 'center' }}>
          {'Starting authorization process...'}
        </Box>
      </Box>
    </>
  );
};

export default AuthStart;
