import { useEffect, useState } from 'react';
import * as microsoftTeams from '@microsoft/teams-js';
import { useAuth } from 'react-oidc-context';
import { subDomain } from '../teams-js-hooks/utils/basePath';

export const useMicrosoftTeamsAuthenticate = () => {
  const { isAuthenticated } = useAuth();
  const [isAuthenticatedFromTeams, setIsAuthenticatedFromTeams] = useState(isAuthenticated);
  const getAccessToken = async () => {
    return await microsoftTeams.authentication.authenticate({
      url: window.location.origin + subDomain + '/authstart?inTeams=true',
      width: 600,
      height: 535
    });
  };

  const authenticate = async () => {
    try {
      await getAccessToken();
      setIsAuthenticatedFromTeams(true);
    } catch (ex) {
      console.log(ex);
    }
  };

  useEffect(() => {
    if (!isAuthenticated) authenticate();
  });

  return { isAuthenticatedFromTeams, authenticate };
};
