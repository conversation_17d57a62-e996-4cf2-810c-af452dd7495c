import React, { useEffect, useState } from 'react';
import * as microsoftTeams from '@microsoft/teams-js';

const AuthEnd = () => {
  const [initialized, setInitialized] = useState(false);

  useEffect(() => {
    if (!initialized) {
      microsoftTeams.app
        .initialize()
        .then(() => {
          console.log('AuthEnd: Finished Authentication');
          microsoftTeams.authentication.notifySuccess();
          setInitialized(true);
        })
        .catch((error: any) => console.error(error));
    } else {
      setInitialized(true);
    }
  }, [initialized]);

  return <h1>Finished Auth process...</h1>;
};

export default AuthEnd;
