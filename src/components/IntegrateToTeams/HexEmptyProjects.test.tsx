import React, { render } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import { HexEmptyProjects } from './HexEmptyProjects';

vi.mock('react-i18next', () => ({
  ...vi.importActual('react-i18next'),
  useTranslation: () => {
    return {
      t: (str: string) => str,
      i18n: {
        changeLanguage: () => new Promise<void>((resolve) => resolve())
      }
    };
  }
}));

describe('HexEmptyProjects', () => {
  it('Should match snapshot', () => {
    const { container } = render(<HexEmptyProjects />);
    expect(container).toMatchSnapshot();
  });
});
