import { useEffect, useState } from 'react';
import * as microsoftTeams from '@microsoft/teams-js';
import { LiveShareClient } from '@microsoft/live-share';
import { LiveShareHost } from '@microsoft/teams-js';
import { SharedString } from '@fluidframework/sequence';

export const useTeamsContainer = () => {
  const [teamsContainer, setTeamsContainer] = useState<any>();

  useEffect(() => {
    (async function () {
      console.log('useTeamsContainer: started');
      await microsoftTeams.app.initialize();
      const host: any = LiveShareHost.create();

      // Define Fluid document schema and create container
      const client = new LiveShareClient(host);
      const containerSchema = {
        initialObjects: { editorMap: SharedString }
      };

      // Joining the container with default schema defined.
      const { container } = await client.joinContainer(containerSchema);
      setTeamsContainer(container);
      console.log('useTeamsContainer: finished', container, teamsContainer);
    })();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return { teamsContainer };
};
