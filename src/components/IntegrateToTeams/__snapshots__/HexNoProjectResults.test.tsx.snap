// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`HexNoProjectResults > Should match snapshot 1`] = `
{
  "asFragment": [Function],
  "baseElement": <body>
    <div>
      <div
        class="MuiBox-root css-vqjrcq"
      >
        <p
          class="MuiTypography-root MuiTypography-body1 css-1oizers-MuiTypography-root"
          data-testid="no-results"
        >
          noResultsTitle
        </p>
        <p
          class="MuiTypography-root MuiTypography-body1 css-1oizers-MuiTypography-root"
          data-testid="no-results"
        >
          "abc"
        </p>
        <p
          class="MuiTypography-root MuiTypography-body1 css-1gz1taj-MuiTypography-root"
        >
          noResultsDescription
        </p>
      </div>
    </div>
  </body>,
  "container": <div>
    <div
      class="MuiBox-root css-vqjrcq"
    >
      <p
        class="MuiTypography-root MuiTypography-body1 css-1oizers-MuiTypography-root"
        data-testid="no-results"
      >
        noResultsTitle
      </p>
      <p
        class="MuiTypography-root MuiTypography-body1 css-1oizers-MuiTypography-root"
        data-testid="no-results"
      >
        "abc"
      </p>
      <p
        class="MuiTypography-root MuiTypography-body1 css-1gz1taj-MuiTypography-root"
      >
        noResultsDescription
      </p>
    </div>
  </div>,
  "debug": [Function],
  "findAllByAltText": [Function],
  "findAllByDisplayValue": [Function],
  "findAllByLabelText": [Function],
  "findAllByPlaceholderText": [Function],
  "findAllByRole": [Function],
  "findAllByTestId": [Function],
  "findAllByText": [Function],
  "findAllByTitle": [Function],
  "findByAltText": [Function],
  "findByDisplayValue": [Function],
  "findByLabelText": [Function],
  "findByPlaceholderText": [Function],
  "findByRole": [Function],
  "findByTestId": [Function],
  "findByText": [Function],
  "findByTitle": [Function],
  "getAllByAltText": [Function],
  "getAllByDisplayValue": [Function],
  "getAllByLabelText": [Function],
  "getAllByPlaceholderText": [Function],
  "getAllByRole": [Function],
  "getAllByTestId": [Function],
  "getAllByText": [Function],
  "getAllByTitle": [Function],
  "getByAltText": [Function],
  "getByDisplayValue": [Function],
  "getByLabelText": [Function],
  "getByPlaceholderText": [Function],
  "getByRole": [Function],
  "getByTestId": [Function],
  "getByText": [Function],
  "getByTitle": [Function],
  "queryAllByAltText": [Function],
  "queryAllByDisplayValue": [Function],
  "queryAllByLabelText": [Function],
  "queryAllByPlaceholderText": [Function],
  "queryAllByRole": [Function],
  "queryAllByTestId": [Function],
  "queryAllByText": [Function],
  "queryAllByTitle": [Function],
  "queryByAltText": [Function],
  "queryByDisplayValue": [Function],
  "queryByLabelText": [Function],
  "queryByPlaceholderText": [Function],
  "queryByRole": [Function],
  "queryByTestId": [Function],
  "queryByText": [Function],
  "queryByTitle": [Function],
  "rerender": [Function],
  "unmount": [Function],
}
`;
