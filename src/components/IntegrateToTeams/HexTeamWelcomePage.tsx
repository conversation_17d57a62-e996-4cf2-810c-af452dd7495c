import React, { useEffect } from 'react';
import { Box, List, ListItem, ListItemIcon, Typography } from '@mui/material';
import { styles } from './HexTeamWelcomePage.styles';
import * as microsoftTeams from '@microsoft/teams-js';
import { inTeams } from './teams-js-hooks/utils/inTeams';
import { subDomain } from './teams-js-hooks/utils/basePath';
import { useTranslation } from 'react-i18next';
import { MarkUpNoteIcon } from './MarkUpNoteIcon';
import { ViewAndDiscussIcon } from './ViewAndDiscussion';
import { CommentsConversationIcon } from './CommentsConversationIcon';

export const HexTeamWelcomePage = () => {
  const { t } = useTranslation();

  useEffect(() => {
    if (inTeams()) {
      microsoftTeams.pages.config.registerOnSaveHandler((saveEvent: any) => {
        microsoftTeams.pages.config.setConfig({
          contentUrl: `${window.location.origin + subDomain}/sidepanel?inTeams=true`,
          suggestedDisplayName: t('teamsWhiteboard.wbLabel')
        });
        saveEvent.notifySuccess();
      });
      microsoftTeams.pages.config.setValidityState(true);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <Box sx={styles.page}>
      <Typography sx={styles.gettingStarted}>{t('teamsWhiteboard.getStartedNote')}</Typography>
      <List>
        <ListItem sx={styles.listItem}>
          <ListItemIcon>
            <MarkUpNoteIcon />
          </ListItemIcon>
          <Typography sx={styles.listItemTxt}>{t('teamsWhiteboard.markupNote')} </Typography>
        </ListItem>
        <ListItem sx={styles.listItem}>
          <ListItemIcon>
            <ViewAndDiscussIcon />
          </ListItemIcon>
          <Typography sx={styles.listItemTxt}>{t('teamsWhiteboard.viewAndDiscussNote')} </Typography>
        </ListItem>
        <ListItem sx={styles.listItem}>
          <ListItemIcon>
            <CommentsConversationIcon />
          </ListItemIcon>
          <Typography sx={styles.listItemTxt}>{t('teamsWhiteboard.addCommentsNote')} </Typography>
        </ListItem>
      </List>
    </Box>
  );
};
