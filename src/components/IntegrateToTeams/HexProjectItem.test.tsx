import React, { render, screen } from '@testing-library/react';
import { HexProjectItem, IHexProjectItem } from './HexProjectItem';
import { describe, it, expect } from 'vitest';
import { Provider } from 'react-redux';
import { store } from '../../redux';

describe('HexProjectItem', () => {
  const props: IHexProjectItem = {
    project: { id: '1000', project: 'Test Project1', description: 'Description1', tags: ['Test tag1', 'Test tag2'], schemaTypeId: 'test', thumbnail: 'test image' },
    selected: false,
    shareToMeeting: (id) => <div>Share to Meeting</div>
  };

  it('Should show project data', async () => {
    const { container } = render(
      <Provider store={store}>
        <HexProjectItem {...props} />
      </Provider>
    );

    expect(await screen.findByText('Test Project1')).toBeInTheDocument();
    expect(container.firstChild).toHaveStyle(`backgroundColor: (theme: Theme) => theme.palette.grey[700]`);
  });

  it('Should highlight when selected is true', async () => {
    const { container } = render(
      <Provider store={store}>
        <HexProjectItem selected={true} project={props.project} shareToMeeting={props.shareToMeeting} />
      </Provider>
    );
    expect(container.firstChild).toHaveStyle('backgroundColor: (theme: Theme) => theme.palette.text.disabled');
  });
});
