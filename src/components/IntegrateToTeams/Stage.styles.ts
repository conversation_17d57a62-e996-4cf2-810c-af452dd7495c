import { SxProps, Theme } from '@mui/material';
import backgroundImage from '../../assets/images/teams_blurred_background.png';

export const styles: SxProps<Theme> | any = {
  boxContainer: {
    backgroundImage: `url(${backgroundImage})`,
    backgroundSize: 'cover',
    backgroundRepeat: 'no-repeat',
    height: '100vh',
    width: '100%',
    display: 'flex'
  },
  cardContainer: {
    position: 'absolute',
    bottom: 16,
    right: 16,
    maxWidth: 345,
    borderRadius: '4px'
  },
  cardContent: {
    p: '0px 12px',
    fontSize: '12px',
    lineHeight: '16px'
  },
  cardHeader: {
    p: '12px'
  },
  cardActions: {
    fontFamily: 'Segoe UI',
    p: '12px'
  },
  primaryButton: {
    backgroundColor: '#5B5FC7',
    borderRadius: '4px',
    minWidth: '100px',
    fontSize: '13px',
    height: '28px',
    '&:hover': {
      backgroundColor: '#5B5FC7'
    }
  },
  secondaryButton: {
    color: '#5B5FC7',
    borderRadius: '4px',
    borderColor: '#5B5FC7',
    minWidth: '100px',
    fontSize: '13px',
    height: '28px',
    '&:hover': {
      borderColor: '#5B5FC7',
      background: 'none'
    }
  }
};
