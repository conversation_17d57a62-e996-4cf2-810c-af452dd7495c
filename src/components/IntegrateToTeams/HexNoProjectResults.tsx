import React from 'react';
import { Box, Typography } from '@mui/material';
import { useTranslation } from 'react-i18next';

import { styles } from './HexNoProjectResults.styles';

export interface IHexNoProjectResultsProps {
  searchText: string;
}
export const HexNoProjectResults = (props: IHexNoProjectResultsProps) => {
  const { searchText } = props;
  const { t } = useTranslation();

  const truncateText = (text: string, maxLength: number) => {
    if (text.length > maxLength) {
      return text.substring(0, maxLength - 3) + '...'; // Adjusted for adding "..."
    }
    return text;
  };

  const truncatedSearchText = truncateText(searchText, 20);
  return (
    <Box sx={styles.page}>
      <Typography data-testid="no-results" sx={styles.noResultsTitle}>
        {t('noResultsTitle')}
      </Typography>
      <Typography data-testid="no-results" sx={styles.noResultsTitle}>
        {`"${truncatedSearchText}"`}
      </Typography>
      <Typography sx={styles.noResultsDescription}>{t('noResultsDescription')}</Typography>
    </Box>
  );
};
