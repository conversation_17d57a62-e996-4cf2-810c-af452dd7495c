import React, { render } from '@testing-library/react';
import { HexNoProjectResults } from './HexNoProjectResults';
import { describe, it, expect, vi } from 'vitest';

vi.mock('react-i18next', () => ({
  ...vi.importActual('react-i18next'),
  useTranslation: () => {
    return {
      t: (str: string) => str,
      i18n: {
        changeLanguage: () =>
          new Promise<void>((resolve) => {
            resolve();
          })
      }
    };
  }
}));

describe('HexNoProjectResults', () => {
  it('Should match snapshot', () => {
    const container = render(<HexNoProjectResults searchText="abc" />);

    expect(container).toMatchSnapshot();
  });
});
