import React from 'react';
import { Box, Button, Typography } from '@mui/material';
import { useTranslation } from 'react-i18next';
import LaunchIcon from '@mui/icons-material/Launch';

import { styles } from './HexEmptyProjects.styles';
import EmptyFolder from '/src/assets/empty_folder.svg';
import { subDomain } from './teams-js-hooks/utils/basePath';

export const HexEmptyProjects = () => {
  const { t } = useTranslation();

  return (
    <Box sx={styles.page}>
      <img style={styles.emptyIcon} src={EmptyFolder} />
      <Typography data-testid="no-projects" sx={styles.noProjectsTitle}>
        {t('noProjectsTitle')}
      </Typography>
      <Typography sx={styles.noProjectsDescription}>{t('noProjectsDescription')}</Typography>
      <Button sx={styles.openNexusBtn} onClick={() => window.open(`${window.location.origin + subDomain}`, '_blank')}>
        <LaunchIcon sx={styles.launchIcon} />
        <Typography sx={styles.openNexusTxt}>{t('teamsWhiteboard.openNexus')}</Typography>
      </Button>
    </Box>
  );
};
