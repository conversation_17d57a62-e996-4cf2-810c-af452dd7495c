import { SxProps, Theme } from '@mui/material';

export const styles: SxProps<Theme> | any = {
  projectTitle: {
    width: '120px',
    height: '20px',
    fontFamily: 'Segoe UI',
    fontStyle: 'normal',
    fontWeight: 600,
    fontSize: '14px !important',
    lineHeight: '19px !important',
    color: (theme: Theme) => (theme.palette.mode === 'dark' ? theme.palette.background.default : '#242424'),
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    whiteSpace: 'nowrap'
  },
  projectDescrptionContainer: {
    width: '242px',
    height: '20px',
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'flex-start',
    padding: '0px',
    gap: '3px'
  },
  projectCreation: {
    width: '100%',
    height: '20px',
    fontFamily: 'Segoe UI',
    fontStyle: 'normal',
    fontWeight: 400,
    fontSize: '12px !important',
    lineHeight: '19px !important',
    color: (theme: Theme) => (theme.palette.mode === 'dark' ? theme.palette.background.default : '#242424'),
    overflow: 'hidden',
    textOverflow: 'ellipsis'
  },
  card: {
    backgroundColor: (theme: Theme) => (theme.palette.mode === 'dark' ? '#3B3A39' : '#FAFAFA'),
    width: '270px',
    height: '222px',
    borderRadius: '4px',
    padding: '0px 0px 16px',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    marginBottom: '6px'
  },

  cardbackdrop: {
    position: 'absolute',
    zIndex: (theme: Theme) => theme.zIndex.drawer + 1,
    width: '270px',
    height: '222px',
    gap: '16px',
    borderRadius: '4px',
    border: '1px solid #5B5FC7',
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center'
  },

  cardMediaDark: {
    backgroundColor: ' rgba(255, 255, 255, 1)',
    height: '100%',
    width: '100%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center'
  },
  cardMediaLight: {
    backgroundColor: (theme: Theme) => theme.palette.grey[50],
    height: '100%',
    width: '100%',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center'
  },

  thumbnailLogo: {
    width: '84px',
    height: '100px'
  },
  thumbnail: {
    width: '100%',
    height: '120px'
  },
  description: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'flex-start',
    padding: '0px',
    width: '242px',
    height: '81px',
    marginLeft: '19px',
    marginRight: '19px',
    marginbottom: '16px'
  },
  actions: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: '0px',
    gap: '8px',
    width: '242px',
    height: '24px'
  },
  actionBtn: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    padding: '0px',
    width: '136px',
    height: '24px',
    border: (theme: Theme) => (theme.palette.mode === 'dark' ? `1px solid ${theme.palette.grey[700]}` : `1px solid #D1D1D1`),
    color: (theme: Theme) => (theme.palette.mode === 'dark' ? theme.palette.background.default : '#242424'),
    '&:hover': {
      border: (theme: Theme) => (theme.palette.mode === 'dark' ? '1px solid #C7C7C7' : '1px solid #FFFFFF, 30%')
    }
  },
  actionBtnShare: {
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    padding: '2px 0px 2px 0px',
    gap: '6px',
    width: '120px',
    height: '24px',
    color: (theme: Theme) => (theme.palette.mode === 'dark' ? theme.palette.background.default : '#242424')
  },
  actionBtnTxt: {
    width: '94px',
    height: '17px',
    color: (theme: Theme) => (theme.palette.mode === 'dark' ? theme.palette.background.default : '#242424'),
    fontFamily: 'Segoe UI',
    fontStyle: 'normal',
    fontWeight: '600',
    fontSize: '12px !important',
    lineHeight: '17px !important',
    display: 'flex',
    alignContent: 'center'
  },
  nowSharingTxt: {
    fontFamily: 'Segoe UI',
    fontStyle: 'normal',
    fontWeight: '700',
    fontSize: '17.86px !important',
    lineHeight: '28.57px !important',
    marginLeft: '8px',
    color: (theme: Theme) => theme.palette.common.white,
    position: 'absolute'
  }
};
