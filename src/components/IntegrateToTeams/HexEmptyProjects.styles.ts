import { SxProps, Theme } from '@mui/material';

export const styles: SxProps<Theme> | any = {
  emptyIcon: {
    marginTop: '60px',
    color: (theme: Theme) => (theme.palette.mode === 'dark' ? theme.palette.background.default : '#242424')
  },
  page: {
    backgroundColor: (theme: Theme) => (theme.palette.mode !== 'dark' ? '#f5f5f5' : '#201F1F'),
    width: '300px',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center'
  },
  noProjectsTitle: {
    height: '24px',
    width: '177px',
    fontFamily: 'Segoe UI',
    fontStyle: 'normal',
    fontWeight: 700,
    fontSize: '14px',
    lineHeight: '24px',
    color: (theme: Theme) => (theme.palette.mode === 'dark' ? theme.palette.background.default : '#242424'),
    textAlign: 'center',
    marginTop: '28px',
    marginLeft: '14px',
    overflow: 'hidden',
    textOverflow: 'ellipsis'
  },
  noProjectsDescription: {
    height: '48px',
    width: '254px',
    fontFamily: 'Segoe UI',
    fontStyle: 'normal',
    fontWeight: 400,
    fontSize: '14px !important',
    lineHeight: '20px !important',
    color: (theme: Theme) => (theme.palette.mode === 'dark' ? theme.palette.background.default : '#242424'),
    textAlign: 'center',
    marginLeft: '14px',
    marginTop: '8px'
  },
  openNexusBtn: {
    backgroundColor: '#4AB5CE',
    width: '183px',
    height: '32px',
    borderRadius: '4px',
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
    padding: '0px',
    marginTop: '16px',
    '&:hover': {
      background: '#4AB5CE'
    }
  },
  openNexusTxt: {
    height: '19px',
    width: '133px',
    fontFamily: 'Segoe UI',
    fontStyle: 'normal',
    fontWeight: 600,
    fontSize: '14px !important',
    lineHeight: '19px !important',
    color: (theme: Theme) => (theme.palette.mode === 'dark' ? theme.palette.background.default : '#242424'),
    textAlign: 'center',
    alignItems: 'center',
    marginLeft: '8px'
  },
  launchIcon: { height: '20px', width: '20px', color: (theme: Theme) => (theme.palette.mode === 'dark' ? theme.palette.background.default : '#242424') }
};
