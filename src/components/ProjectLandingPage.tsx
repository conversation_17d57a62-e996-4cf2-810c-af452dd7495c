import React, { useContext, useEffect } from 'react';
import ProjectDocumentView from './ProjectDocumentView';
import ProjectView from '../pages/ProjectsView';
import { SwitchContext } from '../App';
import { PLATFORM_LANDING_PAGE_URL, BASE_PATH } from '../configuration/URL.config';

interface ProjectLandingPageProps {
  forceMobileMode?: boolean;
}

const ProjectLandingPage = ({ forceMobileMode = false }: ProjectLandingPageProps) => {
  // redirect to originally hit url if app param is present in sessionStorage or redirect to dev to remove code from url address after auth0 authentication
  useEffect(() => {
    const originalUrlBeforeLogin: string = window.location.href;
    const platformRedirectUrl = window.sessionStorage.getItem('platformRedirectUrl');

    if (originalUrlBeforeLogin.indexOf('app=') === -1 && platformRedirectUrl) {
      // In local development, if the stored URL contains /platform-landing,
      // redirect to root with the same query parameters instead
      if (import.meta.env.VITE_APP_BASE === 'dev' && platformRedirectUrl.includes('/platform-landing')) {
        const url = new URL(platformRedirectUrl);
        const newUrl = `${window.location.origin}${BASE_PATH}${url.search}`;
        window.location.replace(newUrl);
      } else {
        window.location.replace(platformRedirectUrl);
      }
    } else {
      if (originalUrlBeforeLogin.indexOf('code=') !== -1) {
        // Use BASE_PATH to handle different environments correctly
        const redirectUrl = import.meta.env.VITE_APP_BASE !== 'dev' ? `https://${PLATFORM_LANDING_PAGE_URL}/platform-landing` : `${window.location.origin}${BASE_PATH}`;
        window.location.replace(redirectUrl);
      }
    }
    window.sessionStorage.removeItem('skipLandingPage');
    window.sessionStorage.removeItem('platformRedirectUrl');
  }, []);

  const switchContextValue = useContext<boolean>(SwitchContext);
  return <>{switchContextValue ? <ProjectDocumentView forceMobileMode={forceMobileMode} /> : <ProjectView forceMobileMode={forceMobileMode} />}</>;
};

export default ProjectLandingPage;
