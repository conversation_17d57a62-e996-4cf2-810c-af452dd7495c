import React from 'react';
import { Box, Typography, Paper, IconButton, useTheme, useMediaQuery } from '@mui/material';
import { CelebrationOutlined, LaunchOutlined, HelpOutlineOutlined, Groups, Description, MoreHoriz, Build, Tune, AutoAwesome, OpenInNew } from '@mui/icons-material';
import NexusLogo from '/src/assets/NexusLogo.svg';
import ThreeDWhiteboardIcon from '@nexusui/branding/ThreeDWhiteboard';
import CategoryOutlinedIcon from '@mui/icons-material/CategoryOutlined';

export const NexusConnect: React.FC = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  const navButtons = [
    {
      name: 'Help',
      icon: <HelpOutlineOutlined sx={{ fontSize: 18, color: '#646e78' }} />
    },
    {
      name: 'Communities',
      icon: <Groups sx={{ fontSize: 18, color: '#646e78' }} />
    },
    {
      name: 'Docs',
      icon: <Description sx={{ fontSize: 18, color: '#646e78' }} />
    },
    {
      name: 'More',
      icon: <MoreHoriz sx={{ fontSize: 18, color: '#646e78' }} />
    }
  ];

  const libraries = [
    {
      name: 'Cutting Tools',
      icon: <Build sx={{ color: '#F3F3F3', fontSize: 18 }} />
    },
    {
      name: 'Bending Tools',
      icon: <Tune sx={{ color: '#F3F3F3', fontSize: 18 }} />
    },
    {
      name: 'ProPlan AI',
      icon: <AutoAwesome sx={{ color: '#F3F3F3', fontSize: 18 }} />
    }
  ];

  const communityActions = [
    {
      name: 'Nexus registrations',
      icon: <OpenInNew sx={{ color: '#747577', fontSize: 16 }} />
    },
    {
      name: 'New idea',
      icon: <OpenInNew sx={{ color: '#747577', fontSize: 16 }} />
    },
    {
      name: 'Bookmark suggestion',
      icon: <OpenInNew sx={{ color: '#747577', fontSize: 16 }} />
    }
  ];

  const relatedApps = [
    { name: 'Apex', color: '#F95B61', letter: 'A' },
    { name: 'Designer', color: '#BCE562', letter: 'D' },
    { name: 'Patran', color: '#F95B61', letter: 'P' },
    { name: '3D Whiteboard', color: '#A5D867', letter: '3' },
    { name: 'Metrology Reporting', color: '#97D9F5', letter: 'M' },
    { name: 'Materials Connect', color: '#A5D867', letter: 'M' }
  ];

  const handleButtonClick = (buttonName: string) => {
    console.log(`${buttonName} button clicked`);
  };

  return (
    <Paper
      elevation={0}
      sx={{
        backgroundColor: '#f8fafd',
        border: '0.622721px solid #e6e6e6',
        borderRadius: '4.98177px',
        minWidth: '312px',
        height: 'fit-content',
        overflow: 'auto',
        boxShadow: '0px 9.96353px 9.96353px 2.49088px rgba(0,0,0,0.11)',
        display: 'flex',
        flexDirection: 'column',
        gap: '8px',
        padding: '16px'
      }}
    >
      {/* Header with Logo */}
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          width: '100%'
        }}
      >
        <img
          src={NexusLogo}
          alt="Nexus"
          style={{
            height: '12px',
            width: '47px'
          }}
        />
      </Box>

      {/* Announcement Banner */}
      <Box
        sx={{
          backgroundColor: '#dff73f',
          borderRadius: '4px',
          padding: '8px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          width: '100%'
        }}
      >
        <Box
          sx={{
            display: 'flex',
            alignItems: 'flex-start',
            gap: '8px'
          }}
        >
          <CelebrationOutlined sx={{ fontSize: 16, color: '#002846' }} />
          <Typography
            sx={{
              fontSize: '12px',
              color: '#002846',
              fontWeight: 400,
              lineHeight: '18px',
              fontFamily: 'Open Sans, sans-serif'
            }}
          >
            Announcement
          </Typography>
        </Box>
        <LaunchOutlined sx={{ fontSize: 16, color: '#002846' }} />
      </Box>

      {/* Navigation Tabs */}
      <Box
        sx={{
          backgroundColor: '#ffffff',
          borderRadius: '8px',
          width: '100%'
        }}
      >
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            width: '100%'
          }}
        >
          {navButtons.map((button) => (
            <Box
              key={button.name}
              onClick={() => handleButtonClick(button.name)}
              sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                padding: '16px',
                cursor: 'pointer',
                flex: 1,
                gap: '4px',
                borderRadius: '3.11646px',
                '&:hover': {
                  backgroundColor: '#f8fafd'
                }
              }}
            >
              {button.icon}
              <Typography
                sx={{
                  fontSize: '12px',
                  color: '#646e78',
                  lineHeight: '18.321px',
                  fontFamily: 'Open Sans, sans-serif',
                  fontWeight: 400,
                  whiteSpace: 'nowrap'
                }}
              >
                {button.name}
              </Typography>
            </Box>
          ))}
        </Box>
      </Box>

      {/* Quick Actions */}
      <Box
        sx={{
          display: 'flex',
          gap: '8px',
          width: '100%'
        }}
      >
        {/* Create Design Review */}
        <Box
          sx={{
            backgroundColor: '#ffffff',
            borderRadius: '4px',
            padding: '8px',
            flex: 1,
            cursor: 'pointer',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            minHeight: '64px',
            gap: '4px',
            '&:hover': {
              transform: 'translateY(-1px)',
              boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)'
            }
          }}
        >
          <ThreeDWhiteboardIcon style={{ width: '24px', height: '24px' }} />
          <Typography
            sx={{
              fontSize: '10px',
              color: '#121212',
              textAlign: 'center',
              lineHeight: '1.5',
              fontFamily: 'Open Sans, sans-serif',
              fontWeight: 400
            }}
          >
            Create Design Review
          </Typography>
        </Box>

        {/* Discover more */}
        <Box
          sx={{
            backgroundColor: '#ffffff',
            borderRadius: '4px',
            padding: '8px',
            flex: 1,
            cursor: 'pointer',
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            minHeight: '64px',
            gap: '4px',
            '&:hover': {
              transform: 'translateY(-1px)',
              boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)'
            }
          }}
        >
          <CategoryOutlinedIcon sx={{ fontSize: 24, color: '#00718C' }} />
          <Typography
            sx={{
              fontSize: '10px',
              color: '#121212',
              textAlign: 'center',
              lineHeight: '1.5',
              fontFamily: 'Open Sans, sans-serif',
              fontWeight: 400
            }}
          >
            Discover more
          </Typography>
        </Box>
      </Box>

      {/* Libraries */}
      <Box
        sx={{
          backgroundColor: '#ffffff',
          borderRadius: '4px',
          padding: '8px',
          width: '100%'
        }}
      >
        <Box
          sx={{
            height: '24px',
            display: 'flex',
            alignItems: 'center',
            marginBottom: '8px'
          }}
        >
          <Typography
            sx={{
              fontSize: '12px',
              color: '#545559',
              fontWeight: 700,
              lineHeight: '20px',
              fontFamily: 'Open Sans, sans-serif'
            }}
          >
            Libraries
          </Typography>
        </Box>
        <Box
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            gap: '10px'
          }}
        >
          {libraries.map((library, index) => (
            <Box
              key={index}
              sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                cursor: 'pointer',
                width: '70px',
                height: '64px',
                justifyContent: 'center',
                gap: '4px',
                '&:hover': {
                  transform: 'translateY(-1px)'
                }
              }}
            >
              <Box
                sx={{
                  backgroundColor: '#000000',
                  borderRadius: '5.53406px',
                  width: '24px',
                  height: '24px',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center'
                }}
              >
                {library.icon}
              </Box>
              <Typography
                noWrap
                sx={{
                  fontSize: '10px',
                  color: '#121212',
                  textAlign: 'center',
                  lineHeight: '11.7419px',
                  fontFamily: 'Open Sans, sans-serif',
                  fontWeight: 400,
                  width: '100%',
                  textOverflow: 'ellipsis',
                  overflow: 'hidden'
                }}
              >
                {library.name}
              </Typography>
            </Box>
          ))}
        </Box>
      </Box>

      {/* Explore Community */}
      <Box
        sx={{
          backgroundColor: '#ffffff',
          borderRadius: '4px',
          padding: '8px 0',
          width: '100%'
        }}
      >
        <Box
          sx={{
            paddingX: '8px',
            marginBottom: '8px'
          }}
        >
          <Typography
            sx={{
              fontSize: '12px',
              color: '#545559',
              fontWeight: 700,
              lineHeight: '20px',
              fontFamily: 'Open Sans, sans-serif'
            }}
          >
            Explore Community
          </Typography>
        </Box>
        {communityActions.map((action, index) => (
          <Box
            key={index}
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              padding: '4px 8px',
              cursor: 'pointer',
              '&:hover': {
                backgroundColor: '#f8fafd'
              }
            }}
          >
            <Typography
              sx={{
                fontSize: '12px',
                color: '#545559',
                lineHeight: '16px',
                fontFamily: 'Open Sans, sans-serif',
                fontWeight: 400
              }}
            >
              {action.name}
            </Typography>
            {action.icon}
          </Box>
        ))}
      </Box>

      {/* Related Apps */}
      <Box
        sx={{
          backgroundColor: '#ffffff',
          borderRadius: '4px',
          padding: '8px',
          width: '100%'
        }}
      >
        <Box
          sx={{
            height: '24px',
            display: 'flex',
            alignItems: 'center',
            marginBottom: '8px'
          }}
        >
          <Typography
            sx={{
              fontSize: '12px',
              color: '#545559',
              fontWeight: 700,
              lineHeight: '20px',
              fontFamily: 'Open Sans, sans-serif'
            }}
          >
            Related apps
          </Typography>
        </Box>
        <Box
          sx={{
            height: '144px',
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'space-between'
          }}
        >
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'flex-start',
              gap: '10px',
              flexWrap: 'wrap'
            }}
          >
            {relatedApps.map((app, index) => (
              <Box
                key={index}
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  alignItems: 'center',
                  cursor: 'pointer',
                  width: '81px',
                  height: '64px',
                  justifyContent: 'center',
                  gap: '4px',
                  '&:hover': {
                    transform: 'translateY(-1px)'
                  }
                }}
              >
                <Box
                  sx={{
                    width: '24px',
                    height: '24px',
                    borderRadius: '7.5px',
                    backgroundColor: app.color,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    fontSize: '14px',
                    fontWeight: 600,
                    color: '#000000',
                    fontFamily: 'Open Sans, sans-serif'
                  }}
                >
                  {app.letter}
                </Box>
                <Typography
                  noWrap
                  sx={{
                    fontSize: '10px',
                    color: '#121212',
                    textAlign: 'center',
                    lineHeight: '1.5',
                    fontFamily: 'Open Sans, sans-serif',
                    fontWeight: 400,
                    textOverflow: 'ellipsis',
                    overflow: 'hidden'
                  }}
                >
                  {app.name}
                </Typography>
              </Box>
            ))}
          </Box>
        </Box>
      </Box>
    </Paper>
  );
};

export default NexusConnect;
