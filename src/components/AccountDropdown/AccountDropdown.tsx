import React, { useState } from 'react';
import { ConnectedAccountDropdown } from '@nexusui/connected-components';
import { PLATFORM_APP_VERSION } from '../../configuration/application.confg';
import { handleLogout } from '../../App';
import { useAuth } from 'react-oidc-context';
import { useSearchParams } from 'react-router-dom';
import { cleanSensitiveInfo } from '../../utils/helper';

export default function AccountDropdown() {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const { signoutRedirect } = useAuth();
  const [searchParams] = useSearchParams();

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleSignout = () => {
    cleanSensitiveInfo();
    const urlBeforeLogout: string = window.location.href;
    if (urlBeforeLogout.indexOf('filter=') !== -1) {
      window.sessionStorage.setItem('platformRedirectUrl', urlBeforeLogout);
    }
    handleLogout(signoutRedirect, searchParams);
  };

  const onOrganizationSwitch = (org: any) => {
    handleSignout();
  };

  return (
    <ConnectedAccountDropdown
      open
      hideBackHeader
      version={PLATFORM_APP_VERSION}
      anchorEl={anchorEl}
      onClose={handleClose}
      onSignOut={handleSignout}
      onMenuItemClick={handleClose}
      data-testid="test-accountDropdown"
      sx={{
        width: '300px',
        '& .MuiPopover-paper': {
          top: '0px !important',
          left: '0px !important',
          position: 'absolute !important',
          borderRadius: '0px !important',
          '@media (max-width: 599px)': {
            right: '0px !important',
            bottom: '0px !important',
            maxWidth: '100% !important',
            maxHeight: '100% !important'
          }
        }
      }}
      onOrganizationSwitch={onOrganizationSwitch}
    />
  );
}
