import React, { useEffect, useRef } from 'react';
import { DocumentAction, DocumentActionProvider, ConnectedDocumentCard, IDocument } from '@nexusui/connected-components';
import { Grid } from '@mui/material';
import TextSnippetOutlinedIcon from '@mui/icons-material/TextSnippetOutlined';
import { useTranslation } from 'react-i18next';
import { AUDIT_URL } from '../../configuration/URL.config';
import { useFlags } from 'launchdarkly-react-client-sdk';
import { debounce } from 'lodash';
import { IFolderList } from '../../models/Folders';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../../redux';
import { setProjectCount } from '../../redux/slices/userProjectCount';

export interface IConnectedDocumentView {
  userDocuments: IDocument[];
  isLoading: boolean;
  isMobile: boolean;
  clickProject: (projectId: string) => void;
  projectsList: IFolderList[] | undefined;
  onSuccessEventCallback?: (action: DocumentAction, doc?: IDocument) => void;
}

export const ConnectedDocumentView = ({ userDocuments, isLoading, isMobile, clickProject, projectsList, onSuccessEventCallback }: IConnectedDocumentView) => {
  const [documents, setDocuments] = React.useState<IDocument[]>([]); // The documents you want to display
  const { t } = useTranslation();
  const { platformSdcAuditLogMenuItemVisible } = useFlags();
  const showMoveDocumentOption = useRef<boolean>(false);
  const pageType = sessionStorage.getItem('data');
  const toggle = sessionStorage.getItem('toggle');
  const user = useSelector((state: RootState) => state.userProfile.user);
  const projectCount = useSelector((state: RootState) => state.userProjectCount.projectCount);
  const dispatch = useDispatch();

  useEffect(() => {
    if (toggle === 'true' && pageType === 'Drafts') {
      showMoveDocumentOption.current = true;
    }
    const fetchData = async () => {
      !isLoading ? setDocuments(userDocuments) : setDocuments([]);
    };
    fetchData();
  }, [isLoading, userDocuments]);

  const handleProjectClick = (document: IDocument) => {
    if (clickProject && document.id) {
      clickProject(document.id);
    }
  };

  // To move draft documents into existing projects owned by the user
  const transformedProjectsList = projectsList?.map((project) => ({
    label: project.name,
    value: project.id
  }));

  const generateMenuConfig = (document: IDocument) => {
    return {
      actions: [
        {
          action: 'Audit Data',
          itemContent: {
            label: 'Audit Data',
            icon: <TextSnippetOutlinedIcon />,
            onClick: () => {
              if (document.id) {
                const win: any = window.open(
                  window.location.href.includes('localhost')
                    ? `http://localhost:3000/audit/` + document.id + `/${document.project}`
                    : AUDIT_URL + document.id + `/${document.project}`,
                  '_blank'
                );
                win.onload = function onload() {
                  win.document.title = t('auditDataTitle');
                };
              }
            }
          },
          show: !!(platformSdcAuditLogMenuItemVisible && user?.id === document.createdBy)
        },
        {
          action: 'move',
          show: showMoveDocumentOption.current
        },
        {
          action: 'debugger',
          show: !!(window.location.href.includes('dev') || (window.location.href.includes('localhost') && user?.id === document.createdBy))
        }
      ],
      sortOptions: ['share', 'rename', 'changeCover', 'move', 'debugger', 'delete', 'unfollow', 'Audit Data']
    };
  };

  const mode = isMobile ? 'rectangle' : 'square'; // `square` or `rectangle` based on your screen.
  const onSuccessEvent = (action: DocumentAction, doc?: IDocument, others?: Record<string, any>) => {
    if (onSuccessEventCallback) {
      onSuccessEventCallback(action, doc); // This is the callback function that will be called when the action is successful
    }

    switch (action) {
      case 'rename':
      case 'changeCover':
      case 'move':
        if (doc) {
          setDocuments((preDocuments) =>
            preDocuments.map((p) => {
              if (p.id === doc.id) {
                return { ...p, ...doc };
              } else {
                return p;
              }
            })
          );
        }
        break;
      case 'delete':
        if (doc) {
          setDocuments((preDocuments) => preDocuments.filter((p) => p.id !== doc.id));
        }
        dispatch(setProjectCount(projectCount - 1));
        break;
      case 'unfollow':
        if (doc) {
          setDocuments((preDocuments) => preDocuments.filter((p) => p.id !== doc.id));
        }
        break;
    }
  };

  return (
    <>
      {!isLoading && userDocuments.length > 0 && (
        <DocumentActionProvider onSuccessEvent={onSuccessEvent} moveProjectDestinationOptions={transformedProjectsList} shareSettings={{ orgShareConfig: { visible: true } }}>
          <Grid alignItems="stretch" container rowSpacing={4} columnSpacing={4}>
            {documents.map((d) => (
              <Grid item key={d.id} xs={mode === 'square' ? undefined : 12}>
                <ConnectedDocumentCard
                  onClick={debounce(() => handleProjectClick(d), 500)} // remove the debounce function once the onClick event handler getting called twice issue is fixed from the Mystique team
                  hideTags={true}
                  document={d}
                  menuConfig={generateMenuConfig(d)}
                  mode={mode}
                  sx={{ height: '100%', width: mode === 'square' ? 300 : undefined }}
                />
              </Grid>
            ))}
          </Grid>
        </DocumentActionProvider>
      )}
    </>
  );
};
