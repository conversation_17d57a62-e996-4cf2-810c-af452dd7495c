import React, { useEffect, useRef, useState } from 'react';
import { <PERSON>nac<PERSON><PERSON>, <PERSON>ert, Typography, Button } from '@mui/material';
import { t } from 'i18next';
import RefreshIcon from '@mui/icons-material/Refresh';
import CheckCircleOutlineIcon from '@mui/icons-material/CheckCircleOutline';
import { IFluidContainer } from '@fluidframework/fluid-static';
import { style } from './HexWhiteboardStateAlert.styles';

export interface IHexWhiteboardStateAlertProps {
  onClickHandler: () => void;
  container: IFluidContainer;
}

export const HexWhiteboardStateAlert = (props: IHexWhiteboardStateAlertProps) => {
  const { onClickHandler, container } = props;

  const [isOnline, setIsOnline] = useState<boolean>();
  const [connectionRestoredSnackbar, setConnectionRestoredSnackbar] = useState<boolean>(false);
  const isFirstRender = useRef<boolean>(true);

  useEffect(() => {
    if (isOnline) {
      setConnectionRestoredSnackbar(true);
    }
  }, [isOnline]);

  useEffect(() => {
    const setMyContainerStateDisconnected = () => {
      setIsOnline(false);
    };
    const setMyContainerStateConnected = () => {
      setIsOnline(true);
    };

    if (container) {
      container?.on('disconnected', setMyContainerStateDisconnected);
      container?.on('connected', setMyContainerStateConnected);
      isFirstRender.current = false;
    }
    return () => {
      if (container) {
        container?.off('disconnected', setMyContainerStateDisconnected);
        container?.off('connected', setMyContainerStateConnected);
        container?.dispose();
      }
    };
  }, [container]);

  return (
    <>
      {isOnline !== undefined && (
        <>
          <Snackbar anchorOrigin={{ vertical: 'top', horizontal: 'center' }} open={!isFirstRender.current && !isOnline} sx={style.errorSnackBar}>
            <Alert variant="filled" severity="error" sx={style.errorAlert}>
              <Typography sx={style.typographyDisconnected}>{t('disconnected')}</Typography>
              <Button sx={style.alertButton} onClick={onClickHandler}>
                <Typography sx={style.reloadButton}>{t('reload')}</Typography>
                <RefreshIcon sx={style.refreshIcon} />
              </Button>
            </Alert>
          </Snackbar>
          <Snackbar
            anchorOrigin={{ vertical: 'top', horizontal: 'center' }}
            open={isOnline && connectionRestoredSnackbar}
            autoHideDuration={6000}
            sx={style.connectionRestoredSnackbar}
            onClose={() => setConnectionRestoredSnackbar(false)}
          >
            <Alert variant="filled" sx={style.connectionRestoredAlert} iconMapping={{ success: <CheckCircleOutlineIcon /> }}>
              <Typography sx={style.typographyConnectionRestored}>{t('connectionRestored')}</Typography>
              <Button sx={style.alertButton} onClick={() => setConnectionRestoredSnackbar(false)}>
                <Typography sx={style.dismiss}>{t('dismiss')}</Typography>
              </Button>
            </Alert>
          </Snackbar>
        </>
      )}
    </>
  );
};
