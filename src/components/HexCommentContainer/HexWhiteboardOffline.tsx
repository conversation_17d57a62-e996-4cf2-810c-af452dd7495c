import React from 'react';
import { Box, Typography, Button } from '@mui/material';
import { t } from 'i18next';
import CloudOffIcon from '@mui/icons-material/CloudOff';

import { styles } from './HexWhiteboardOffline.styles';

// export interface IHexWhiteboardOfflineProps {
//   isOnline: boolean;
// }

export const HexWhiteboardOffline = () => {
  return (
    <Box sx={styles.wbPage}>
      <CloudOffIcon sx={styles.cloudOffIconWeb} />
      <Typography sx={styles.connectTypographyWb}>{t('unableToConnectToNexus')}</Typography>

      <Button
        size="medium"
        sx={styles.reloadButtonWb}
        onClick={() => {
          window.location.reload();
        }}
      >
        <Typography sx={styles.reloadTypography}>{t('reloadPage')}</Typography>
      </Button>
    </Box>
  );
};
