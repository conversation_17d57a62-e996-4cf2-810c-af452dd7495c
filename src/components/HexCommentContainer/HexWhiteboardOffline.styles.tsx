import { SxProps, Theme } from '@mui/material';

export const styles: SxProps<Theme> | any = {
  wbPage: {
    height: '100vh',
    backgroundColor: (theme: Theme) => theme.palette.background.paper,
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    gap: '16px'
  },

  reloadButtonWb: {
    height: '32px',
    display: 'flex',
    padding: '0px 12px',
    flexDirection: 'column',
    alignItems: 'center',
    borderRadius: '4px',
    backgroundColor: (theme: Theme) => theme.palette.primary.main,
    '&:hover': {
      background: (theme: Theme) => theme.palette.primary.dark
    }
  },

  connectTypographyWb: {
    textAlign: 'center',
    fontSize: '20px',
    fontWeight: 600,
    lineHeight: '24px',
    color: (theme: Theme) => theme.palette.text.primary
  },

  cloudOffIconWeb: {
    fontSize: '60px',
    alignItems: 'center',
    margin: '15px',
    color: (theme: Theme) => theme.palette.grey[300]
  },

  reloadTypography: {
    display: 'flex',
    height: '24px',
    padding: '2px 0px',
    alignItems: 'center',
    gap: '6px',
    lineHeight: '19px',
    fontFamily: 'Segoe UI',
    fontWeight: 600,
    fontSize: '14px',
    color: (theme: Theme) => theme.palette.background.paper
  }
};
