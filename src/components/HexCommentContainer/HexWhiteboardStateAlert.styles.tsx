import { SxProps, Theme } from '@mui/material';

export const style: SxProps<Theme> | any = {
  alertButton: {
    width: '55px',
    padding: '0px',
    position: 'absolute',
    fontFamily: 'Open Sans',
    fontStyle: 'normal',
    fontWeight: '700',
    fontSize: '16px',
    lineHeight: '24px',
    textAlign: 'center',
    color: '#FFFFFF',
    flex: 'none',
    order: '0',
    flexGrow: '0',
    right: '55px'
  },

  dismiss: { width: '55px', position: 'absolute', textAlign: 'center', top: '-24px', padding: '0px', fontWeight: '700' },
  refreshIcon: {
    position: 'absolute',
    left: '66px',
    top: '-22px',
    width: '24px !important',
    height: '24px !important'
  },
  reloadButton: { width: '55px', position: 'absolute', textAlign: 'center', top: '-22px', fontWeight: '700' },
  typographyDisconnected: { width: '285px', height: '24px', color: 'white !important' },
  errorSnackBar: {
    position: 'absolute',
    width: '480px !important',
    height: '54px',
    backgroundColor: (theme: Theme) => theme.palette.error.dark,
    fontFamily: 'Open Sans',
    fontStyle: 'normal',
    fontWeight: '400',
    fontSize: '14px',
    lineHeight: '24px',
    gap: '12px',
    borderRadius: '4px',
    top: '71px !important',
    left: '43% !important'
  },
  connectionRestoredSnackbar: {
    position: 'absolute',
    width: '480px !important',
    height: '54px',
    backgroundColor: (theme: Theme) => theme.palette.success.light,
    fontFamily: 'Open Sans',
    fontStyle: 'normal',
    fontWeight: '400',
    fontSize: '14px',
    lineHeight: '24px',
    gap: '12px',
    top: '71px !important',
    left: '43% !important',
    borderRadius: '4px',
    '& .MuiAlert-icon': {
      '& .MuiSvgIcon-root': { width: '22px !important', height: '22px !important', color: (theme: Theme) => theme.palette.background.default, marginTop: '2px !important' }
    }
  },
  connectionRestoredAlert: { backgroundColor: (theme: Theme) => theme.palette.success.light, width: '480px !important' },
  typographyConnectionRestored: { width: '285px', height: '24px', color: 'white !important' },
  errorAlert: {
    width: '480px !important',
    backgroundColor: (theme: Theme) => theme.palette.error.dark,
    '& .MuiAlert-icon': {
      '& .MuiSvgIcon-root': { width: '22px !important', height: '22px !important', color: (theme: Theme) => theme.palette.background.default, marginTop: '2px !important' }
    }
  }
};
