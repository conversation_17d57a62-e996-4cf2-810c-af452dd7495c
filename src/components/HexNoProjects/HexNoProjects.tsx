import React from 'react';
import { Typography, Button, useMediaQuery } from '@mui/material';
import { styles } from '../EmptyProjectView/HexEmptyProject.styles';
import { Box } from '@mui/material';
import { useTranslation } from 'react-i18next';
import NoProjects from '/src/assets/images/NoProjects.svg';
import { NoProjectStyles as stylesNoProject } from './HexNoProjects.styles';
export interface IHexEmptyProjects {
  containerHeight: number | undefined;
  onCreateProject: () => void;
}
/**
 * @param {IHexEmptyProjects} props - provides the properties fo React component
 * @return {ReactNode} - provides the react component to be ingested
 **/
export const HexNoProjects: React.FC<IHexEmptyProjects> = ({ containerHeight, onCreateProject }: IHexEmptyProjects) => {
  const { t } = useTranslation();
  const isMobile = useMediaQuery('(max-width:900px)');

  return (
    <>
      {isMobile && (
        <Box sx={stylesNoProject.container}>
          <Typography fontFamily={'Open Sans'} variant="subtitle2" fontWeight={600}>
            {t('firstProject')}
          </Typography>
          <Typography fontFamily={'Open Sans'} variant="body2" fontWeight={400} margin="10px 0px 20px 0px">
            {t('projectText')}
          </Typography>
          <Button variant="outlined" color={'primary'} onClick={onCreateProject}>
            {t('createProjects')}
          </Button>
        </Box>
      )}
      <Box sx={styles.hexContainer} data-testid="empty" height={containerHeight ? containerHeight - 57 : 0}>
        {!isMobile && (
          <Box sx={stylesNoProject.containerDesktop} style={{ backgroundImage: `url(${NoProjects})` }}>
            <Typography fontFamily={'Open Sans'} variant="h6" fontWeight={600} color={'#FFFFFF'} paddingBottom={'10px'}>
              {t('firstProject')}
            </Typography>
            <Typography fontFamily={'Open Sans'} variant="subtitle2" fontWeight={400} color={'#FFFFFF'} paddingBottom={'20px'}>
              {t('projectText')}
            </Typography>
            <Button variant="outlined" sx={{ color: '#FFFFFF', borderColor: '#FFFFFF' }} onClick={onCreateProject}>
              {t('createProjects')}
            </Button>
          </Box>
        )}
      </Box>
    </>
  );
};
