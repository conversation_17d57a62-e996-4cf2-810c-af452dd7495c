import React, { useEffect, useState } from 'react';
import { Dialog, DialogTitle, DialogContent, DialogContentText, DialogActions, Button } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { IProject } from '../../models/Project';
import { IFolderList } from '../../models/Folders';

export interface IDeleteDialogProps {
  showDeleteDialog: boolean;
  handleClose: () => void;
  handleDelete: () => void;
  project: IProject | IFolderList | null | undefined;
}

export const HexDeleteDialog = ({ showDeleteDialog, project, handleClose, handleDelete }: IDeleteDialogProps) => {
  const { t } = useTranslation();
  const [type, setType] = useState<string>('');
  useEffect(() => {
    if (project !== null && project !== undefined) {
      if ('project' in project) {
        setType('document');
      } else {
        setType('project');
      }
    }
  }, []);
  return (
    <Dialog data-testid="delete-project-dialog" open={showDeleteDialog} onClose={handleClose} aria-labelledby="alert-dialog-title" aria-describedby="alert-dialog-description">
      <DialogTitle data-testid="delete-dialog-title">{type === 'document' ? t('deleteDocumentHeader') : type === 'project' && t('deleteProjectHeader')}</DialogTitle>
      <DialogContent>
        <DialogContentText sx={{ color: 'grey.400' }} data-testid="delete-dialog-description">
          {type === 'document' ? t('deleteDocumentBody') : type === 'project' && t('deleteProjectBody')}
        </DialogContentText>
      </DialogContent>
      <DialogActions className="hex-dialog-actions" sx={{ display: 'flex', justifyContent: 'flex-end' }}>
        <Button onClick={handleClose} color="primary" sx={{ m: 0 }} data-testid="cancel-delete-project">
          {t('cancel')}
        </Button>
        <Button onClick={handleDelete} color="primary" sx={{ m: 0 }} data-testid="confirm-delete-project">
          {type === 'document' ? t('deleteDocument') : type === 'project' && t('deleteProjectBtn')}
        </Button>
      </DialogActions>
    </Dialog>
  );
};
