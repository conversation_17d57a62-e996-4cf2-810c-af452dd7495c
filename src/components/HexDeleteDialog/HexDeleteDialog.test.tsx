import React, { fireEvent, render, screen } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import { HexDeleteDialog, IDeleteDialogProps } from './index';
vi.mock('react-i18next', () => ({
  // this mock makes sure any components using the translate hook can use it without a warning being shown
  useTranslation: () => {
    return {
      t: (str: any) => str,
      i18n: {
        changeLanguage: () => new Promise<void>((resolve) => resolve())
      }
    };
  }
}));

describe('HexDeleteDialog', () => {
  const handleClose = vi.fn();
  const handleDelete = vi.fn();
  const mockProject = {
    id: '5309f47a-6141-4b24-b78a-3a960e198acd',
    organizationId: 'auth0|62ac3fb8fc1881001d521793',
    project: 'MACKENZIE_TEST',
    description: 'Design and Additive Manufacturing',
    schemaTypeId: '5f9f1b9a-8c9c-4b9f-9c1a-3a960e198acd'
  };
  const props: IDeleteDialogProps = { project: mockProject, showDeleteDialog: true, handleClose: handleClose, handleDelete: handleDelete };
  it('should render the component', async () => {
    const component = render(<HexDeleteDialog {...props} />);
    expect(component).toBeDefined();
  });

  it('should render a title', async () => {
    render(<HexDeleteDialog {...props} />);
    const title = screen.getByTestId('delete-dialog-title').innerHTML;
    expect(title).toBe('deleteDocumentHeader');
  });

  it('should have project in description', async () => {
    render(<HexDeleteDialog {...props} />);
    const description = screen.getByTestId('delete-dialog-description').innerHTML;
    expect(description).toContain('deleteDocumentBody');
  });

  it('should fire close event', async () => {
    render(<HexDeleteDialog {...props} />);
    fireEvent.click(await screen.findByTestId('cancel-delete-project'));
    expect(handleClose).toBeCalledTimes(1);
  });

  it('should fire handle delete ', async () => {
    render(<HexDeleteDialog {...props} />);
    fireEvent.click(await screen.findByTestId('confirm-delete-project'));
    expect(handleDelete).toBeCalledTimes(1);
  });
});
