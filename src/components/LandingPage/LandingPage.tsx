import React, { useEffect, useLayoutEffect, useRef } from 'react';
import { AUTH_CONFIG as AUTH } from '../../configuration/AUTH.config';
import { useFlags } from 'launchdarkly-react-client-sdk';
import { Button, Grid, Box, Typography, Container } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { useAuth } from 'react-oidc-context';
import OpenInNewIcon from '@mui/icons-material/Launch';
import { TileComponent } from './TileComponent';
import { styles } from './LandingPage.styles';
import { PLATFORM_LANDING_PAGE_URL, NEXUS_HOMEPAGE } from '../../configuration/URL.config';
import { InformationWithProgressBackDrop } from '../BackDrops/InformationWithProgressBackDrop';
import { useSelector } from 'react-redux';
import { RootState } from '../../redux';
import hexagonNexusWhite from '/src/assets/hexagon_nexus_white.svg';
import hexagonNexusDark from '/src/assets/images/nexus_by_hexagon_dark.svg';
import useWindowSize from '../../utils/hooks/UseWindowSize';

export const LandingPage = () => {
  const { platformLandingPageBrowserWeb, platformLandingPageBrowserMobileSignUpWeb } = useFlags();
  const { t } = useTranslation();
  const { signinRedirect } = useAuth();
  const { width, height } = useWindowSize();
  const [skipLandingPage, setSkipLandingPage] = React.useState<string>('false');
  const themeMode = useSelector((state: RootState) => state.userProfile.theme);
  const logoSrc = themeMode === 'dark' ? `${hexagonNexusWhite}` : `${hexagonNexusDark}`;

  let target = '';

  // if the feature flag platformLandingPageBrowserWeb is enabled conditionally open the signUp & signIn links in same/new tab
  if (platformLandingPageBrowserWeb) {
    target = '_blank';
  } else {
    target = '_self';
  }

  // custom event for login button click
  const triggerNativeAppEvent = (eventName: string) => {
    const preLoginEvent = new CustomEvent(`nexus:${eventName}`, { detail: {} });
    window.dispatchEvent(preLoginEvent);
  };

  useLayoutEffect(() => {
    triggerNativeAppEvent('landingpage');
  }, []);

  // when skipLogin optional param is present in the platform url
  const skipRedirectUrl = useRef<string>();
  useEffect(() => {
    skipRedirectUrl.current = window.sessionStorage.getItem('skipRedirectUrl') as string;
    // if isSilentAuth=true is provided with the user already authenticated by auth0
    if (window.sessionStorage.getItem('skipLandingPage') === 'true') {
      setSkipLandingPage(window.sessionStorage.getItem('skipLandingPage') as string);
    }
  }, []);

  const localhostRun = window.location.href;
  let redirectionUrl;
  if (localhostRun.indexOf('localhost') !== -1) {
    redirectionUrl = 'http://localhost:3000';
  } else {
    redirectionUrl = `https://${PLATFORM_LANDING_PAGE_URL}/platform-landing`;
  }

  if (skipLandingPage === 'true') {
    return <InformationWithProgressBackDrop open={true} message={t('nexusPlatform')} description={t('loadingYourExperience')} />;
  } else {
    return (
      <Grid container justifyContent="center" alignItems="center" sx={styles.landingpageGrid}>
        <Box sx={styles.landingpageBox}>
          <Container component="main" maxWidth="xs">
            <Box sx={styles.nexusLogoBoxPosition}>
              <Box component="img" sx={styles.nexusLogoBox} alt="Nexus by Hexagon." src={logoSrc} data-testid="Nexus-by-Hexagon-logo" />
            </Box>
            <TileComponent
              title={t('platformLandingpageTitle')}
              description={t('platformLandingpageDescription')}
              link={NEXUS_HOMEPAGE}
              textColor={themeMode === 'dark' ? 'common.white' : 'common.black'}
            />
            <Box sx={styles.signInBox}>
              <Button
                fullWidth
                variant="contained"
                sx={styles.signInButton}
                onClick={() => {
                  triggerNativeAppEvent('pre-login');
                  if (!skipRedirectUrl.current) {
                    if (target === '_blank') {
                      window.open(`${AUTH.issuer}/protocol/openid-connect/auth?response_type=code&client_id=${AUTH.clientID}&redirect_uri=${redirectionUrl}`, target);
                    } else {
                      signinRedirect(redirectionUrl);
                    }
                  }
                  window.sessionStorage.removeItem('skipRedirectUrl');
                }}
                data-testid="Sign-In"
              >
                Log In
              </Button>
            </Box>
            <Box sx={styles.signUpBox}>
              <Typography sx={styles.signUpButtonText}>
                <strong>
                  <Button
                    variant="text"
                    endIcon={<OpenInNewIcon />}
                    data-testid="Sign-Up"
                    onClick={() => {
                      window.open(
                        `https://${PLATFORM_LANDING_PAGE_URL}/home/<USER>/?redirectTo=platform/?isSilentAuth=true`,
                        platformLandingPageBrowserMobileSignUpWeb && (width < 800 || height < 800) ? '_blank' : target
                      );
                    }}
                  >
                    {t('platformLandingpageSignup')}
                  </Button>
                </strong>
              </Typography>
            </Box>
          </Container>
        </Box>
      </Grid>
    );
  }
};

export default LandingPage;
