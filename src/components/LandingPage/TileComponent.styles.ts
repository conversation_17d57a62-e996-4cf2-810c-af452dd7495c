import { SxProps, Theme, alpha } from '@mui/material/styles';
import cubes from '../../assets/cubes.png';

export const styles: SxProps<Theme> | any = {
  tileCard: {
    backgroundColor: (th: Theme) => alpha(th.palette.primary.main, 0.04),
    m: 4,
    backgroundImage: `url('${cubes}')`,
    backgroundPosition: 'right',
    backgroundRepeat: 'no-repeat'
  },
  tileBoxFlex: {
    borderRadius: 2,
    p: 4
  },
  tileBoxText: {
    height: '140px',
    textAlign: 'start'
  },
  titleText: {
    mb: 2
  },
  learnMoreText: {
    fontSize: '14px',
    display: 'flex',
    alignItems: 'center'
  }
};
