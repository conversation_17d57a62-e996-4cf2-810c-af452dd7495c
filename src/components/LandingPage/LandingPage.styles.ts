import { SxProps, Theme } from '@mui/material';

export const styles: SxProps<Theme> | any = {
  signUpBox: {
    display: 'flex',
    m: '8px 16px 16px 16px',
    justifyContent: 'space-around'
  },
  signUpButtonText: {
    fontSize: '14px'
  },
  signInBox: {
    alignItems: 'start',
    pr: 4,
    pl: 4
  },
  signInButton: {
    mt: 3,
    mb: 2,
    borderRadius: 1
  },
  nexusLogoBox: {
    maxHeight: '52px',
    maxWidth: '100%'
  },
  nexusLogoBoxPosition: {
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center'
  },
  landingpageBox: {
    display: 'flex',
    maxWidth: 400,
    minHeight: 540,
    alignItems: 'center',
    boxShadow: '0 12px 40px rgba(0,0,0,0.12)',
    borderRadius: '5px'
  },
  landingpageGrid: {
    minHeight: '100vh'
  }
};
