import React, { render } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import LandingPage from './LandingPage';
import hexagonNexusWhite from '/src/assets/hexagon_nexus_white.svg';
import hexagonNexusDark from '/src/assets/images/nexus_by_hexagon_dark.svg';
import { AuthProviderMock } from '../../utils/test-untils/mockUtils';

// Mock the useWindowSize hook
vi.mock('../../utils/hooks/UseWindowSize', () => ({
  default: () => ({
    width: 1024,
    height: 768
  })
}));

// Mock react-i18next
vi.mock('react-i18next', () => ({
  ...vi.importActual('react-i18next'),
  useTranslation: () => ({
    t: (str: string) => str
  })
}));

// Mock react-redux's useSelector
const mockUseSelector = vi.fn();
vi.mock('react-redux', () => ({
  ...vi.importActual('react-redux'),
  useSelector: (selector: any) => mockUseSelector(selector)
}));

describe('Platform LandingPage', () => {
  beforeEach(() => {
    // Mock sessionStorage.getItem to return 'false' when asked for 'skipLandingPage'
    Storage.prototype.getItem = vi.fn((key) => {
      if (key === 'skipLandingPage') return 'false';
      return null; // Return null or a default value for other keys
    });

    // Set up useSelector default return
    mockUseSelector.mockImplementation((selector) => {
      if (selector.name === 'userProfileSelector') {
        return 'light';
      }
      // When called with the actual Redux state, extract the theme
      return selector({ userProfile: { theme: 'light' } });
    });
  });

  it('should render LandingPage Card with SignIn, SignUp, Learn more buttons & title card', async () => {
    const component = render(
      <AuthProviderMock>
        <LandingPage />
      </AuthProviderMock>
    );
    expect(component.getByTestId('Sign-In')).toBeInTheDocument();
    expect(component.getByTestId('Sign-Up')).toBeInTheDocument();
    expect(component.getByTestId('Nexus-by-Hexagon-logo')).toBeInTheDocument();
    expect(component.getByTestId('Learn-More-link')).toBeInTheDocument();
    expect(component.getByTestId('Title-Card')).toBeInTheDocument();
    expect(component.getByTestId('Description-Line')).toBeInTheDocument();
  });

  it('renders the light mode logo', () => {
    // Set mock for light theme
    mockUseSelector.mockImplementation(() => {
      return 'light';
    });

    const component = render(
      <AuthProviderMock>
        <LandingPage />
      </AuthProviderMock>
    );

    expect(component.getByTestId('Nexus-by-Hexagon-logo')).toHaveAttribute('src', hexagonNexusDark);
  });

  it('renders the dark mode logo', () => {
    // Set mock for dark theme
    mockUseSelector.mockImplementation(() => {
      return 'dark';
    });

    const component = render(
      <AuthProviderMock>
        <LandingPage />
      </AuthProviderMock>
    );

    expect(component.getByTestId('Nexus-by-Hexagon-logo')).toHaveAttribute('src', hexagonNexusWhite);
  });
});
