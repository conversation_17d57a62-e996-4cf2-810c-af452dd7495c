import React from 'react';
import { Box, <PERSON><PERSON>, Card, Typography } from '@mui/material';
import OpenInNewIcon from '@mui/icons-material/Launch';
import { styles } from './TileComponent.styles';
import { useTranslation } from 'react-i18next';

export interface TileComponentProps {
  title: string;
  link: string;
  description: string;
  textColor: string;
}

export const TileComponent: React.FC<TileComponentProps> = ({ title, link, description, textColor }: TileComponentProps) => {
  const { t } = useTranslation();

  return (
    <Card sx={styles.tileCard}>
      <Box sx={styles.tileBoxFlex}>
        <Box sx={styles.tileBoxText}>
          <Typography sx={styles.titleText} variant="body1" fontWeight={700} color={textColor} data-testid="Title-Card">
            {title}
            <br></br>
          </Typography>
          <Typography variant="body2" fontWeight={400} color={textColor} data-testid="Description-Line">
            {description}
          </Typography>
          <Typography sx={styles.learnMoreText}>
            <strong>
              <Button
                color="secondary"
                variant="text"
                data-testid="Learn-More-link"
                endIcon={<OpenInNewIcon />}
                onClick={() => {
                  window.open(`${link}`, '_blank');
                }}
              >
                {t('platformLandingpageLearnMore')}
              </Button>
            </strong>
          </Typography>
        </Box>
      </Box>
    </Card>
  );
};
