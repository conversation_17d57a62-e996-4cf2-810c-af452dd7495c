import React, { useMemo } from 'react';
import { Box } from '@mui/material';
import { styles } from './HexWaterMark.styles';
export interface IWaterMarkProps {
  url: string;
}
const HexWaterMark = ({ url }: IWaterMarkProps) => {
  const checkEnv = useMemo(() => {
    if (url.toLowerCase().includes('dev') || url.toLowerCase().includes('localhost')) {
      return 'DEV';
    }

    if (url.toLowerCase().includes('qa')) {
      return 'QA';
    }

    if (url.toLowerCase().includes('sit')) {
      return 'SIT';
    }

    return undefined;
  }, [url]);

  return (
    <>
      {checkEnv && (
        <Box data-testid="hexmark" sx={styles.container}>
          {checkEnv}
        </Box>
      )}
    </>
  );
};

export default HexWaterMark;
