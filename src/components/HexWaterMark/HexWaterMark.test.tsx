import React, { act, render, screen } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import HexWaterMark from './HexWaterMark';

beforeEach(() => {
  // Setup code will go here if needed
  vi.resetAllMocks();
});
afterEach(async () => {
  await vi.clearAllMocks();
});
describe('user list testing', () => {
  it('Should display dev watermark if dev', async () => {
    await act(async () => {
      render(<HexWaterMark url="DEV.hexagon.com" />);
    });
    expect(screen.getByText('DEV')).toBeInTheDocument();
  });
  it('Should display qa watermark if qa', async () => {
    await act(async () => {
      render(<HexWaterMark url="qa.hexagon.com" />);
    });
    expect(screen.getByText('QA')).toBeInTheDocument();
  });
  it('Should display sit watermark if sit', async () => {
    await act(async () => {
      render(<HexWaterMark url="sit.hexagon.com" />);
    });
    expect(screen.getByText('SIT')).toBeInTheDocument();
  });
});
