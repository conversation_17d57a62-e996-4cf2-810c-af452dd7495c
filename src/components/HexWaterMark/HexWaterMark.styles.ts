import { SxProps, Theme } from '@mui/material';

export const styles: SxProps<Theme> | any = {
  container: {
    display: 'flex',
    alignItems: 'center',
    color: '#FFF',
    position: 'fixed',
    bottom: 0,
    height: '20px',
    justifyContent: 'center',
    left: 0,
    right: 0,
    opacity: 0.4,
    zIndex: 1000,
    backgroundColor: '#7ac5d7'
  }
};

import { ContainerProperty, Int64Property, Float64Property, StringProperty, StringArrayProperty, StringMapProperty, BoolProperty } from '@fluid-experimental/property-properties';
