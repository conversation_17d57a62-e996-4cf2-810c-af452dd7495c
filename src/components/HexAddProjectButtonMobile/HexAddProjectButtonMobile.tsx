import React from 'react';
import { Typo<PERSON>, Button, Tooltip, Box } from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import LaunchIcon from '@mui/icons-material/Launch';
import styles from './HexAddProjectButtonMobile.styles';
import { useTranslation } from 'react-i18next';
import { DIGITAL_PRODUCT_URL } from '../../configuration/URL.config';
import { useFlags } from 'launchdarkly-react-client-sdk';

export interface IHexAddProjects {
  smallScreen?: boolean;
  createProject?: React.Dispatch<React.SetStateAction<boolean>>;
  addEnabled?: boolean;
  labels?: IHexLabels;
}

interface IHexLabels {
  addProject?: string;
  addProjectDescription?: string;
  addProjectTooltipDisabled?: string;
  addProjectTooltipEnabled?: string;
}

export const HexAddProjectButtonMobile = ({ createProject, addEnabled = false, labels }: IHexAddProjects) => {
  const { t } = useTranslation();
  const { designerButtonVisible } = useFlags();

  return (
    <>
      {designerButtonVisible && (
        <Box sx={styles.addNew}>
          <Box>
            <Button sx={{ width: '100%' }} variant="contained" color="primary" onClick={() => window.open(DIGITAL_PRODUCT_URL, '_blank')} data-testid="add-desiger-document">
              <LaunchIcon />
              <Typography data-testid="add-desiger-document-title" fontFamily={'Open Sans'} fontSize={'15px'} fontWeight={'400'}>
                {t('digitalProduct')}
              </Typography>
            </Button>
          </Box>
        </Box>
      )}
      <Box sx={styles.addNew}>
        <Tooltip
          title={
            !addEnabled
              ? labels && labels.addProjectTooltipDisabled
                ? labels.addProjectTooltipDisabled
                : `${t('createNewProjectDisabled')}`
              : labels && labels.addProjectTooltipEnabled
              ? labels.addProjectTooltipEnabled
              : `${t('createNewProject')}`
          }
          arrow
        >
          <Box>
            <Button
              sx={{ width: '100%' }}
              variant="contained"
              color="primary"
              onClick={() => createProject && createProject(true)}
              data-testid="add-projects-card"
              disabled={!addEnabled}
            >
              <AddIcon sx={{ color: !addEnabled ? 'text.disabled' : 'common.white' }} />
              <Typography
                sx={{ color: !addEnabled ? 'text.disabled' : 'common.white' }}
                data-testid="add-projects-title"
                fontFamily={'Open Sans'}
                fontSize={'15px'}
                fontWeight={'400'}
              >
                {labels && labels.addProject ? labels.addProject : `${t('createNew')}`}
              </Typography>
            </Button>
          </Box>
        </Tooltip>
      </Box>
    </>
  );
};
