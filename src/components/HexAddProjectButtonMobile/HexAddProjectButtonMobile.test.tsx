import React, { fireEvent, render } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import { HexAddProjectButtonMobile } from './HexAddProjectButtonMobile';

describe('HexAddProjectButtonMobile', () => {
  it('should render add project card', () => {
    const component = render(<HexAddProjectButtonMobile />);
    expect(component.getByTestId('add-projects-card')).toBeInTheDocument();
    expect(component.getByTestId('AddIcon')).toBeInTheDocument();
    expect(component.getByTestId('add-projects-title')).toBeInTheDocument();
  });

  it('should show the create project screen on click', async () => {
    const clkev = vi.fn();

    const { getByTestId } = render(<HexAddProjectButtonMobile createProject={clkev} addEnabled={true} />);
    const newDocButton = getByTestId('add-projects-card');
    vi.spyOn(newDocButton, 'click');
    fireEvent.click(newDocButton);

    expect(clkev).toHaveBeenCalled();
  });

  it('should not show the create project screen on click if disabled', async () => {
    const clkev = vi.fn();

    const { getByTestId } = render(<HexAddProjectButtonMobile createProject={clkev} addEnabled={false} />);
    const newDocButton = getByTestId('add-projects-card');
    vi.spyOn(newDocButton, 'click');
    fireEvent.click(newDocButton);

    expect(clkev).not.toHaveBeenCalled();
  });
});
