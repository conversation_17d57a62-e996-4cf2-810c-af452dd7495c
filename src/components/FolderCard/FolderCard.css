.folderCardStyle .NexusComplexCard-rectangleImageBackground {
  width: 10%;
  background: none;
  margin: 10px;
  padding-left: 0;
  padding-right: 0;
}

.MuiCardContent-root {
  padding-left: 0.5rem;
}

.NexusComplexCard-root .NexusComplexCard-rectangleImageBackground{
  padding:5px !important;
}

.NexusComplexCard-placeholderImageWrapper{
  padding: 10px !important;
}

.folderCardStyle {
  width: 100%;
  height: 72px;
  border-radius: 8px;
  border: 1px solid #e6e6e6;
  display: flex;
  justify-content: center;
  align-items: center;
}

@media only screen and (max-width: 900px) {
  .folderCardStyle {
    width: 100% !important;
  }

  .hex-foldersList-tile-mobile-card .NexusComplexCard-rectangleImageBackground {
    width: 5%;
    background: none;
  }

  .foldersContainerStyle {
    overflow-y: auto;
    height: calc(100vh - 307px);
  }
}

@media only screen and (max-width: 600px) {
  .folderCardStyle {
    width: 100% !important;
  }

  .hex-foldersList-tile-mobile-card .NexusComplexCard-rectangleImageBackground {
    width: 10%;
    background: none;
  }
}
