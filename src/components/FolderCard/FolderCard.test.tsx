import React, { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import { FolderCard } from './FolderCard';
import theme from '@nexusui/theme';
import { Experimental_CssVarsProvider as CssVarsProvider } from '@mui/material/styles';
import { describe, it, expect, vi, beforeAll } from 'vitest';
import { AuthProviderMock, StoreProviderMock } from '../../utils/test-untils/mockUtils';

vi.mock('@nexusplatform/react', () => ({
  useHexAuth: () => ({
    getAccessTokenSilently: vi.fn().mockResolvedValue('mock-token')
  })
}));

describe('<FolderCard/>', () => {
  beforeAll(() => {
    Object.defineProperty(window, 'matchMedia', {
      writable: true,
      value: vi.fn().mockImplementation((query) => ({
        matches: false,
        media: query,
        onchange: null,
        addListener: vi.fn(), // Deprecated
        removeListener: vi.fn(), // Deprecated
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
        dispatchEvent: vi.fn()
      }))
    });
  });
  const folder = {
    id: '7951321c-383c-4074-92e3-d2906073a51e',
    organizationId: 'org_TXpDz8lg2K17UXJB',
    name: 'Project Test',
    description: 'Project ABC for Motors',
    documentCount: 4,
    tags: ['test', 'demo'],
    status: 'active',
    acl: [
      {
        userId: 'user123',
        role: 'owner',
        defaultDocumentAccess: 'readWrite',
        defaultCollaborator: true,
        documentCount: 4,
        access: 'readWrite'
      }
    ],
    createdBy: 'user123',
    createdByDate: '2022-07-01T08:35:33.477Z',
    lastModifiedBy: 'user123',
    lastModifiedByDate: '2022-07-01T08:35:33.477Z',
    canUnfollow: false
  };
  const onClickProjectCard = vi.fn();
  const handleDeleteProjectFolder = vi.fn();
  const handleRenameFolder = vi.fn();
  it('should render projectcard', () => {
    render(
      <AuthProviderMock>
        <StoreProviderMock>
          <CssVarsProvider theme={theme}>
            <FolderCard
              id={folder.id ?? ''}
              folder={folder}
              onClickProjectCard={onClickProjectCard}
              handleDeleteProjectFolder={handleDeleteProjectFolder}
              isProjectCardClicked={false}
              hexAuthToken={''}
              handleRenameFolder={() => handleRenameFolder(folder)}
            />
          </CssVarsProvider>
        </StoreProviderMock>
      </AuthProviderMock>
    );

    const card = screen.getByTestId('NexusComplexCard-root');
    expect(card).toBeInTheDocument();
  });

  it('should have Project Name', () => {
    render(
      <AuthProviderMock>
        <StoreProviderMock>
          <CssVarsProvider theme={theme}>
            <FolderCard
              id={folder.id ?? ''}
              folder={folder}
              onClickProjectCard={onClickProjectCard}
              handleDeleteProjectFolder={handleDeleteProjectFolder}
              handleRenameFolder={() => handleRenameFolder(folder)}
              isProjectCardClicked={false}
              hexAuthToken={''}
            />
          </CssVarsProvider>
        </StoreProviderMock>
      </AuthProviderMock>
    );
    const element = screen.getAllByTestId('NexusComplexCard-primaryText');
    expect(element[0]).toHaveTextContent('Project Test');
  });
});
