import React, { useContext, useMemo, useState } from 'react';
import { Photo } from '@mui/icons-material';
import { ComplexCard } from '@nexusui/components';
import { t } from 'i18next';
import { IFolderList } from '../../models/Folders';
import folderIcon from '/src/assets/FolderIcon.svg';
import ShareDialogComponent from '../ShareDialog';
import './FolderCard.css';
import { sdcPrivilegeContext } from '../../context/SdcUserPrivilegesContext';
import { useSelector } from 'react-redux';
import { RootState } from '../../redux';

interface IFolderCard {
  id: string;
  folder: IFolderList;
  onClickProjectCard: (id: string, projectName: string) => void;
  hexAuthToken: string;
  isProjectCardClicked: boolean;
  handleRenameFolder?: () => void;
  handleDeleteProjectFolder?: () => void;
  handleUnfollowProjectFolder?: () => void;
  canUnfollow?: boolean;
}

export const FolderCard = (props: IFolderCard) => {
  const { id, folder, onClickProjectCard, handleDeleteProjectFolder, handleRenameFolder, hexAuthToken, isProjectCardClicked, handleUnfollowProjectFolder, canUnfollow } = props;
  const [showShareDialog, setShowShareDialog] = useState(false);
  const user = useSelector((state: RootState) => state.userProfile.user);

  const userDocumentCount = folder?.acl.find((item) => user?.id === item.userId);
  const userRole = userDocumentCount?.role;
  const { sdcPrivileges } = useContext(sdcPrivilegeContext);

  const handleRename = () => {
    if (handleRenameFolder && id) {
      handleRenameFolder();
    }
  };

  const handleUnfollow = () => {
    if (handleUnfollowProjectFolder && id) {
      handleUnfollowProjectFolder();
    }
  };

  const handleShare = () => {
    setShowShareDialog(true);
  };
  const handleDelete = () => {
    if (handleDeleteProjectFolder && id) {
      handleDeleteProjectFolder();
    }
  };

  const menuItems: Array<{ label: string; onClick: () => void }> = useMemo(() => {
    const items: Array<{ label: string; onClick: (folder?: IFolderList) => void }> = [];
    if (sdcPrivileges.includes('update:projectinstance')) {
      items.push({ label: t('rename'), onClick: handleRename });
    }
    if (sdcPrivileges.includes('unfollow:projectinstance') && canUnfollow) {
      items.push({ label: t('unfollow'), onClick: handleUnfollow });
    }
    if (sdcPrivileges.includes('share:projectinstance') && userRole === 'owner') {
      items.push({ label: t('share'), onClick: handleShare });
    }
    if (sdcPrivileges.includes('delete:projectinstance')) {
      items.push({ label: t('delete'), onClick: handleDelete });
    }
    return items;
  }, []);
  return (
    <>
      <ComplexCard
        key={folder.id}
        image={folderIcon}
        imagePlaceholder={<Photo fontSize={'inherit'} />}
        primaryText={folder?.name}
        tertiaryText={`${userDocumentCount?.documentCount} ${t('documents')}`}
        mode={'rectangle'}
        className={'folderCardStyle'}
        onClick={() => onClickProjectCard(folder.id, folder.name)}
        menuActions={menuItems}
      ></ComplexCard>
      {/* {showDeleteProjectModal && <HexDeleteDialog showDeleteDialog={showDeleteProjectModal} handleClose={handleCloseProject} handleDelete={handleDelete} project={folder} />} */}

      {showShareDialog && !isProjectCardClicked && (
        <ShareDialogComponent show={showShareDialog} authToken={hexAuthToken} onClose={() => setShowShareDialog(false)} instanceId={folder?.id} isProjectOrSDC={'Project'} />
      )}
    </>
  );
};
