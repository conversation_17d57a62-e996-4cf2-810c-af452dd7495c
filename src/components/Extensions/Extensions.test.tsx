import React, { render } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import { Extensions } from './Extensions';

// Mock the useAuth hook
vi.mock('@nexusui/connected-components', async (importOriginal) => {
  // Import the original module first
  const originalModule = (await importOriginal()) as Record<string, unknown>;

  return {
    ...originalModule,
    // Override the specific hooks and components that use auth
    useAuth: () => mockAuthContext,
    useAccessToken: () => 'mock-access-token',
    ConnectedExtensions: ({ appTags, appVariant, children }: { appTags?: string[]; appVariant?: string; children?: React.ReactNode }) => (
      <div data-testid="NexusExtensions-root">
        <h2 data-testid="NexusExtensions-title">Extensions</h2>
        <div data-testid="NexusExtensions-menuList">
          {/* Mock the extension menu items */}
          <div data-testid="NexusExtensions-menuItem">Extension 1</div>
          <div data-testid="NexusExtensions-menuItem">Extension 2</div>
        </div>
        {children}
      </div>
    )
  };
});

const mockAuthContext = {
  user: {
    profile: {
      name: 'Test User',
      email: '<EMAIL>'
    }
  }
};
describe('Extensions', () => {
  it.only('should render Extensions Card', async () => {
    const component = render(<Extensions />);
    await expect(component.getByTestId('NexusExtensions-root')).toBeInTheDocument();
  });
  it('should render Card Title', async () => {
    const component = render(<Extensions />);
    await expect(component.getByTestId('NexusExtensions-title')).toBeInTheDocument();
  });
  it('should render Icons sections', async () => {
    const component = render(<Extensions />);
    await expect(component.getByTestId('NexusExtensions-menuList')).toBeInTheDocument();
  });
});
