import React, { fireEvent, render, screen } from '@testing-library/react';
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { VariantDialog, IVariantProps } from './VariantDialog';
import { ReactElement } from 'react';

// Create a proper mock for @nexusui/components
// This is necessary because we can't easily modify the library's internal usage of useFormContext
vi.mock('@nexusui/components', () => {
  return {
    // Mock the specific components used in VariantDialog
    FormInput: ({ name, label, required, ...props }: { name: string; label: string; required?: boolean; [key: string]: any }) => {
      // Create a simple form input that doesn't depend on form context
      return (
        <div>
          <label htmlFor={name}>{label}</label>
          <input id={name} name={name} required={required} data-testid={`input-${name}`} {...props} />
        </div>
      );
    }
  };
});

describe('VariantDialog', () => {
  const onCancel = vi.fn();
  const onAdd = vi.fn().mockImplementation(() => {
    return Promise.resolve();
  });
  const prop: IVariantProps = { showVariantDialog: true, onAdd: onAdd, onCancel: onCancel };

  beforeEach(() => {
    // Clean up between tests
    vi.resetAllMocks();
    document.body.innerHTML = '';
  });

  it('should render variant dialog', async () => {
    render(<VariantDialog {...prop} />);
    expect(await screen.findByTestId('NexusVariant-name')).toBeInTheDocument();
    expect(await screen.findByTestId('NexusVariant-description')).toBeInTheDocument();
    expect(await screen.findByTestId('NexusVariant-documentationUrl')).toBeInTheDocument();
    expect(await screen.findByTestId('NexusVariant-communityForumsUrl')).toBeInTheDocument();
    expect(await screen.findByTestId('NexusVariant-requestHelpUrl')).toBeInTheDocument();
  });

  it('should fire close event', async () => {
    render(<VariantDialog {...prop} />);
    fireEvent.click(await screen.findByTestId('NexusDialog-action-Cancel'));
    expect(onCancel).toBeCalledTimes(1);
  });

  it('should fire add event', async () => {
    // Mock implementation of handleSubmit to directly call the callback
    vi.mock('react-hook-form', () => ({
      ...vi.importActual('react-hook-form'),
      useForm: () => ({
        handleSubmit: (callback) => () => {
          // Directly call the callback with test data
          callback({
            name: 'Test Variant',
            tag: 'test-tag',
            description: '',
            url: '',
            windowType: 'WebBrowser'
          });
        },
        formState: { isValid: true, errors: {} },
        watch: () => 'Test Variant', // Make VariantName truthy
        setValue: vi.fn()
      }),
      FormProvider: ({ children }: { children: ReactElement | ReactElement[] }) => <>{children}</>
    }));

    // Re-render with the updated mock
    render(<VariantDialog {...prop} />);

    // Click the Add button
    fireEvent.click(await screen.findByTestId('NexusDialog-action-Add'));

    // Verify the callback was called
    expect(onAdd).toHaveBeenCalledTimes(1);

    expect(onAdd).toHaveBeenCalledWith(
      expect.objectContaining({
        name: 'Test Variant',
        tag: 'test-tag',
        windowType: 'WebBrowser'
      })
    );
  });
});
