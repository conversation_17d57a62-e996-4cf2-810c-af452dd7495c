import React, { ReactElement } from 'react';
import { fireEvent, render, screen } from '@testing-library/react';
import { EditExtensionDialog, IEditExtensionProps } from './EditExtensionDialog';
import { vi, describe, it, expect, beforeEach } from 'vitest';

// Create a proper mock for @nexusui/components
// This is necessary because we can't easily modify the library's internal usage of useFormContext
vi.mock('@nexusui/components', () => {
  return {
    // Mock the specific components used in VariantDialog
    FormInput: ({ name, label, required, ...props }: { name: string; label: string; required?: boolean; [key: string]: any }) => {
      // Create a simple form input that doesn't depend on form context
      return (
        <div>
          <label htmlFor={name}>{label}</label>
          <input id={name} name={name} required={required} data-testid={`input-${name}`} {...props} />
        </div>
      );
    }
  };
});

describe('EditExtensionDialog', () => {
  const onCancel = vi.fn();
  const onUpdate = vi.fn().mockImplementation(() => {
    return Promise.resolve();
  });
  const data = {
    baseId: 'baseId',
    name: 'name',
    description: 'description',
    url: 'url',
    openIn: 'tab'
  };

  const prop: IEditExtensionProps = { showEditExtensionDialog: true, onUpdate: onUpdate, onCancel: onCancel, data: data };

  beforeEach(() => {
    // Clean up between tests
    vi.resetAllMocks();
    document.body.innerHTML = '';
  });

  it('should render edit extension dialog', async () => {
    render(<EditExtensionDialog {...prop} />);
    expect(await screen.findByTestId('NexusEditExtension-name')).toBeInTheDocument();
    expect(await screen.findByTestId('NexusEditExtension-description')).toBeInTheDocument();
    expect(await screen.findByTestId('NexusEditExtension-url')).toBeInTheDocument();
  });

  it('should fire close event', async () => {
    render(<EditExtensionDialog {...prop} />);
    fireEvent.click(await screen.findByTestId('NexusDialog-action-Cancel'));
    expect(onCancel).toBeCalledTimes(1);
  });

  it('should fire add event', async () => {
    // Mock implementation of handleSubmit to directly call the callback
    vi.mock('react-hook-form', () => ({
      ...vi.importActual('react-hook-form'),
      useForm: () => ({
        handleSubmit: (callback) => () => {
          // Directly call the callback with test data
          callback({
            name: 'Test Extension updated',
            tag: 'test-tag',
            description: '',
            url: '',
            windowType: 'WebBrowser'
          });
        },
        formState: { isValid: true, errors: {} },
        watch: () => 'Test Extension updated', // Make BaseExtensionName truthy
        setValue: vi.fn()
      }),
      FormProvider: ({ children }: { children: ReactElement | ReactElement[] }) => <>{children}</>
    }));

    // Re-render with the updated mock
    render(<EditExtensionDialog {...prop} />);

    // Click the Update button
    fireEvent.click(await screen.findByTestId('NexusDialog-action-Update'));

    // Verify the callback was called
    expect(onUpdate).toHaveBeenCalledTimes(1);

    expect(onUpdate).toHaveBeenCalledWith(
      expect.objectContaining({
        name: 'Test Extension updated',
        tag: 'test-tag',
        windowType: 'WebBrowser'
      })
    );
  });
});
