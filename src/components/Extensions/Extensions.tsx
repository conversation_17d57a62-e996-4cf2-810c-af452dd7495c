import React from 'react';
import { ConnectedExtensions } from '@nexusui/connected-components';
import { Box } from '@mui/material';

export const Extensions = () => {
  let appTags;
  let appVariant;
  const withVariantURL = new URL(window.location.href);
  if (withVariantURL.searchParams.has('appVariant')) {
    appTags = withVariantURL.searchParams.get('app');
    appVariant = withVariantURL.searchParams.get('appVariant');
  } else {
    const url = window.location.href.toString().replace(/&/g, ',');
    const urlParams = new URLSearchParams(new URL(url).search);
    appTags = urlParams.get('app');
  }
  return (
    <Box sx={{ height: '100vh', overflowY: 'auto' }}>
      <ConnectedExtensions appTags={appTags} appVariant={appVariant} />
    </Box>
  );
};

export default Extensions;
