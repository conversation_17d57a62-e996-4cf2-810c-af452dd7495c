import { Button, Dialog, DialogActions, DialogTitle, FormLabel, Box, Typography } from '@mui/material';
import React from 'react';
import Grid from '@mui/material/Grid';
import { FormProvider, useForm } from 'react-hook-form';
import { FormInput } from '@nexusui/components';
import { FormControlLabel, Radio, RadioGroup } from '@mui/material';
import { openInType, AddBaseExtensionFormDataType } from '../../configuration/extensions';
import { useTranslation } from 'react-i18next';

export interface IEditExtensionProps {
  showEditExtensionDialog: boolean;
  data: any;
  onUpdate: (data) => void;
  onCancel: () => void;
}
export const EditExtensionDialog = ({ showEditExtensionDialog, onCancel, onUpdate, data }: IEditExtensionProps) => {
  const { t } = useTranslation();
  const defaultValues = {
    name: data.name,
    description: data.description,
    tag: data.tag,
    url: data.url,
    windowType: data.openIn
  };

  const methods = useForm<AddBaseExtensionFormDataType>({ defaultValues, mode: 'onChange' });
  const { handleSubmit, formState, watch, setValue } = methods;
  const ExtensionName = watch('name');
  const windowType = watch('windowType');
  const handleChange = (event) => {
    setValue('windowType', event.target.value);
  };
  const onSubmit = (data: AddBaseExtensionFormDataType) => {
    onUpdate(data);
  };
  const { isValid } = formState;

  return (
    <Dialog
      data-testid="EditExtension-dialog"
      open={showEditExtensionDialog}
      onClose={onCancel}
      PaperProps={{
        sx: {
          width: '60%'
        }
      }}
    >
      <DialogTitle>{t('editExtension')}</DialogTitle>
      <FormProvider {...methods}>
        <Grid sx={{ pt: 2, pl: 5, pr: 5 }} container spacing={6}>
          <Grid item xs={12}>
            <FormInput fullWidth name="name" label={t('name')} required data-testid="NexusEditExtension-name" />
          </Grid>
          <Grid item xs={12}>
            <FormInput fullWidth name="description" label={t('description')} data-testid="NexusEditExtension-description" />
          </Grid>
          <Grid item xs={12}>
            <FormInput
              fullWidth
              name="tag"
              label={t('tag')}
              data-testid="NexusEditExtension-tag"
              rules={{
                required: true,
                minLength: {
                  value: 3,
                  message: t('tagLengthError')
                },
                maxLength: {
                  value: 50,
                  message: t('tagLengthError')
                }
              }}
            />
          </Grid>
          <Grid item xs={12}>
            <FormInput fullWidth name="url" label={t('url')} data-testid="NexusEditExtension-url" />
          </Grid>
          <Grid item xs={12}>
            <FormLabel sx={{ pl: 2 }}>
              <strong>{t('openLinkIn')}</strong>
            </FormLabel>
            <RadioGroup name="windowType" sx={{ pt: 2 }} value={windowType} onChange={handleChange}>
              <Box sx={{ border: '1px dotted', margin: '5px', padding: '5px', display: 'flex', alignItems: 'center' }}>
                <Box sx={{ display: 'flex', flexDirection: 'column' }}>
                  <strong>{t('systemBrowser')}</strong>
                  <Typography fontSize={'small'}>{t('systemBrowserDescription')}</Typography>
                </Box>
                <FormControlLabel value={openInType.WebBrowser} control={<Radio />} label={''} />
              </Box>
              <Box sx={{ border: '1px dotted', margin: '5px', padding: '5px', display: 'flex', alignItems: 'center' }}>
                <Box sx={{ display: 'flex', flexDirection: 'column' }}>
                  <strong>{t('floatingWindow')}</strong>
                  <Typography fontSize={'small'}>{t('floatingWindowDescription')}</Typography>
                </Box>
                <FormControlLabel value={openInType.CefBrowser} control={<Radio />} label={''} />
              </Box>
            </RadioGroup>
          </Grid>
        </Grid>
        <DialogActions sx={{ display: 'flex', justifyContent: 'flex-end', flexWrap: 'wrap' }}>
          <Button onClick={onCancel} color="primary" sx={{ m: 0 }} data-testid="NexusDialog-action-Cancel">
            {t('cancel')}
          </Button>
          <Button onClick={handleSubmit(onSubmit)} disabled={!isValid || !ExtensionName} data-testid="NexusDialog-action-Update">
            {t('update')}
          </Button>
        </DialogActions>
      </FormProvider>
    </Dialog>
  );
};
