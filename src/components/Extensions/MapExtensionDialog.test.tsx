import React, { ReactElement } from 'react';
import { fireEvent, render, screen } from '@testing-library/react';
import { MapExtensionDialog, IMapExtensionProps } from './MapExtensionDialog';
import { IExtension } from '../manageExtensions';
import { describe, it, expect, vi, beforeEach } from 'vitest';

// Create a proper mock for @nexusui/components
// This is necessary because we can't easily modify the library's internal usage of useFormContext
vi.mock('@nexusui/components', () => {
  return {
    // Mock the specific components used in VariantDialog
    FormInput: ({ name, label, required, ...props }: { name: string; label: string; required?: boolean; [key: string]: any }) => {
      // Create a simple form input that doesn't depend on form context
      return (
        <div>
          <label htmlFor={name}>{label}</label>
          <input id={name} name={name} required={required} data-testid={`input-${name}`} {...props} />
        </div>
      );
    },
    // Mock FormSelect component
    FormSelect: ({
      name,
      label,
      options = [],
      defaultValue,
      rules,
      MenuProps,
      ...props
    }: {
      name: string;
      label: string;
      options: Array<{ label: string; value: string }>;
      defaultValue?: string;
      rules?: any;
      MenuProps?: any;
      [key: string]: any;
    }) => {
      return (
        <div data-testid={`${name}-container`}>
          <label htmlFor={name}>{label}</label>
          <select id={name} name={name} data-testid={name === 'extensions' ? 'AddExtension-dialog-select' : `select-${name}`} defaultValue={defaultValue} {...props}>
            <option value="">Select {label}</option>
            {options.map((option, index) => (
              <option key={index} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        </div>
      );
    }
  };
});

describe('MapExtensionDialog', () => {
  const onCancel = vi.fn();
  const onSave = vi.fn().mockImplementation(() => {
    return Promise.resolve();
  });
  const data: IExtension[] = [
    {
      id: '1',
      name: 'Extension 01',
      description: 'Extension 01 description',
      url: 'https://www.extension01.com',
      tag: 'tag1',
      openIn: 'newTab'
    },
    {
      id: '2',
      name: 'Extension 02',
      description: 'Extension 02 description',
      url: 'https://www.extension02.com',
      tag: 'tag2',
      openIn: 'newTab'
    }
  ];
  const extensionOptions = [];
  const prop: IMapExtensionProps = { showMapExtensionDialog: true, onSave: onSave, onCancel: onCancel, extensionOptions: extensionOptions, data: data };

  beforeEach(() => {
    // Clean up between tests
    vi.resetAllMocks();
    document.body.innerHTML = '';
  });

  it('should render add application extension dialog', async () => {
    render(<MapExtensionDialog {...prop} />);
    expect(await screen.findByTestId('AddExtension-dialog-select')).toBeInTheDocument();
    expect(await screen.findByTestId('NexusAddAppExtension-description')).toBeInTheDocument();
    expect(await screen.findByTestId('NexusAddAppExtension-url')).toBeInTheDocument();
  });

  it('should fire close event', async () => {
    render(<MapExtensionDialog {...prop} />);
    fireEvent.click(await screen.findByTestId('NexusDialog-action-Cancel'));
    expect(onCancel).toBeCalledTimes(1);
  });

  it('should fire add event', async () => {
    // Mock implementation of handleSubmit to directly call the callback
    vi.mock('react-hook-form', () => ({
      ...vi.importActual('react-hook-form'),
      useForm: () => ({
        handleSubmit: (callback) => () => {
          // Directly call the callback with test data
          callback({
            id: '3',
            name: 'Extension 03',
            description: 'Extension 03 description',
            url: 'https://www.extension03.com',
            tag: 'tag3',
            openIn: 'newTab'
          });
        },
        formState: { isValid: true, errors: {} },
        watch: () => 'Extension 03', // Make ExtensionName truthy
        setValue: vi.fn()
      }),
      FormProvider: ({ children }: { children: ReactElement | ReactElement[] }) => <>{children}</>
    }));

    // Re-render with the updated mock
    render(<MapExtensionDialog {...prop} />);

    // Click the Save button
    fireEvent.click(await screen.findByTestId('NexusDialog-action-Add'));

    // Verify the callback was called
    expect(onSave).toHaveBeenCalledTimes(1);

    expect(onSave).toHaveBeenCalledWith(
      expect.objectContaining({
        id: '3',
        name: 'Extension 03',
        description: 'Extension 03 description',
        url: 'https://www.extension03.com',
        tag: 'tag3',
        openIn: 'newTab'
      })
    );
  });
});
