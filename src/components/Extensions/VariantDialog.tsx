import { Button, Dialog, DialogActions, DialogTitle } from '@mui/material';
import React from 'react';
import Grid from '@mui/material/Grid';
import { FormProvider, useForm } from 'react-hook-form';
import { FormInput } from '@nexusui/components';
import { VariantFormDataType, IVariant } from '../../configuration/extensions';
import { useTranslation } from 'react-i18next';

export interface IVariantProps {
  showVariantDialog: boolean;
  onAdd: (data) => void;
  onCancel: () => void;
  selectedVariant?: IVariant;
}
export const VariantDialog = ({ showVariantDialog, onCancel, onAdd, selectedVariant }: IVariantProps) => {
  const { t } = useTranslation();
  const defaultValues = {
    name: selectedVariant ? selectedVariant.name : '',
    description: selectedVariant ? selectedVariant.description : '',
    documentationUrl: selectedVariant ? selectedVariant.documentationUrl : '',
    communityForumsUrl: selectedVariant ? selectedVariant.communityForumsUrl : '',
    requestHelpUrl: selectedVariant ? selectedVariant.requestHelpUrl : ''
  };

  const methods = useForm<VariantFormDataType>({ defaultValues, mode: 'onChange' });
  const { handleSubmit, formState, watch } = methods;
  const variantName = watch('name');
  const variantDocumentationUrl = watch('documentationUrl');
  const variantCommunityForumsUrl = watch('communityForumsUrl');
  const variantRequestHelpUrl = watch('requestHelpUrl');

  const onSubmit = (data: VariantFormDataType) => {
    onAdd(data);
  };
  const { isValid } = formState;

  return (
    <Dialog
      data-testid="Variant-dialog"
      open={showVariantDialog}
      onClose={onCancel}
      PaperProps={{
        sx: {
          width: '60%'
        }
      }}
    >
      <DialogTitle>{selectedVariant ? t('editVariant') : t('addVariant')}</DialogTitle>
      <FormProvider {...methods}>
        <Grid sx={{ pt: 2, pl: 5, pr: 5 }} container spacing={6}>
          <Grid item xs={12}>
            <FormInput fullWidth name="name" label={t('name')} required data-testid="NexusVariant-name" />
          </Grid>
          <Grid item xs={12}>
            <FormInput fullWidth name="description" label={t('description')} data-testid="NexusVariant-description" />
          </Grid>
          <Grid item xs={12}>
            <FormInput fullWidth name="documentationUrl" label={t('documentationUrl')} required data-testid="NexusVariant-documentationUrl" />
          </Grid>
          <Grid item xs={12}>
            <FormInput fullWidth name="communityForumsUrl" label={t('communityForumsUrl')} required data-testid="NexusVariant-communityForumsUrl" />
          </Grid>
          <Grid item xs={12}>
            <FormInput fullWidth name="requestHelpUrl" label={t('requestHelpUrl')} required data-testid="NexusVariant-requestHelpUrl" />
          </Grid>
        </Grid>
        <DialogActions sx={{ display: 'flex', justifyContent: 'flex-end', flexWrap: 'wrap' }}>
          <Button onClick={onCancel} color="primary" sx={{ m: 0 }} data-testid="NexusDialog-action-Cancel">
            {t('cancel')}
          </Button>
          <Button
            onClick={handleSubmit(onSubmit)}
            disabled={!isValid || !variantName || !variantDocumentationUrl || !variantCommunityForumsUrl || !variantRequestHelpUrl}
            data-testid="NexusDialog-action-Add"
          >
            {selectedVariant ? t('update') : t('add')}
          </Button>
        </DialogActions>
      </FormProvider>
    </Dialog>
  );
};
