import { Button, Dialog, <PERSON>alogActions, DialogTitle, FormLabel, Box, Typography } from '@mui/material';
import React, { useEffect } from 'react';
import Grid from '@mui/material/Grid';
import { FormProvider, useForm } from 'react-hook-form';
import type { OptionType } from '@nexusui/components';
import { FormSelect, FormInput } from '@nexusui/components';
import { IExtension } from '../../configuration/extensions';
import { FormControlLabel, Radio, RadioGroup } from '@mui/material';
import { openInType, AddAppExtensionFormDataType } from '../../configuration/extensions';
import { useTranslation } from 'react-i18next';

export interface IMapExtensionProps {
  showMapExtensionDialog: boolean;
  extensionOptions?: OptionType[];
  data: IExtension[];
  onSave: (data) => void;
  onCancel: () => void;
}

export const MapExtensionDialog = ({ showMapExtensionDialog, extensionOptions = [], onCancel, onSave, data }: IMapExtensionProps) => {
  const { t } = useTranslation();
  const defaultValues = {
    extensions: '',
    description: '',
    tag: '',
    url: ''
  };
  extensionOptions = data.map((item) => ({ label: item.name, value: item.name })) || [];
  const methods = useForm<AddAppExtensionFormDataType>({ defaultValues, mode: 'onChange' });
  const { handleSubmit, formState, watch, setValue } = methods;
  const selectedExtensionName = watch('extensions');
  const windowType = watch('windowType');
  const description = watch('description');
  const tag = watch('tag');
  const url = watch('url');
  const handleOpenInChange = (event) => {
    setValue('windowType', event.target.value);
  };

  useEffect(() => {
    const selectedExtension = data.find((item) => item.name === selectedExtensionName);
    setValue('id', selectedExtension?.id || '');
    setValue('description', selectedExtension?.description || '');
    setValue('tag', selectedExtension?.tag || '');
    setValue('url', selectedExtension?.url || '');
    setValue('windowType', selectedExtension?.openIn ?? '');
  }, [selectedExtensionName]);

  const onSubmit = (data: AddAppExtensionFormDataType) => {
    onSave(data);
  };
  const { isValid } = formState;

  return (
    <Dialog
      data-testid="AddExtension-dialog"
      open={showMapExtensionDialog}
      onClose={onCancel}
      PaperProps={{
        sx: {
          width: '60%'
        }
      }}
    >
      <DialogTitle>{t('addExtension')}</DialogTitle>
      <FormProvider {...methods}>
        <Grid sx={{ pt: 2, pl: 5, pr: 5 }} container spacing={6}>
          {extensionOptions.length > 0 && (
            <Grid item xs={12}>
              <FormSelect
                MenuProps={{
                  PaperProps: {
                    style: { maxHeight: '200px' }
                  }
                }}
                rules={{ required: true }}
                fullWidth
                name="extensions"
                defaultValue=""
                label={t('baseExtensions')}
                aria-label="Extension Select"
                data-testid="AddExtension-dialog-select"
                options={extensionOptions}
              />
            </Grid>
          )}
          <Grid item xs={12}>
            <FormInput fullWidth name="description" label={t('description')} focused={!!description} data-testid="NexusAddAppExtension-description" />
          </Grid>
          <Grid item xs={12}>
            <FormInput
              fullWidth
              name="tag"
              label={t('tag')}
              focused={!!tag}
              data-testid="NexusAddAppExtension-tag"
              rules={{
                required: true,
                minLength: {
                  value: 3,
                  message: t('tagLengthError')
                },
                maxLength: {
                  value: 50,
                  message: t('tagLengthError')
                }
              }}
            />
          </Grid>
          <Grid item xs={12}>
            <FormInput fullWidth name="url" label={t('url')} focused={!!url} data-testid="NexusAddAppExtension-url" />
          </Grid>
          <Grid item xs={12}>
            <FormLabel sx={{ pl: 2 }}>
              <strong>{t('openLinkIn')}</strong>
            </FormLabel>
            <RadioGroup name="windowType" sx={{ pt: 2 }} value={windowType} onChange={handleOpenInChange}>
              <Box sx={{ border: '1px dotted', margin: '5px', padding: '5px', display: 'flex', alignItems: 'center' }}>
                <Box sx={{ display: 'flex', flexDirection: 'column' }}>
                  <strong>{t('systemBrowser')}</strong>
                  <Typography fontSize={'small'}>{t('systemBrowserDescription')}</Typography>
                </Box>
                <FormControlLabel value={openInType.WebBrowser} control={<Radio />} label={''} />
              </Box>
              <Box sx={{ border: '1px dotted', margin: '5px', padding: '5px', display: 'flex', alignItems: 'center' }}>
                <Box sx={{ display: 'flex', flexDirection: 'column' }}>
                  <strong>{t('floatingWindow')}</strong>
                  <Typography fontSize={'small'}>{t('floatingWindowDescription')}</Typography>
                </Box>
                <FormControlLabel value={openInType.CefBrowser} control={<Radio />} label={''} />
              </Box>
            </RadioGroup>
          </Grid>
        </Grid>
        <DialogActions sx={{ display: 'flex', justifyContent: 'flex-end', flexWrap: 'wrap' }}>
          <Button onClick={onCancel} color="primary" sx={{ m: 0 }} data-testid="NexusDialog-action-Cancel">
            {t('cancel')}
          </Button>
          <Button onClick={handleSubmit(onSubmit)} disabled={!isValid} data-testid="NexusDialog-action-Add">
            {t('add')}
          </Button>
        </DialogActions>
      </FormProvider>
    </Dialog>
  );
};
