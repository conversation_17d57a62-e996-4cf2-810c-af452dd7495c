import React, { useEffect, useRef, useState } from 'react';
import { Box } from '@mui/material';
import { ConnectedAppSwitcher } from '@nexusui/connected-components';

export default function AppSwitcher() {
  const [anchorEl, setAnchorEl] = useState<HTMLElement | null>(null);
  const boxRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (boxRef.current) {
      setAnchorEl(boxRef.current);
    }
  }, []);

  return (
    <Box ref={boxRef}>
      <ConnectedAppSwitcher
        sx={{
          '& .MuiPopover-paper': {
            top: '0px !important',
            left: '0px !important',
            position: 'absolute !important',
            borderRadius: '0px !important',
            '@media (max-width: 599px)': {
              right: '0px !important',
              bottom: '0px !important',
              maxWidth: '100% !important',
              maxHeight: '100% !important'
            }
          }
        }}
        open={Boolean(anchorEl)}
        anchorEl={anchorEl}
        data-testid="appSwitcher"
      />
    </Box>
  );
}
