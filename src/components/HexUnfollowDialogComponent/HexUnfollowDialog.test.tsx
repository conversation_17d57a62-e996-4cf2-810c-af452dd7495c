import React, { fireEvent, render, screen } from '@testing-library/react';
import { describe, it, expect, vi } from 'vitest';
import { HexUnfollowDialog, IUnfollowDialogProps } from './HexUnfollowDialog';
vi.mock('react-i18next', () => ({
  // this mock makes sure any components using the translate hook can use it without a warning being shown
  useTranslation: () => {
    return {
      t: (str: any) => str,
      i18n: {
        changeLanguage: () => new Promise<void>((resolve) => resolve())
      }
    };
  }
}));
describe('HexUnfollowDialogComponent', () => {
  const handleClose = vi.fn();
  const handleUnfollow = vi.fn();
  const mockProject = {
    id: '5309f47a-6141-4b24-b78a-3a960e198acd',
    organizationId: 'auth0|62ac3fb8fc1881001d521793',
    project: 'MACKENZIE_TEST',
    description: 'Design and Additive Manufacturing',
    schemaTypeId: '5f9f1b9a-8c9c-4b9f-9c1a-3a960e198acd'
  };
  const props: IUnfollowDialogProps = { project: mockProject, showUnfollowDialog: true, handleClose: handleClose, handleUnfollow: handleUnfollow, isProjectOrSDC: 'SDC' };
  it('should render the component', async () => {
    const component = render(<HexUnfollowDialog {...props} />);
    expect(component).toBeDefined();
  });

  it('should render a title', async () => {
    render(<HexUnfollowDialog {...props} />);
    const title = screen.getByTestId('unfollow-dialog-title').innerHTML;
    expect(title).toBe('unfollowDocument?');
  });

  it('should have project in description', async () => {
    render(<HexUnfollowDialog {...props} />);
    const description = screen.getByTestId('unfollow-dialog-description').innerHTML;
    expect(description).toContain('MACKENZIE_TEST');
  });

  it('should fire close event', async () => {
    render(<HexUnfollowDialog {...props} />);
    fireEvent.click(await screen.findByTestId('cancel-unfollow-project'));
    expect(handleClose).toBeCalledTimes(1);
  });

  it('should fire handleUnfollow ', async () => {
    render(<HexUnfollowDialog {...props} />);
    fireEvent.click(await screen.findByTestId('confirm-unfollow-project'));
    expect(handleUnfollow).toBeCalledTimes(1);
  });
});
