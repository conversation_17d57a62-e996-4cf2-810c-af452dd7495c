import React from 'react';
import { <PERSON><PERSON>, <PERSON>alog, <PERSON>alogActions, DialogTitle, DialogContent, DialogContentText, Divider } from '@mui/material';
import { IDocument } from '@nexusplatform/core-react-components';
import { useTranslation } from 'react-i18next';
import { IFolderList } from '../../models/Folders';
export interface IUnfollowDialogProps {
  showUnfollowDialog: boolean;
  handleClose: () => void;
  handleUnfollow: (isProjectOrSDC: string) => void;
  project: IDocument | IFolderList | null | undefined;
  isProjectOrSDC: string;
}
export const HexUnfollowDialog = ({ showUnfollowDialog, project, handleClose, handleUnfollow, isProjectOrSDC = 'SDC' }: IUnfollowDialogProps) => {
  const { t } = useTranslation();

  return (
    <Dialog
      data-testid="unfollow-project-dialog"
      open={showUnfollowDialog}
      onClose={handleClose}
      aria-labelledby="unfollow-dialog-title"
      aria-describedby="unfollow-dialog-description"
    >
      <DialogTitle id="unfollow-dialog-title" data-testid="unfollow-dialog-title">
        {isProjectOrSDC === 'SDC' ? t('unfollowDocument') : isProjectOrSDC === 'Project' && t('unfollowProject')}?
      </DialogTitle>
      <DialogContent>
        <DialogContentText id="unfollow-dialog-description" data-testid="unfollow-dialog-description">
          {t('sureUnfollow')} {isProjectOrSDC === 'SDC' ? project?.project : isProjectOrSDC === 'Project' && project?.name}?<br />
          {t('reAdd')}
        </DialogContentText>
        <Divider sx={{ mt: 6 }} />
      </DialogContent>
      <DialogActions className="hex-dialog-actions" sx={{ display: 'flex', justifyContent: 'space-between', padding: '0px 24px 24px', flexWrap: 'wrap' }}>
        <Button onClick={handleClose} color="primary" sx={{ m: 0 }} data-testid="cancel-unfollow-project">
          {t('cancel')}
        </Button>
        <Button onClick={() => handleUnfollow(isProjectOrSDC)} sx={{ m: 0, color: 'error.dark' }} data-testid="confirm-unfollow-project">
          {isProjectOrSDC === 'SDC' ? t('unfollowDocument') : isProjectOrSDC === 'Project' && t('unfollowProject')}
        </Button>
      </DialogActions>
    </Dialog>
  );
};
