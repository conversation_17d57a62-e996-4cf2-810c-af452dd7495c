/* eslint-disable camelcase */
import { DataBinder } from '@fluid-experimental/property-binder';
import { v4 } from 'uuid';

import { HexFluidController } from '@nexusplatform/core';
import { hxgn } from '@nexusplatform/sdc-schema';
import { ArrayProperty, ContainerProperty, MapProperty } from '@fluid-experimental/property-properties';

const DEFAULT_ROOT_PATH = '/sdc.visualization.comments';
export default class CommentsController extends HexFluidController {
  private _commentsPath = DEFAULT_ROOT_PATH;
  constructor(binder: DataBinder, path: string = DEFAULT_ROOT_PATH) {
    super(binder, path);
    if (path !== undefined && path !== null) this._commentsPath = path;
  }

  bindCommentsTo(setComments: any) {
    this.addListener('', ['modify', 'insert', 'remove'], setComments);
  }

  private get3DWhiteBoardComments = () => {
    return this.getRoot()?.resolvePath(this._commentsPath) as MapProperty;
  };

  getComments = (): [string, hxgn.prd.IValueComment][] | undefined => {
    try {
      const values = this.get3DWhiteBoardComments();
      return values ? Object.entries(values.getValues()) : undefined;
    } catch (err) {
      console.error(err);
    }
  };

  private createCommentProperty = (message: string, userId: string): hxgn.prd.Comment => {
    const comment: hxgn.prd.IValueComment = {
      created: { timestampMs: Math.floor(Date.now() / 1000) },
      modified: { timestampMs: Math.floor(Date.now() / 1000) },
      content: message,
      creator: userId
    };
    return hxgn.prd.Comment.create(comment);
  };

  readComment = (commentId: string) => {
    try {
      const comment = this.get3DWhiteBoardComments().get(commentId);
      if (comment) {
        return (comment as ContainerProperty).get('read') as ArrayProperty;
      }
    } catch (err) {
      console.error(err);
    }
  };

  addComment = (commentId: string, message: string, userId: string): hxgn.prd.Comment_1_0_0 | undefined => {
    try {
      const commentDds = this.createCommentProperty(message, userId);
      this.get3DWhiteBoardComments().insert(commentId, commentDds);
      this._binder._propertyTree?.commit();
      return commentDds;
    } catch (err) {
      console.error(err);
    }
  };

  addReply = (message: string, userId: string, commentId: string) => {
    try {
      const comment = this.get3DWhiteBoardComments().get(commentId);
      if (comment) {
        const commentDds = this.createCommentProperty(message, userId);
        const id = v4();
        const replies = (comment as ContainerProperty).get('replies') as MapProperty;
        replies.insert(id, commentDds);
        this._binder._propertyTree?.commit();
      }
    } catch (err) {
      console.error(err);
    }
  };

  deleteCommentOrReply = (commentId: string, replyId?: string) => {
    try {
      if (replyId) {
        const comment = this.get3DWhiteBoardComments().get(commentId);
        if (comment) {
          const replies = (comment as ContainerProperty).get('replies') as MapProperty;
          replies.remove(replyId);
        }
      } else {
        const comments = this.get3DWhiteBoardComments();
        comments?.remove(commentId);
      }
      this._binder._propertyTree?.commit();
    } catch (err) {
      console.error(err);
    }
  };

  resolveComment = (parentCommentId: string, replyId?: string, resolve?: boolean) => {
    try {
      if (replyId) {
        const comment = this.get3DWhiteBoardComments().get(parentCommentId);
        if (comment) {
          const replies = (comment as ContainerProperty).get('replies') as MapProperty;
          replies.setValues({ [replyId]: { resolved: true } });
        }
      } else {
        const comments = this.get3DWhiteBoardComments();
        comments?.setValues({ [parentCommentId]: { resolved: resolve } });
      }
      this._binder._propertyTree?.commit();
    } catch (err) {
      console.error(err);
    }
  };

  editMessage = (message: string, commentId: string, replyId?: string) => {
    try {
      if (replyId) {
        const comment = this.get3DWhiteBoardComments().get(commentId);
        if (comment) {
          const replies = (comment as ContainerProperty).get('replies') as MapProperty;
          replies.setValues({ [replyId]: { content: message } });
        }
      } else {
        const comments = this.get3DWhiteBoardComments();
        comments?.setValues({ [commentId]: { content: message } });
      }
      this._binder._propertyTree?.commit();
    } catch (err) {
      console.error(err);
    }
  };

  setCommentAsSeen = (commentId: string, userId: string) => {
    try {
      const comment = this.get3DWhiteBoardComments().get(commentId);
      if (comment) {
        const read = (comment as ContainerProperty).get('read') as ArrayProperty;
        read.push({ timestamp: { timestampMs: Math.floor(Date.now() / 1000) }, user: userId });
      }
      this._binder._propertyTree?.commit();
    } catch (err) {
      console.error(err);
    }
  };
}
