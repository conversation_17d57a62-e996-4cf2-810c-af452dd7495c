import { BaseProperty, MapProperty, NodeProperty, PropertyFactory } from '@fluid-experimental/property-properties';

const GET_REF = { referenceResolutionMode: BaseProperty.REFERENCE_RESOLUTION.NEVER };

/**
 * Copied from sdc-schema to convert a DDS object to JSON.
 * Should be part of the sdc-schema package
 */

// eslint-disable-next-line require-jsdoc
export function ddsToJson(node: any, defaultTypeId = ''): any {
  if (node.getTypeid === undefined) {
    return node;
  }

  if (node.getAbsolutePath().startsWith('/$')) {
    return undefined;
  }

  const typeid = node.getTypeid();
  try {
    if (node._context === 'array' || node._context === 'set') {
      if (typeid.startsWith('Reference')) {
        return node.getValues();
      }

      const ddsArray: any[] = [];
      for (let i = 0; i < node.getLength(); i++) {
        const element = node.get(i, GET_REF);
        const dds = ddsToJson(element, typeid);
        ddsArray.push(dds);
      }

      return ddsArray;
    } else if (node._context === 'map') {
      if (typeid.startsWith('Reference')) {
        return node.getValues();
      }

      return (Object as any).fromEntries((node as MapProperty).getIds().map((id) => [id, ddsToJson(node.get(id, GET_REF), typeid)]));
    } else if (typeid.startsWith('Reference')) {
      return node.getValue();
    } else if (node.isPrimitiveType()) {
      if (!node.getParent()?.isDynamic()) {
        return node.getValue();
      }
      return {
        $typeid: typeid,
        value: node.getValue()
      };
    } else if (typeid.includes(':') || PropertyFactory.inheritsFrom(typeid, 'NodeProperty')) {
      return nodeToJson(node, defaultTypeId || typeid);
    } else if (PropertyFactory.inheritsFrom(typeid, 'ArrayProperty')) {
      console.warn('ArrayProperty is deprecated, use NodeProperty instead');
    } else {
      throw new Error('unknown' + typeid);
    }
  } catch (e) {
    console.error('err', e);
  }
}

const DEFAULT_PROPS = new Map<string, any>();

// eslint-disable-next-line require-jsdoc
function nodeToJson(node: NodeProperty, defaultTypeid = '', removeDefaults = true): any {
  const json = {} as any;
  const typeid = node.getTypeid();
  if (typeid !== defaultTypeid || node.getParent()?.isDynamic()) {
    json.$typeid = node.getTypeid();
  }

  let defaultProperty = {} as any;
  if (node.getTypeid().includes(':') && removeDefaults) {
    defaultProperty = DEFAULT_PROPS.get(typeid);
    if (defaultProperty === undefined) {
      const dds = PropertyFactory.create<any>(typeid);
      defaultProperty = nodeToJson(dds, defaultTypeid, false);
      DEFAULT_PROPS.set(typeid, defaultProperty);
    }
  }

  node.getIds().forEach((id) => {
    const value = ddsToJson(node.get(id, GET_REF));

    if (value === undefined) {
      return;
    }

    if (removeDefaults && JSON.stringify(value) === JSON.stringify(defaultProperty[id])) {
      return;
    }

    json[id] = value;
  });

  const context = (node as any)._context;
  if (context && context !== 'single') {
    json.$context = context;
  }

  const isConstant = (node as any)._isConstant;
  if (isConstant) {
    json.$isConstant = isConstant;
  }

  return json;
}
