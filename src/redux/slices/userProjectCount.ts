import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface ProjectCountState {
  projectCount: number;
}

const initialState: ProjectCountState = {
  projectCount: 1
};

const projectCountSlice = createSlice({
  name: 'projectCount',
  initialState,
  reducers: {
    setProjectCount: (state, action: PayloadAction<number>) => {
      state.projectCount = action.payload;
    }
  }
});

const { actions, reducer } = projectCountSlice;
export const { setProjectCount } = actions;
export default reducer;
