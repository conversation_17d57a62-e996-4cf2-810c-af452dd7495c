import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';
import { resources } from './locales/resources';
const isDev = import.meta.env.NODE_ENV === 'development';

i18n
  // detect user language
  // learn more: https://github.com/i18next/i18next-browser-languageDetector
  .use(LanguageDetector)
  // pass the i18n instance to react-i18next.
  .use(initReactI18next)
  // init i18next
  // for all options read: https://www.i18next.com/overview/configuration-options
  .init({
    fallbackLng: 'en-US',
    debug: isDev,
    resources: resources,
    interpolation: {
      escapeValue: false // not needed for react as it escapes by default
    },
    // The configuration below will automatically detect the missing keys in the project
    // during development. If you don't want it, you can remove it
    saveMissing: isDev,
    saveMissingTo: 'all',
    // save missing keys handle, return the missing keys
    missingKeyHandler: (lng, ns, k, val, isUpdate, opts) => {
      console.log(val);
    }
  });
