{"accept": "Accept", "actionUndone": ". This action is permanent and cannot be undone.", "adamsCar": "<PERSON>", "amWorkflow": "AM Workflow", "compute": "Compute", "adamsCarSubHeading": "Run Events on Nexus Cloud", "additiveManufacturing": "Additive Manufacturing", "additiveProjectType": "Additive Manufacturing", "all": "All", "documents": "Documents", "allDocuments": "All Documents", "allProjectTypes": "All document types", "alwaysOn": "Always on", "allDataBanks": "All databanks", "apply": "Apply", "areYouSure": "Are you sure?", "assemblyTree": "Assembly Tree", "asterix": "Materials Enrich", "autoSave": "Auto-save changes", "baseplate": "Baseplate", "buildCosts": "Build costs", "buildSpaceDimension": "Build space dimension", "buildParameters": "Build parameters", "buildResults": "Build results", "buildStrategy": "Build strategy", "buildTime": "Build time", "by": "by", "save": "Save", "cancel": "Cancel", "changeCover": "Change Cover", "close": "Close", "Color": "Colour", "comments": "Comments", "commentUnresolved": "Comment marked as unresolved", "commentResolved": "Comment marked as resolved", "compensatedPart": "Compensated part", "connectionRestored": "Connection Restored", "confirmDelete": "Please confirm that you wish to delete", "confirmUnfollow": "Please confirm that you wish to unfollow ", "CopyLink": "Copy Link", "Created": "Created", "createNew": "Create New", "materialAccessRestrictedTitle": "Restricted access", "materialAccessRestrictedMsg": "You do not have access to this databank. Please contact your administrator.", "createNewProject": "Create New Document", "createNewProjectDisabled": "Create new documents in Nexus Apps and Solutions", "createProject": "Create Document", "creatingProject": "Creating Document.....", "productDigitalTwin": "Product Digital Twin", "ProjectLoading": "Document Loading.....", "ProjectLoadingDescription": "Loading your Nexus Documents", "data": "Data", "debugger": "debugger", "defaultExpandedErrorMessage": "Unexpected error encountered. No additional information is available.", "delete": "Delete", "deleteComment": "Are you sure you want to delete this comment ? This will permanently delete this comment and all of its replies.", "deleteReply": "Are you sure you want to delete this reply ? This will permanently delete this reply.", "deleteProject": "Delete Document", "deleteSnapshot": "Are you sure you want to delete this Snapshot?", "deletedSuccessfully": "deleted", "density": "Density", "designAndAMMainHeading": "Design &amp; Additive Manufacturing", "designAndAMSubHeading": "Share Geometries, Materials, Support", "designAndAdditiveManufacturing": "Design and Additive Manufacturing", "dfam": "DfAM / Design for Additive Manufacturing", "digitalProduct": "Digital Product", "disconnected": "Disconnected", "dismiss": "<PERSON><PERSON><PERSON>", "doYouWishToDelete": "Do you wish to delete?", "doYouWishToUnfollow": "Do you wish to unfollow?", "dragFilesHere": "drag files here", "editor": "Editor", "viewer": "Viewer", "owner": "Owner", "emailCommaSeparated": "Email comma separated", "emailAlreadyAdded": "has already been added.", "emailIsNotValidEmail": "is not a valid email address.", "enterNewProjectName": "Enter a new document name", "errorCallingResponse": "There was an error calling the response data", "errorGettingMaterialDetails": "There was an error getting material details", "errorRetrievingProjects": "There was an error retrieving your projects", "errorSavingMaterial": "There was an error saving your material; please try again", "errorWhileSaving": "Unable to create your document", "errorUpdatingDocument": "Unable to rename your document", "errorUpdatingProject": "Unable to rename your project", "errorDeletingProject": "Unable to delete", "errorUpdatingAuthorization": "You don't have permission to perform this action; contact the owner of the document", "errorDeletingAuthorization": "You don't have permission to perform this action, contact the project owner", "failedToCreateDemoProject": "Unable to create your demo document", "fileAcceptanceMessage": "Accepted file types: jpg, png, gif files. Maximum file size 2 MB.", "getMaterials": "Getting materials for your document", "getMaterialDetails": "Getting material details for your selection", "icebreakerDemo": "Icebreaker De<PERSON>", "informationUpdatedSuccessfully": "Information updated successfully", "invite": "Invite", "inviteMessage": "When shared with others, those invited will be able to work on the same Document at the same time.", "inviteMessageProject": "When shared with others, those invited will be able to work on the same project at the same time.", "unresolvedInviteMessage": "is/are not registered with Nexus. An invitation email to register with Nexus has been sent. Once account is created they will need to be re-invited to gain access to this document.", "unresolvedInviteMessageProject": "is/are not registered with Nexus. An invitation email to register with Nexus has been sent. Once an account is created a re-invitation is necessary to gain access to this project.", "errorShareProject": "Unable to share your project", "errorShareDocument": "Unable to share your document", "Portal": "Portal", "SavedViews": "Saved Views", "NoData": "No Data", "justNowLabel": "Just now", "laserPower": "Laser power", "laserSpeed": "Laser speed", "layerThickness": "Layer thickness", "LinkCopiedToClipboard": "People with existing access can use the link copied", "loading": "Loading", "loading3dModels": "Loading 3D Models", "loadingProjectData": "Loading Document Data", "loadingProjectDataDescription": "Please wait while we set up your document details", "loadingYourExperience": "Loading Your Experience", "loadingYourProjects": "Loading your projects...", "logout": "Log out", "loadMore": "Load More ...", "mass": "Mass", "manageOrganizations": "Manage Organisations", "materialCenter": "Material Centre", "machine": "Machine", "manufacturer": "Manufacturer", "material": "Material", "materialGroup": "Material Group", "materialEnrichProjectType": "Material Enrich", "materialSelectorTitle": "Back to document", "materialSelectorTitleUpdateMaterial": "Update Material", "materialSelectorInfoTitle": " Material Connect &amp; Databanks", "materialSelectorInfoContent": "Materials Connect provides the materials utilised in Nexus Core. A selection of standard materials is housed in the", "materialSelectorInfoContentHighlight": "Standard Materials", "materialSelectorInfoContentDataBank": "databank", "materialSelectorInfoCloseBTN": "Close", "materialServiceIsOffline": "Material connect service is unavailable", "metals": "Metals", "metroProjectType": "Metrology Reports", "metroReports": "Metrology Reports", "model": "Model", "modifiedLabel": "modified", "myAccount": "My Account", "plastic": "Plastic", "plasticProjectType": "Plastic", "processing": "Processing ...", "projectUpdating": "Please wait while we update the document", "metrologyMainHeading": "Metrology", "metrologySubHeading": "Post-Production, Measure and Analyse", "name": "Name", "newProject": "New Document", "nexusPlatform": "Nexus Platform", "nexusByHexagon": "Nexus By Hexagon", "noProjectsTitle": "No documents to show yet", "noProjectsDescription": "Go to your Nexus projects page to create your first document.", "noResultsTitle": "No results found for", "noResultsDescription": "Please adjust your spelling or try a different search.", "notSharedWithAnyone": "You have not shared this with anyone", "no3DWhiteboard": "No 3D Geometry to display", "no3DWhiteboardDescription": "3D models can be added to your document from within Hexagon software.", "noSavedViewsTitle": "No saved views to show yet", "noSavedViewsSubtitle": "Saved views allows you and your team to capture camera angles of your model so you can review areas of interest at a later time.\n\nClick on the camera icon in the navigation bar to start capturing views.", "noSearchResultFor": "No Search Result for", "{{number}}DocumentTypes": "{{number}} Document types", "open3DWhiteboard": "Open 3D Whiteboard", "open3DWhiteboardComingSoon": "Open 3D Whiteboard (Coming Soon)", "or": "OR", "owned": "Owned", "part": "Part", "plastics": "Plastics", "poissonsRatio": "<PERSON><PERSON>on's ratio", "projectDetails": "Document Details", "projectName": "Document Name", "projectNameIsRequired": "DocumentName is required", "projectType": "Document Type", "releaseCursorToDropFiles": "Release cursor to drop files", "materialSelectorNoPermission": "You do not have permission to edit this container", "readMore": "Read more", "readLess": "Read less", "refresh": "Refresh", "reload": "Reload", "reloadPage": "Reload page", "rename": "<PERSON><PERSON>", "renameProject": "Rename Document", "renamedSuccessfully": "<PERSON><PERSON> successfully", "replies": "replies", "requestAccess": "Request access", "savedSnapshots": "Saved <PERSON><PERSON><PERSON>s", "savedSuccessfully": "document successfully created", "searchMaterialsByBeyword": "Search materials by keyword...", "searchProject": "Search by keyword...", "searchProjectByKeyword": "Search by keyword...", "selectProjectType": "Select Document Type", "settingUpWhiteBoard": "Setting up 3D Whiteboard", "settings": "Document Settings", "share": "Share", "shareProject": "Share Document", "shared": "Shared", "sharedAccessFilter": "Shared Access Filter", "sharedWith": "Shared with", "sharingSettings": "Sharing Settings", "shouldNotBeBlank": "Should not be blank", "showMore": "Show more", "showLess": "Show less", "showDetails": "Show details", "hide": "<PERSON>de", "smartAssemblyProjectType": "Smart Assembly", "support": "Support", "supportedSurface": "Supported surface", "supportType": "Support type", "supportVolume": "Support volume", "takeSnapshot": "Take Snapshot", "teamsWhiteboard": {"addCommentsNote": "Add comments to models to continue your conversations after meeting", "createAccount": "Create Account", "getStartedNote": "Get started with 3D Whiteboard in Microsoft 365", "login": "Log in", "markupNote": "Mark up and annotate on 3D models", "nowSharing": "Now sharing", "shareToMeeting": "Share to meeting", "sharingModel": "{{USER_NAME}} is sharing a model in the 3D Whiteboard", "signInToNexus": "Sign in to Nexus to join {{USER_NAME}} and your teammates in reviewing your project in the 3D Whiteboard.", "searchForProjects": "Search for projects", "viewAndDiscussNote": "View and discuss 3D models in a live setting", "wbLabel": "Nexus 3D Whiteboard", "openNexus": "Open Nexus Projects", "authHeader": "Design together in the 3D Whiteboard", "authBodyText": "Collaborate, explore, and capture action items in your design reviews right within Teams."}, "tensionStrength": "Tension strength", "thereWasAnErrorInDeleting": "Unable to delete", "threeDWhiteBoardMainHeading": "3D Whiteboard", "threeDWhiteBoardSubHeading": "Render Sharing, Collaboration, Model Layers", "tryAgainBtn": "Try Again", "tryDemoBtn": "Try a Demo", "updateAvailable": "Update available", "updating": "Updating", "unableToAcquireDownloadStream": "Unable to acquire download stream", "unableToConnectToNexus": "Unable to connect to Nexus", "undo": "Undo", "unfollow": "Unfollow", "unfollowedSuccessfully": "Successfully unfollowed", "unfollowedError": "Unable to unfollow", "uploadFiles": "Upload files", "uploadedSuccessfully": "Cover image uploaded successfully", "volume": "Volume", "WhiteboardLoading": "Whiteboard Loading.....", "youngsModulus": "<PERSON>'s modulus", "yourProjects": "Your Documents", "ProjectNoAccess": "You don’t have access to this content.", "ProjectNoAccessDescription": "You don’t currently have permission to view this content. Please ask the file owner to add you.", "OrgManagement": {"fetchFRSFailed": "Fluid relay services fetching failed", "fetchFailed": "Fetching all organisations failed", "createSuccess": "Organisation created successfully", "createFailed": "Organisation creation failed", "updateSuccess": "Organisation updated successfully", "updateFailed": "Organisation update failed", "activatedSuccess": "Organisation activated successfully", "activatedFailed": "Organisation activation failed", "deactivatedSuccess": "Organisation inactivated successfully", "deactivatedFailed": "Organisation inactivation failed", "removeSuccess": "Organisation removed successfully", "removeFailed": "Organisation removal failed", "notFound": "No organisations found."}, "UserManagement": {"fetchOrgFailed": "Organisation fetching failed", "fetchFailed": "User list fetching failed", "inviteSuccess": "{{0}} invitations successfully sent", "inviteFailedEmails": "Sending the following invitation emails failed: {{0}}", "inviteFailed": "Sending invitation failed.", "inviteResendSuccess": "Invitation successfully resent", "inviteResendFailed": "Resending invitation failed", "updateSuccess": "User updated successfully", "updateFailed": "User update failed", "deactivateSuccess": "User successfully inactivated", "deactivateFailed": "User inactivation failed", "reactivateSuccess": "User successfully activated", "reactivateFailed": "User activation failed", "cancelInviteSuccess": "Invitation successfully cancelled", "cancelInviteFailed": "Invitation cancelling failed", "noUsersFound": "No users found.", "noUsersFoundDescription": "There are no users associated with this organisation.", "fetchRolesFailed": "Role fetching failed.", "genericDomainNotSupport": "Invitations to generic email domains are not supported."}, "UserDetail": {"fetchFailed": "User activity fetching failed"}, "deleteDocument": "Delete Document", "sureDelete": "Are you sure you want to delete?", "undoDelete": " You won’t be able to get it back.", "sureUnfollow": "Are you sure you want to unfollow?", "reAdd": "You can always be re-added at a later date.", "unfollowDocument": "Unfollow Document", "renameDocument": "Rename Document", "smartassembly": "Smart Assembly", "smartassemblySubHeading": "Generate mesh, run simulation on assemblies", "noDocuments": "No documents to show yet.", "noDocumentsSubText": "Create a Nexus document, a space for seamless data sharing and collaboration across teams, to get your next job done. Documents are cloud-based and rely on the Nexus", "smartdatacontracts": "Smart Data Contracts,", "accessibleTest": "an architecture to securely exchange information.", "availableNexusText": "Available Nexus Apps and solutions", "learnMore": "Learn more", "comingSoon": "Coming soon", "AdditiveTileText": "Additive Manufacturing Solution", "MetrologyTileText": "Metrology Reporting – Shared Reports", "PlasticTileText": "Plastic Moulding", "plasticSubHeading": "Simulate moulds with different properties and conditions", "computeSubHeading": "Run Events on Nexus Cloud", "SheetMetalTileText": "Sheet Metal Quoting", "AdamsCarTileText": "Cloud Execution for Adams Car", "schemaMismatchErrorTitle": "Incompatible Document", "schemaMisMarchErrorMsg": "The document you are trying to access is not compatible with your application.", "saveView": "Save View?", "defaultErrorTitle": "Something went wrong", "defaultErrorMessage": "We seem to have encountered a problem; try refreshing the page.", "viewCaptured": "View captured", "sort": "Sort", "sortByDate": "Sort by date", "sortByAuthor": "Sort By author", "stop": "Stop", "continue": "Continue", "followers": "{{count}} followers", "following": "Following {{name}}", "copyUrl": "Copy URL", "urlCopiedMessage": "URL copied to clipboard", "documentUrl": "Document URL", "blacklistedMessage": "Unable to invite user(s), one or more invited user's email ID is in public domain ", "resendInviteMsg": "In order for us to provide the best service, please use work email.", "termsAndConditions": "Terms and Conditions", "footerText": "© 2023 Hexagon AB and/or its subsidiaries", "changeLanguage": "Change language", "viewAllAPPs": "View all apps", "Share Access Filter": "Share Access Filter", "Owned by anyone": "Owned by anyone", "Owned by me": "Owned by me", "Shared with me": "Shared with me", "Sort by created date": "Sort by created date", "Sort by A to Z": "Sort by A to Z", "Sort by last opened by me": "Sort by last opened by me", "changeTheme": "Change theme", "toggleTooltip": "Try the new Projects View", "projects": "Projects", "drafts": "Drafts", "noProjects": "No projects created yet.", "noDocumentsText": "You don't have any Documents yet", "noDocumentsBody": "Documents are sharable cloud-based spaces that can be created in Nexus Apps and connected to Nexus Solutions.", "createSampleDocument": "Create Sample Document", "exploreNexusSolutions": "Explore our Nexus Solutions...", "firstProject": "Create your first Project", "projectText": "Projects allow you to group related Documents together in a single space and share them with other users.", "createProjects": "Create Project", "back": "Back", "LoadingProjectsList": "Loading your Nexus Projects", "create": "Create", "projectNameField": "Project Name", "CreatingProjects": "Creating Projects...", "projectSuccess": "project successfully created.", "documentSuccess": "document successfully created.", "somethingWentWrongMsg": "Unable to create your project", "chooseProject": "Choose Project", "renameFolderProject": "Rename Project", "enterNewFolderProjectName": "Enter new project name", "tryTheNewLook": "Try the new look", "projectDescription": "An overview of every project either owned by you, or that you’ve been shared into.", "documentDescription": "Explore all documents you have access to, whether owned or shared – regardless of the project.", "draftDescription": "Your private experimentation space – documents can be shared individually or moved into a public project.", "deleteDocumentHeader": "Delete Document?", "deleteDocumentBody": "All data in this document will be deleted. This action is permanent and cannot be undone.", "deleteProjectHeader": "Delete Project?", "deleteProjectBody": "All documents contained in this project will be deleted, this action is permanent and cannot be undone.", "deleteProjectBtn": "Delete Project", "unfollowProject": "Unfollow Project", "continueFollowMe": "Return to Follow Me mode", "disabledSnapshots": "Snapshots are disabled during Follow Me mode", "disabledAssemblyTree": "Assembly Tree is disabled during Follow Me mode", "moveDocumentTitle": "Move Document", "auditDataTitle": "Audit data", "moveDocumentName": "Document Name", "moveProject": "Project", "moveButton": "Move", "moveDocumentSuccess": "Moved", "moveDocumentTo": "to", "errorMoveDocument": "Unable to move your document", "copyProjectLink": "Project link copied to clipboard", "errorCopyProjectLink": "Unable to copy project link", "copyDocumentLink": "Document link copied to clipboard", "errorCopyDocumentLink": "Unable to copy document link", "followedSuccessfully": "You are now following", "followedError": "Unable to follow", "cannotUnfollow": "You cannot unfollow the project as you have a document owned under the project.", "cannotShare": "You cannot remove the project folder access as the collaborator has documents owned under the", "project": "project", "notifications": "Notifications", "inviteSharedProjectMessage": "has invited you to join the shared Nexus project", "inviteSharedDocumentMessage": "has invited you to join the shared Nexus document", "noNotificationMessage": "You do not have notification messages", "viewRawData": "View raw data", "operation": "Operation", "time": "Time stamp", "userId": "User Name / User ID", "manyMoreTileText": "Many more...", "noAccess": "No Access", "shareDocumentOrgLevel": "Document shared with", "access": "access", "errorShareDocumentOrgLevel": "Issue when sharing Document"}