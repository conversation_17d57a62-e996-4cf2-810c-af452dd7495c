{"accept": "Accept", "actionUndone": ". This action is permanent and cannot be undone.", "adamsCar": "<PERSON>", "amWorkflow": "AM Workflow", "compute": "Compute", "adamsCarSubHeading": "Run Events on Nexus Cloud", "additiveManufacturing": "Additive Manufacturing", "additiveProjectType": "Additive Manufacturing", "all": "All", "redirectionTitle": "Auto redirection", "redirectionInfo": "You will be redirected to {{targetApp}} app in {{timeout}} seconds. If redirection does not happen automatically, please click on the button below", "redirectionSubTitle": "This document is hosted outside side of Nexus platform. ", "redirectionNow": "Redirect now", "documents": "Documents", "allDocuments": "All Documents", "allProjectTypes": "All document types", "alwaysOn": "Always on", "allDataBanks": "All databanks", "apply": "Apply", "areYouSure": "Are you sure?", "assemblyTree": "Assembly Tree", "asterix": "Materials Enrich", "autoSave": "Auto-save changes", "baseplate": "Baseplate", "buildCosts": "Build costs", "buildSpaceDimension": "Build space dimension", "buildParameters": "Build parameters", "buildResults": "Build Results", "buildStrategy": "Build strategy", "buildTime": "Build time", "by": "by", "save": "Save", "cancel": "Cancel", "changeCover": "Change Cover", "close": "Close", "Color": "Color", "comments": "Comments", "commentUnresolved": "Comment marked as unresolved", "commentResolved": "Comment marked as resolved", "compensatedPart": "Compensated part", "connectionRestored": "Connection Restored", "confirmDelete": "Please confirm you wish to delete", "confirmUnfollow": "Please confirm you wish to unfollow ", "CopyLink": "Copy Link", "Created": "Created", "createNew": "Create New", "materialAccessRestrictedTitle": "Access is restricted", "materialAccessRestrictedMsg": "You do not have access to this databank. Please contact your administrator.", "createNewProject": "Create New Document", "createNewProjectDisabled": "Create new documents in Nexus Apps and Solutions", "createProject": "Create Document", "creatingProject": "Creating Document.....", "productDigitalTwin": "Product Digital Twin", "ProjectLoading": "Document Loading.....", "ProjectLoadingDescription": "Loading your Nexus Documents", "data": "Data", "debugger": "debugger", "defaultExpandedErrorMessage": "Unexpected error encountered. No additional information is available.", "delete": "Delete", "deleteComment": "Are you sure you want to Delete this comment ? This will permanently delete this  comment and all of it’s replies.", "deleteReply": "Are you sure you want to Delete this reply ? This will permanently delete this reply.", "deleteProject": "Delete Document", "deleteSnapshot": "Are you sure you want to delete this Snapshot", "deletedSuccessfully": "deleted", "density": "Density", "designAndAMMainHeading": "Design & Additive Manufacturing", "designAndAMSubHeading": "Share Geometries, Materials, Support", "designAndAdditiveManufacturing": "Design and Additive Manufacturing", "dfam": "DfAM / Design for Additive Manufacturing", "digitalProduct": "Digital Product", "disconnected": "Disconnected", "dismiss": "<PERSON><PERSON><PERSON>", "doYouWishToDelete": "Do you wish to delete", "doYouWishToUnfollow": "Do you wish to unfollow", "dragFilesHere": "drag files here", "editor": "Editor", "viewer": "Viewer", "owner": "Owner", "emailCommaSeparated": "Email comma separated", "emailAlreadyAdded": "has already been added.", "emailIsNotValidEmail": "is not a valid email address.", "enterNewProjectName": "Enter a new document name", "errorCallingResponse": "There was an error from calling the response data", "errorGettingMaterialDetails": "There was an error getting material details", "errorRetrievingProjects": "There was an error retrieving your projects", "errorSavingMaterial": "There was an error saving your material, please try again", "errorWhileSaving": "Unable to create your document", "errorUpdatingDocument": "Unable to rename your document", "errorUpdatingProject": "Unable to rename your project", "errorDeletingProject": "Unable to delete", "errorUpdatingAuthorization": "You don't have permission to perform this action, contact the owner of the document", "errorDeletingAuthorization": "You don't have permission to perform this action, contact the owner of the Project", "failedToCreateDemoProject": "Unable to create your demo document", "fileAcceptanceMessage": "Accepted file types: jpg, png, gif files. Maximum file size 2 MB.", "getMaterials": "Getting materials for your document", "getMaterialDetails": "Getting material details for your selection", "icebreakerDemo": "Icebreaker De<PERSON>", "informationUpdatedSuccessfully": "Information updated successfully", "invite": "Invite", "inviteMessage": "When shared with others, those invited will be able to work on the same Document at the same time.", "inviteMessageProject": "When shared with others, those invited will be able to work on the same Project at the same time.", "unresolvedInviteMessage": "is/are not registered with Nexus. An invitation email to register with Nexus has been sent.", "unresolvedInviteMessageProject": "is/are not registered with Nexus. An invitation email to register with Nexus has been sent.", "errorShareProject": "Unable to share your project", "errorShareDocument": "Unable to share your document", "Portal": "Portal", "SavedViews": "Saved Views", "NoData": "No Data", "justNowLabel": "Just now", "laserPower": "Laser power", "laserSpeed": "Laser speed", "layerThickness": "Layer thickness", "LinkCopiedToClipboard": "People with existing access can use the link copied", "loading": "Loading", "loading3dModels": "Loading 3D Models", "loadingProjectData": "Loading Document Data", "loadingProjectDataDescription": "Please wait while we setup your document details", "loadingYourExperience": "Loading Your Experience", "loadingYourProjects": "Loading your projects...", "logout": "Logout", "loadMore": "Load More ...", "mass": "Mass", "manageOrganizations": "Manage Organizations", "materialCenter": "Material Center", "machine": "Machine", "manufacturer": "Manufacturer", "material": "Material", "materialGroup": "Material Group", "materialEnrichProjectType": "Material Enrich", "materialSelectorTitle": "Back to document", "materialSelectorTitleUpdateMaterial": "Update Material", "materialSelectorInfoTitle": " Material Connect & Databanks", "materialSelectorInfoContent": "Materials Connect provides the materials utilised in Nexus Core. A selection of standard Materials is housed in the", "materialSelectorInfoContentHighlight": "Standard Materials", "materialSelectorInfoContentDataBank": "databank", "materialSelectorInfoCloseBTN": "Close", "materialServiceIsOffline": "Material connect service is unavailable", "metals": "Metals", "metroProjectType": "Metrology Reports", "metroReports": "Metrology Reports", "model": "Model", "modifiedLabel": "modified", "myAccount": "My Account", "plastic": "Plastic", "plasticProjectType": "Plastic", "processing": "Processing ...", "projectUpdating": "Please wait while we update the document", "metrologyMainHeading": "Metrology", "metrologySubHeading": "Post-Production, Measure and Analyze", "name": "Name", "newProject": "New Document", "nexusPlatform": "Nexus Platform", "nexusByHexagon": "Nexus By Hexagon", "noProjectsTitle": "No documents to show yet", "noProjectsDescription": "Go to your Nexus projects page to create your first document.", "noResultsTitle": "No results found for", "noResultsDescription": "Please adjust your spelling or try a different search.", "notSharedWithAnyone": "You have not shared this with anyone", "no3DWhiteboard": "No 3D Geometry to display", "no3DWhiteboardDescription": "3D models can be added to your document from within Hexagon software.", "noSavedViewsTitle": "No saved views to show yet", "noSavedViewsSubtitle": "Saved views allows you and your team to capture camera angles of your model so you can review areas of interest at a later time.\n\nClick on the camera icon in the navigation bar to start capturing views.", "noSearchResultFor": "No Search Result for", "{{number}}DocumentTypes": "{{number}} Document types", "open3DWhiteboard": "Open 3D Whiteboard", "open3DWhiteboardComingSoon": "Open 3D Whiteboard (Coming Soon)", "or": "OR", "owned": "Owned", "part": "Part", "plastics": "Plastics", "poissonsRatio": "<PERSON><PERSON>on's ratio", "projectDetails": "Document Details", "projectName": "Document Name", "projectNameIsRequired": "DocumentName is required", "projectType": "Document Type", "releaseCursorToDropFiles": "Release cursor to drop files", "materialSelectorNoPermission": "You do not have permission to edit this container", "readMore": "Read more", "readLess": "Read less", "refresh": "Refresh", "reload": "Reload", "reloadPage": "Reload page", "rename": "<PERSON><PERSON>", "renameProject": "Rename Document", "renamedSuccessfully": "<PERSON><PERSON> successfully", "replies": "replies", "requestAccess": "Request access", "savedSnapshots": "Saved <PERSON><PERSON><PERSON>s", "savedSuccessfully": "document successfully created", "searchMaterialsByBeyword": "Search materials by keyword...", "searchProject": "Search by keyword...", "searchProjectByKeyword": "Search by keyword...", "selectProjectType": "Select Document Type", "settingUpWhiteBoard": "Setting up 3D Whiteboard", "settings": "Document Settings", "share": "Share", "shareProject": "Share Document", "shared": "Shared", "sharedAccessFilter": "Shared Access Filter", "sharedWith": "Shared with", "sharingSettings": "Sharing Settings", "shouldNotBeBlank": "Should not be blank", "showMore": "Show more", "showLess": "Show less", "showDetails": "Show Details", "hide": "<PERSON>de", "smartAssemblyProjectType": "Smart Assembly", "support": "Support", "supportedSurface": "Supported surface", "supportType": "Support type", "supportVolume": "Support volume", "takeSnapshot": "Take Snapshot", "teamsWhiteboard": {"addCommentsNote": "Add comments to models to continue your conversations after meeting", "createAccount": "Create Account", "getStartedNote": "Get started with 3D Whiteboard in Microsoft 365", "login": "Log in", "markupNote": "Mark up and annotate on 3D models", "nowSharing": "Now sharing", "shareToMeeting": "Share to meeting", "sharingModel": "{{USER_NAME}} is sharing a model in the 3D Whiteboard", "signInToNexus": "Sign in to Nexus to join {{USER_NAME}} and your teammates in reviewing your project in the 3D Whiteboard.", "searchForProjects": "Search for projects", "viewAndDiscussNote": "View and discuss 3D models in a live setting", "wbLabel": "Nexus 3D Whiteboard", "openNexus": "Open Nexus Projects", "authHeader": "Design together in the 3D Whiteboard", "authBodyText": "Collaborate, explore, and capture action items in your design reviews right within Teams."}, "tensionStrength": "Tension strength", "thereWasAnErrorInDeleting": "Unable to delete", "threeDWhiteBoardMainHeading": "3D Whiteboard", "threeDWhiteBoardSubHeading": "Render Sharing, Collaboration, Model Layers", "tryAgainBtn": "Try Again", "tryDemoBtn": "Try a Demo", "updateAvailable": "Update Available", "updating": "Updating", "unableToAcquireDownloadStream": "Unable to acquire download stream", "unableToConnectToNexus": "Unable to connect to Nexus", "undo": "Undo", "unfollow": "Unfollow", "unfollowedSuccessfully": "Successfully unfollowed", "unfollowedError": "Unable to unfollow", "uploadFiles": "Upload files", "uploadedSuccessfully": "Cover Image Uploaded Successfully", "volume": "Volume", "WhiteboardLoading": "Whiteboard Loading.....", "youngsModulus": "<PERSON>'s modulus", "yourProjects": "Your Documents", "ProjectNoAccess": "You don’t have access to this content.", "ProjectNoAccessDescription": "Currently you don’t have permission to view this content. Please ask the file owner to add you.", "OrgManagement": {"fetchFRSFailed": "Fetching fluid relay services failed", "fetchFailed": "Fetching all organizations failed", "createSuccess": "Organization created successfully", "createFailed": "Creating organization failed", "updateSuccess": "Organization updated successfully", "updateFailed": "Updating organization failed", "activatedSuccess": "Activated organization successfully", "activatedFailed": "Activitating organization failed", "deactivatedSuccess": "Inactivated organization successfully", "deactivatedFailed": "Inactivating organization failed", "removeSuccess": "Organization removed successfully", "removeFailed": "Removing organization failed", "notFound": "No organizations found."}, "UserManagement": {"fetchOrgFailed": "Fetching organization failed", "fetchFailed": "Fetching users list failed", "inviteSuccess": "{{0}} invites successfully sent", "inviteFailedEmails": "Sending the following invitation emails failed: {{0}}", "inviteFailed": "Sending invitation failed.", "inviteResendSuccess": "Resent invitation successfully", "inviteResendFailed": "Resending invitation failed", "updateSuccess": "Updated user successfully", "updateFailed": "Updating user failed", "deactivateSuccess": "Inactivated user successfully", "deactivateFailed": "Inactivating user failed", "reactivateSuccess": "Activated user successfully", "reactivateFailed": "Activating user failed", "cancelInviteSuccess": "Invitation cancelled successfully", "cancelInviteFailed": "Cancelling invitation failed", "noUsersFound": "No users found.", "noUsersFoundDescription": "There are no users associated with this organization.", "fetchRolesFailed": "Fetching roles failed.", "genericDomainNotSupport": "Invitation to generic email domains are not supported."}, "UserDetail": {"fetchFailed": "Fetching user activities failed"}, "deleteDocument": "Delete Document", "sureDelete": "Are you sure you want to delete", "undoDelete": " you won’t be able to get it back.", "sureUnfollow": "Are you sure you want to unfollow", "reAdd": "You can always be re-added at a later date.", "unfollowDocument": "Unfollow Document", "renameDocument": "Rename Document", "smartassembly": "Smart Assembly", "smartassemblySubHeading": "Generate mesh, Run Simulation on Assemblies", "noDocuments": "No Documents to show yet.", "noProjectsSubText": "To get started, create a project in one of our Nexus applications or explore our demo or trial to assess its capabilities.", "availableNexusText": "Available Nexus Apps and solutions", "learnMore": "Learn More", "comingSoon": "Coming soon", "AdditiveTileText": "Additive Manufacturing Solution", "MetrologyTileText": "Metrology Reporting -Shared Reports", "PlasticTileText": "Plastic Moulding", "plasticSubHeading": "Simulate moulds with different properties and conditions", "computeSubHeading": "Run Events on Nexus Cloud", "SheetMetalTileText": "Sheet Metal Quoting", "AdamsCarTileText": "Cloud Execution for Adams Car", "schemaMismatchErrorTitle": "Incompatible Document", "schemaMisMarchErrorMsg": "The document you are trying to access is not compatible with your application.", "saveView": "Save View?", "defaultErrorTitle": "Something went wrong", "defaultErrorMessage": "We seem to have encountered a problem, try refreshing the page.", "viewCaptured": "View captured", "sort": "Sort", "sortByDate": "Sort By date", "sortByAuthor": "Sort By author", "stop": "Stop", "continue": "Continue", "followers": "{{count}} followers", "following": "Following {{name}}", "copyUrl": "Copy URL", "urlCopiedMessage": "URL copied to clipboard", "documentUrl": "Document URL", "blacklistedMessage": "Unable to invite user(s), one or more invited user's email ID is in public domain ", "resendInviteMsg": "In order for us to provide the best service, please use work email.", "termsAndConditions": "Terms and Conditions", "footerText": "© 2023 Hexagon AB and/or its subsidiaries", "changeLanguage": "Change language", "viewAllAPPs": "View all apps", "Share Access Filter": "Share Access Filter", "Owned by anyone": "Owned by anyone", "Owned by me": "Owned by me", "Shared with me": "Shared with me", "Shared with organization": "Shared with organization", "Sort by created date": "Sort by created date", "Sort by A to Z": "Sort by A to Z", "Sort by last opened by me": "Sort by last opened by me", "changeTheme": "Change theme", "toggleTooltip": "Try the new Projects View", "projects": "Projects", "drafts": "Drafts", "noProjects": "No projects created yet.", "noDocumentsText": "You don't have any Documents yet", "noDocumentsBody": "Documents are sharable cloud-based spaces that can be created in Nexus Apps and connected to Nexus Solutions.", "createSampleDocument": "Create Sample Document", "exploreNexusSolutions": "Explore our Nexus Solutions...", "firstProject": "Create your first Project", "projectText": "Projects allow you to group related Documents together in a single space and share them with other users.", "createProjects": "Create Project", "back": "Back", "LoadingProjectsList": "Loading your Nexus Projects", "create": "Create", "projectNameField": "Project Name", "CreatingProjects": "Creating Projects...", "projectSuccess": "project successfully created.", "documentSuccess": "document successfully created.", "somethingWentWrongMsg": "Unable to create your project", "chooseProject": "Choose Project", "renameFolderProject": "Rename Project", "enterNewFolderProjectName": "Enter new project name", "tryTheNewLook": "Try the new look", "projectDescription": "An overview of every project either owned by you, or that you’ve been shared into.", "documentDescription": "Explore all documents you have access to, whether owned or shared - regardless of the project.", "draftDescription": "Your private experimentation space - documents can be shared individually or moved into a public project.", "deleteDocumentHeader": "Delete Document?", "deleteDocumentBody": "All data in this document will be deleted, this action is permanent and cannot be undone.", "deleteProjectHeader": "Delete Project?", "deleteProjectBody": "All documents contained in this project will be deleted, this action is permanent and cannot be undone.", "deleteProjectBtn": "Delete Project", "unfollowProject": "Unfollow Project", "continueFollowMe": "Return to Follow Me mode", "disabledSnapshots": "Snapshots are disabled during follow me mode", "disabledAssemblyTree": "Assembly Tree are disabled during follow me mode", "moveDocumentTitle": "Move Document", "auditDataTitle": "Audit Data", "moveDocumentName": "Document Name", "moveProject": "Project", "moveButton": "Move", "moveDocumentSuccess": "Moved", "moveDocumentTo": "to", "errorMoveDocument": "Unable to move your document", "copyProjectLink": "Project link copied to clipboard", "errorCopyProjectLink": "Unable to copy project link", "copyDocumentLink": "Document link copied to clipboard", "errorCopyDocumentLink": "Unable to copy document link", "followedSuccessfully": "You now follow", "followedError": "Unable to follow", "cannotUnfollow": "You cannot unfollow the project as you have document owned under the project.", "cannotShare": "You cannot remove the project folder access as the collaborator has documents owned under the", "project": "project", "notifications": "Notifications", "inviteSharedProjectMessage": "has invited you to join the shared Nexus project", "inviteSharedDocumentMessage": "has invited you to join the shared Nexus document", "noNotificationMessage": "You do not have notification messages", "viewRawData": "View Raw Data", "operation": "Operation", "time": "Time Stamp", "errorPage": {"refresh": "Refresh", "defaultErrorTitle": "Something went wrong", "subTitleText": "your administrator for more information", "subTitleTextWithoutContact": "Please contact your administrator for more information", "contactLink": "contact", "showDetails": "Show Details", "showLess": "<PERSON>de", "tryLater": "Something went wrong. Please try again later", "loadDocFailed": "Unable to load document", "docNotFound": "Document not found", "pageNotFound": "Page not found", "backHomeBtm": "Back to home page", "notFound": "The document was not found or you don't have access to it", "redirectionNotAvailable": "Redirection not available", "redirectionNoHostApplication": "This document type is not yet hosted by any target application"}, "userId": "User Name / User ID", "MetrologyAssetManagerTileText": "Metrology Asset Manager", "MaterialsEnrichTileText": "Materials Enrich", "ComputeTileText": "Compute", "MetrologyMentorTileText": "Metrology Mentor", "MaterialsConnectTileText": "Materials Connect", "MetrologyExecuteTileText": "Metrology Execute", "CuttingAndBendingLibrariesTileText": "Cutting and Bending Libraries", "ProplanAiTileText": "Proplan AI", "3dWhiteBoardTileText": "3d Whiteboard", "manyMoreTileText": "Many more...", "platformLandingpageTitle": "Welcome to Nexus", "platformLandingpageDescription": "Empowering teams to design and manufacture at scale. Connect people, technology and data to bring your ideas to life.", "platformLandingpageSignup": "Don't have an account? Sign up", "platformLandingpageLearnMore": "Learn more", "noAccess": "No Access", "shareDocumentOrgLevel": "Document shared with", "access": "access", "errorShareDocumentOrgLevel": "Issue when sharing Document", "manageExtensions": "Manage Extensions", "anyoneFrom": "Anyone from", "description": "Description", "tag": "TAG", "url": "URL", "documentationUrl": "Documentation URL", "communityForumsUrl": "Community Forums URL", "requestHelpUrl": "Request Help URL", "openLinkIn": "Open link in", "add": "Add", "baseExtensions": "Base Extensions", "addExtension": "Add Extension", "addVariant": "<PERSON><PERSON>", "editExtension": "Edit Extension", "update": "Update", "edit": "Edit", "editVariant": "<PERSON>", "systemBrowser": "System Browser", "floatingWindow": "Floating Window", "systemBrowserDescription": "ex. Chrome, Edge. When user is actively editing in or focusing on the app for more than 2 steps.", "floatingWindowDescription": "ex. CEF Browser. When user is quickly importing, selecting elements or adjusting settings", "variantUpdated": "<PERSON><PERSON><PERSON> updated successfully", "variantAdded": "<PERSON><PERSON><PERSON> added successfully", "variantAddedError": "Unable to add variant", "variantUpdatedError": "Unable to update variant", "appExtensionsAdded": "App extensions added successfully", "appExtensionsUpdated": "App extensions updated successfully", "appExtensionsAddedError": "Unable to add app extensions", "appExtensionsUpdatedError": "Unable to update app extensions", "baseExtensionsUpdated": "Base extensions updated successfully", "baseExtensionsAdded": "Base extensions added successfully", "baseExtensionsUpdatedError": "Unable to update base extensions", "baseExtensionsAddedError": "Unable to add base extensions", "variantDeleted": "<PERSON><PERSON><PERSON> deleted successfully", "variantDeletedError": "Unable to delete variant", "appExtensionsDeleted": "App extensions deleted successfully", "appExtensionsDeletedError": "Unable to delete app extensions", "baseExtensionsDeleted": "Base extensions deleted successfully", "baseExtensionsDeletedError": "Unable to delete base extensions", "allApps": "All Apps", "variantId": "Variant ID", "deleteConfirmationExtensions": "All data will be deleted, this action is permanent and cannot be undone.", "baseExtensionsDeleteError": "This base extension is currently in use and cannot be deleted.", "tagLengthError": "Tag should be at least 3 characters and at most 50 characters", "joinTheCommunity": "Join the community", "communityDescription": "Meet other Engineers, Designers, and Quality Experts in the Nexus Communities.", "browseCommunities": "Browse communities", "contactSales": "Contact sales", "contactSalesDescription": "Reach out to our team for questions about 3D Whiteboard functionality and pricing.", "getConnected": "Get connected", "getHelp": "Get help", "helpDescription": "Have an issue to report? Create a support request for our team to assist you.", "createTicket": "Create a ticket", "whiteboardDescription": "Test our 3D part demo to explore our collaborative features such as inking and commenting in a 3D environment.", "computeDescription": " Unlock scalable, secure computing power on-demand. Nexus Compute delivers flexible and cost-efficient solutions.", "metrolgyDescription": "Unlock clear part quality insights with our Metrology Reporting tool, featuring centralized, shareable reports anytime, anywhere.", "freeTrial": "Free Trial", "startTrial": "Start Trial", "openDemo": "Open Demo", "demo": "Demo", "tryOutNexus": "Try out <PERSON><PERSON>us", "helpfulResources": "Helpful Resources", "findMoreapps": "Find more Nexus apps", "noProjectsHeading": "There are no projects in this Nexus account yet.", "CAD_Assembly": "CAD Assembly", "CAD_Assembly_Description": "Explore, share, and review CAD models with assembly hierarchy", "Additive_Manufacturing": "Additive Manufacturing", "Additive_Manufacturing_Description": "Collaborate using geometries from various steps within the Additive Manufacturing process"}