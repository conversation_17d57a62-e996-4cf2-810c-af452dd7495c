import enUS from './en-US.json';
import enGB from './en-GB.json';
import deDE from './de-DE.json';
import frFR from './fr-FR.json';
import itIT from './it-IT.json';
import ja from './ja.json';
import ko from './ko.json';

export const resources: Record<string, Record<string, string> | any> = {
  'en-US': {
    translation: enUS
  },
  'en-GB': {
    translation: enGB
  },
  'de-DE': {
    translation: deDE
  },
  'fr-FR': {
    translation: frFR
  },
  'it-IT': {
    translation: itIT
  },
  ja: {
    translation: ja
  },
  ko: {
    translation: ko
  }
};
