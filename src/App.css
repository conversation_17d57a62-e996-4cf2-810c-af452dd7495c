.hex-flex-container {
  display: flex;
  flex-direction: column;
}

.hex-loading-container {
  align-items: center;
  justify-content: center;
}

.hex-loading-animation {
  width: 50%;
  height: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.hex-left-menu {
  width: 3.5rem;
  height: 100%;
  background: linear-gradient(180deg, #00526f 0%, #00011b 100%);
  display: flex;
  flex-direction: column;
}

.hex-flex-row {
  display: flex;
}

.hex-left-menu .item {
  color: white;
  fill: white;
  display: flex;
  flex-direction: column;
}

.hex-left-menu .item.selected {
  background: white;
  color: #00011b;
  fill: #00011b;
}

.hex-left-menu .item:hover {
  background: white;
  color: #00011b;
  fill: #00011b;
}

.hex-right-panel-container {
  flex-grow: 1;
}

.hex-title-row {
  padding: 0.75rem 24px 0 24px;
}

.hex-label-row {
  padding: 1rem 1rem;
  display: flex;
  flex-wrap: wrap;
  flex-direction: row;
}

.hex-project-list-view {
  flex-direction: column;
  width: 100%;
  overflow: auto;
  height: calc(100vh - 310px);
}

.hex-project-list-view.no-filter {
  height: calc(100vh - 310px);
}

/* Media Query for Mobile Devices */
@media (max-width: 599px) {
  .hex-project-list-view {
    margin: 0px -4px;
  }
}

.css-1aeiuoz-MuiButtonBase-root-MuiMenuItem-root {
  margin-left: 0 !important;
}
/* .hex-main-container {
  height: calc(100vh - 113px);
  overflow: auto;
} */

/* .hex-footer {
  position: fixed;
  bottom: 0;
} */
