export interface userAcl {
  userId: string;
  role: string;
  defaultDocumentAccess: string | null;
  defaultCollaborator: boolean;
  documentCount: number;
}

export interface userAccess extends userAcl {
  access: string | null;
}

export interface IFolderList {
  id: string;
  organizationId: string;
  name: string;
  description: string;
  documentCount: number;
  tags: string[];
  metadata?: { [key: string]: string };
  status: string;
  acl: userAccess[];
  createdBy: string;
  createdByDate: string;
  lastModifiedBy: string;
  lastModifiedByDate: string;
  canUnfollow: boolean;
}
