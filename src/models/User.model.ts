/**
 * The type definition are totally follow the API swagger:
 * https://dev.nexus.hexagon.com/api/ams/swagger/
 */

import { RoleViewModel } from './role.model';

export type Page = {
  total: number;
  size: number;
  index: number;
};

/** ********************User Model************************/
export type UserOrg = {
  organizationId: string;
  roles: { direct: RoleViewModel[] };
};
export type UserViewModel = {
  auth0Id: string;
  email: string;
  firstName: string;
  lastName: string;
  phoneNumber: string;
  language: string;
  country: string;
  timeZone: string;
  creator: string;
  createdDate: string;
  updatedBy: string;
  updatedDate: string;
  enabled: boolean;
  archived: boolean;
  misc: Record<string, any>;
  auth0Metadata: Record<string, any>;
  roles: RoleViewModel[];
  [key: string]: any;
};

export type UpdateUserViewModel = {
  userAuth0Id: string;
  firstName: string;
  lastName: string;
  phoneNumber: string;
  language: string;
  country: string;
  timezone: string;
  measurementSystem: string;
  roles: string[];
  organizationId: string;
  misc: any;
};

export type FlattenedUserViewModel = {
  id: string;
  avatar: string;
  email: string;
  roles: { direct?: RoleViewModel[] };
  status: 'Active' | 'Inactive' | 'Invite Pending';
  firstName: string;
  lastName: string;
  phoneNumber: string;
  lastLogin: string;
  invitationId?: string;
  organizations: UserOrg[];
};

export type FlattenedUserViewModelsWithPagination = {
  data: FlattenedUserViewModel[];
  page: Page;
};

export type UpdateUserPayload = {
  userAuth0Id: string;
  firstName: string;
  lastName: string;
  phoneNumber: string;
  language?: string;
  country?: string;
  timezone?: string;
  measurementSystem?: string;
  misc?: Record<string, never>;
};

export type UpdateUserRole = {
  roleId: string;
  applicationId: string;
  organizationId: string;
};
export type UpdateUserRolePayload = {
  add: UpdateUserRole[];
  remove: UpdateUserRole[];
};

/** ********************User Invite Model************************/

export type NewInvitationRequestViewModel = {
  organizationId: string;
  emailsReceived: string[];
  roles: string[];
};

export type InvitationViewModel = {
  auth0Id: string;
  organizationId: string;
  expiresAt: string;
  clientId: string;
  connectionId: string;
  roles: string[];
  status: string;
  creator: string;
  createdDate: string;
  updatedBy: string;
  updatedDate: string;
};

export type FailedInvitationRequest = {
  email: string;
  error: Record<string, any>;
};

export type InvitationCreationResultViewModel = {
  success: InvitationViewModel[];
  failed: FailedInvitationRequest[];
};

/** ********************User Activities Model************************/

export type EventViewModel = {
  id: string;
  date: string;
  description: string;
  clientId: string;
  clientName: string;
  ip: string;
  hostname: string;
  userId: string;
  userAgent: string;
  locationInfo: LocationInfoViewModel;
};

export type LocationInfoViewModel = {
  countryCode: string;
  countryCode3: string;
  countryName: string;
  cityName: string;
  latitude: string;
  longitude: string;
  timezone: string;
  continentCode: string;
};

export type UserLogsWithPagination = {
  data: EventViewModel[];
  page: Page;
};

export interface UserInfoResponse {
  id: string;
  name: string;
  email: string;
  picture: string;
  metadata: object;
  lastLogin: string;
  status: 'Active' | 'Inactive' | 'Pending';
  archived: boolean;
  jobTitle: string;
  familyName: string;
  givenName: string;
  phone: number[];
  role: string;
}

export type UserActivityType = {
  id: string;
  activeDate: string;
  activeEvent: string;
  location: string;
  os: string;
  exactTime: string;
};

export interface IOrgItemSelfUser {
  organizationId: string;
  organizationName: string;
  archived: boolean;
  enabled: boolean;
  roles: {
    direct: RoleViewModel[];
    applications: RoleViewModel[];
  };
}

export interface IUserSelfResponse {
  organizations: IOrgItemSelfUser[];
}

export interface IUserDetails {
  name: string;
  picture: string;
  email: string;
  given_name: string;
  family_name: string;
}

export interface IBasicUser {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  avatar: string;
}

export interface ContactInfo extends IBasicUser {
  role: string;
  mobile: string;
}

export interface LanguageRegionInfo {
  language: string;
  languageCode?: string;
  country: string;
  countryCode?: string;
  timezone: string;
  measurement: string;
}

export interface UserInvitation {
  emails: ReadonlyArray<string>;
  role: string;
}

export enum StatusValues {
  active = 'Active',
  inactive = 'Inactive',
  invitePending = 'Invite Pending'
}
export interface UserItem extends IBasicUser {
  role: string;
  phoneNumber?: string;
  lastActive?: string;
  status: StatusValues;
  [x: string]: any;
}
