export interface IPageInfo {
  total: number;
  size: number;
  index: number;
}
export interface IOrganizationItem {
  id: string;
  name: string;
  admin: {
    name: string;
    email: string;
    auth0Id: string;
  };
  address1: string;
  address2: string;
  zipCode: string;
  country: string;
  createdDate: string;
  creator: string;
  updatedBy: string;
  updatedDate: string;
  status: StatusValues;
  archived: boolean;
  region: string;
  salesforceId: string;
  fluidRelayService: IFluidRelayServiceValue;
}

export type GETOrganizationsResponse = {
  data: IOrganizationItem[];
  page: IPageInfo;
};

export type CreateOrgPayload = {
  name: string;
  status: 'Active' | 'Inactive' | 'Removed' | '';
  // adminEmail: string;
  address1: string;
  address2?: string;
  zipCode: string;
  country: string;
  region?: string;
  salesforceId: string;
  fluidRelayServiceId: string;
};

export type CreateOrgResponse = {
  id: string;
  auth0Id: string;
  name: string;
  admin: {
    name: string;
    email: string;
    auth0Id: string;
  };
  address1: string;
  address2?: string;
  zipCode: string;
  country: string;
  defaultApplicationClientId: string;
  createdDate: string;
  creator: string;
  updatedBy: string;
  updatedDate: string;
  enabled: boolean;
  archived: boolean;
  region: string;
  salesforceId: string;
  fluidRelayService: IFluidRelayServiceValue;
};

export type EditOrgPayload = {
  id: string;
  name: string;
  status: 'Active' | 'Inactive' | 'Removed' | '';
  adminEmail: string;
  address1: string;
  address2?: string;
  zipCode: string;
  country: string;
  region?: string;
  salesforceId: string;
  fluidRelayServiceId: string;
};
export type EditOrgResponse = CreateOrgResponse;

export enum SortOrder {
  asc = 1,
  desc = -1,
  none = 0
}
export interface ISortOption {
  field: string;
  sort: SortOrder;
}
export interface ISearchOption {
  field?: string;
  text: string;
}

export interface IFluidRelayServiceValue {
  containerTenantId: string;
  containerEndpoint: string;
  containerTokenProvider: string;
  origin: string;
}

export interface IFluidRelayService {
  id: string;
  name: string;
  displayName: string;
  description: string;
  value: IFluidRelayServiceValue;
  isEnabled: true;
  creator: string;
  createdDate: string;
  updatedBy: string;
  updatedDate: string;
  order: number;
  category: string;
}

export interface IMenuItem {
  value: string;
  label: string;
}

export enum StatusValues {
  active = 'Active',
  inactive = 'Inactive',
  removed = 'Removed'
}

export interface IOrganization {
  id?: string;
  name: string;
  email?: string;
  status: StatusValues;
  address1: string;
  address2?: string;
  salesforceId: string;
  zipCode: string;
  country: string;
  createdBy?: string;
  updatedDate: string;
  fluidRelayServiceId?: string;
}
