/* eslint-disable no-unused-vars */
export interface IProject {
  id?: string;
  project: string;
  description: string;
  schemaTypeId: string;
  tags?: Array<string>;
  autoSave?: boolean;
  thumbnail?: string;
  lastModifiedBy?: string;
  lastModifiedByDate?: string;
  createdByDate?: string;
  canDelete?: boolean;
  canUnfollow?: boolean;
  canUserRename?: boolean;
  canChangeCover?: boolean;
  thumbnailLocal?: string;
  projectId?: string;
  acl?: Array<IACLUser>;
  createdBy?: string;
  mostRecentlyUsed?: object;
}

export interface IGroupedData {
  name: string;
  data: Array<{ name: string; label: string }>; // label is the translated value
}

export interface IACLUser {
  access: string;
  userId: string;
  organizationId?: string;
}

export enum DocumentType {
  allDocuments = 'allDocuments',
  drafts = 'drafts',
  projectDocuments = 'projectDocuments',
  tryaDemo = 'tryaDemo'
}

export enum PageType {
  Drafts = 'Drafts',
  Documents = 'Documents',
  Projectcard = 'Projectcard',
  Projects = 'Projects'
}
