import axios from 'axios';
import { BASE_URL as URL } from '../configuration/URL.config';

/**
 * Provides an instance for the axios client
 * To use, import the instance and use it as a normal axios client
 * @returns  - provides an axios instance to use within the application
 **/
const instance = axios.create({ baseURL: URL });
instance.interceptors.request.use((req) => {
  return req;
});

export { instance };
