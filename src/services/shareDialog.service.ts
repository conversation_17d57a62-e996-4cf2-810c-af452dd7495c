import axios, { RawAxiosRequestHeaders } from 'axios';
import { BASE_URL, AUTHRIZATION_BASE_URL } from '../configuration/URL.config';
import { IFolderList } from '../models/Folders';

export const getSDCInstance = async (sdcInstanceId: string, customHeaders?: RawAxiosRequestHeaders): Promise<void> => {
  return (await axios.get(`${BASE_URL}/sdc/instances/${sdcInstanceId}`, { headers: { ...customHeaders, 'Content-Type': 'application/json' } })).data;
};

export const getFolderInstance = async (projectId: string, customHeaders?: RawAxiosRequestHeaders): Promise<IFolderList> => {
  return (await axios.get(`${BASE_URL}/project/instances/${projectId}`, { headers: { ...customHeaders, 'Content-Type': 'application/json' } })).data;
};

export const getProjectUserDetails = async (instanceId: string, customHeaders?: RawAxiosRequestHeaders): Promise<any> => {
  return (await axios.get(`${BASE_URL}/project/instances/${instanceId}/share/users`, { headers: { ...customHeaders, 'Content-Type': 'application/json' } })).data;
};

export const updateSDCUserOrOrgAcl = async (
  sdcInstanceId: string,
  userOrOrgShareAcl: { type?: string; email?: string; organizationId?: string; access: string }[],
  customHeaders?: RawAxiosRequestHeaders
) => {
  return axios.put(
    `${BASE_URL}/sdc/instances/${sdcInstanceId}/acl`,
    JSON.stringify({
      acl: userOrOrgShareAcl
    }),
    {
      headers: { ...customHeaders, 'Content-Type': 'application/json' }
    }
  );
};

export const updateProjectUser = async (
  projectId: string,
  users: { email: string; access: string; role?: string }[],
  defaultCollaborator: boolean,
  shareAllExistingDocuments: boolean,
  customHeaders?: RawAxiosRequestHeaders
) => {
  return axios.put(
    `${BASE_URL}/project/instances/${projectId}/acl`,
    JSON.stringify({
      acl: users,
      defaultCollaborator,
      shareAllExistingDocuments
    }),
    {
      headers: { ...customHeaders, 'Content-Type': 'application/json' }
    }
  );
};

export const removeSDCUserOrOrgAcl = async (
  data: { userIds?: string[]; organizationId?: string },
  sdcInstanceId: string,
  customHeaders?: RawAxiosRequestHeaders,
  queryParams?: { mode: string }
): Promise<any> => {
  return axios.delete(`${BASE_URL}/sdc/instances/${sdcInstanceId}/acl`, {
    data: data,
    headers: { ...customHeaders, 'Content-Type': 'application/json' },
    params: queryParams
  });
};

export const removeProjectUser = async (userId: string, projectInstanceId: string, customHeaders?: RawAxiosRequestHeaders): Promise<any> => {
  return axios.delete(`${BASE_URL}/project/instances/${projectInstanceId}/acl`, {
    data: { userIds: [userId] },
    headers: { ...customHeaders, 'Content-Type': 'application/json' }
  });
};

export const getCurrentOrganizationUsers = async (customHeaders?: RawAxiosRequestHeaders, keyword?: string): Promise<any> => {
  return (
    await axios.get(
      `${AUTHRIZATION_BASE_URL}/userself/organization/users?per_page=9999&q=${
        keyword ? '' : 'enabled:1|'
      }email:${keyword}|firstName:${keyword}|lastName:${keyword}&sort=firstname:-1`,
      { headers: { ...customHeaders, 'Content-Type': 'application/json' } }
    )
  ).data;
};
