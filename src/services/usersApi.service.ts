import { StatusValues, UserInvitation, UserItem } from '../models/User.model';
import axios, { RawAxiosRequestHeaders } from 'axios';
import { AUTHRIZATION_BASE_URL as URL } from '../configuration/URL.config';
import { FlattenedUserViewModel, NewInvitationRequestViewModel, UpdateUserPayload, UpdateUserRole, UpdateUserRolePayload, UserActivityType, UserOrg } from '../models/User.model';
import { RoleViewModel } from '../models/role.model';
import moment from 'moment';
import { v4 } from 'uuid';
import { UserRoles } from '../context/AdminContext';

const stringValues = {
  justNow: 'Just now',
  readMore: 'Read more',
  readLess: 'Read less',
  showMore: 'Show more',
  showLess: 'Show less',
  replies: ' replies',
  close: 'Close'
};

const timeAgo = (date: any) => {
  const formatter = new Intl.RelativeTimeFormat('en');
  const ranges: any = {
    years: 3600 * 24 * 365,
    months: 3600 * 24 * 30,
    weeks: 3600 * 24 * 7,
    days: 3600 * 24,
    hours: 3600,
    minutes: 60,
    seconds: 1
  };
  const a = new Date(date);
  const secondsElapsed = (a?.getTime() - Date.now()) / 1000;
  for (const key in ranges) {
    if (ranges[key] < Math.abs(secondsElapsed)) {
      const delta = secondsElapsed / ranges[key];
      return formatter.format(Math.round(delta), key as Intl.RelativeTimeFormatUnit);
    }
  }
  return stringValues?.justNow;
};

const getHeaders = (customHeaders?: RawAxiosRequestHeaders) => {
  return {
    ...customHeaders,
    'Content-Type': 'application/json'
  };
};

export const getUserActivities = async (userId: any, customHeaders?: RawAxiosRequestHeaders) => {
  const config = {
    url: `${URL}/users/${userId}/logs`,
    headers: getHeaders(customHeaders)
  };
  try {
    const response = await axios.get(config.url, { headers: config.headers });
    if (response.status === 200) {
      response.data.data = response.data.data.map((userLog: any): UserActivityType => {
        return {
          id: userLog.id,
          activeDate: timeAgo(userLog.date),
          activeEvent: userLog.description,
          location: `${userLog?.locationInfo?.countryName ?? ''}, ${userLog?.locationInfo?.countryCode ?? ''}`,
          os: userLog?.userAgent?.split('/')[1] ?? '',
          exactTime: userLog.date
        };
      });
      return response;
    }
  } catch (error: any) {
    console.error('error by API', error);
    return error.response;
  }
};

export const getRolesList = async (customHeaders?: RawAxiosRequestHeaders) => {
  const config = {
    url: `${URL}/roles/system`,
    headers: getHeaders(customHeaders)
  };
  try {
    const response = await axios.get(config.url, { headers: config.headers });
    if (response.status === 200) {
      // Returns two roles Organization Admin and User.
      response.data = response.data
        .filter((role: RoleViewModel) => role.name === UserRoles.OrganizationAdministrator || role.name === UserRoles.User)
        .map((role: RoleViewModel) => ({ id: role.id, value: role.id, label: role.name }));
    }
    return response;
  } catch (error: any) {
    console.error('error by API', error);
    return error.response;
  }
};

export const getAllUsers = async (orgId: string, page: number, pageSize: number, keywords?: string, sort?: string, customHeaders?: RawAxiosRequestHeaders) => {
  const config = {
    url: `${URL}/organizations/${orgId}/users?page=${page + 1}&per_page=${pageSize}&q=${keywords}&sort=${sort}`,
    headers: getHeaders(customHeaders)
  };
  try {
    const response = await axios.get(config.url, { headers: config.headers });
    if (response.status === 200 && response.data.data) {
      response.data.data = response.data.data.map((userItem: FlattenedUserViewModel): UserItem => {
        return {
          ...userItem,
          id: userItem.id || (userItem.invitationId as string) || v4(),
          role: userItem?.organizations.find((org: UserOrg) => org.organizationId === orgId)?.roles?.direct[0]?.id as string,
          lastActive: userItem.lastLogin ? moment(userItem.lastLogin).format('MMMM Do YYYY, h:mm a') : '',
          status: userItem.status === StatusValues.inactive ? StatusValues.inactive : userItem.status === StatusValues.active ? StatusValues.active : StatusValues.invitePending
        };
      });
    }
    return response;
  } catch (error: any) {
    console.error('error by API', error);
    return error.response;
  }
};

export async function updateUser(user: UserItem, orgId: any, customHeaders?: RawAxiosRequestHeaders) {
  const payload: UpdateUserPayload = {
    userAuth0Id: user.id,
    firstName: user.firstName,
    lastName: user.lastName,
    phoneNumber: user.phoneNumber as string
  };
  const userId = user.id;
  const config = {
    url: `${URL}/users`,
    headers: getHeaders(customHeaders),
    data: JSON.stringify(payload)
  };
  try {
    const response = await axios.patch(config.url, config.data, { headers: config.headers });
    if (response.status === 200) {
      const data = response.data;
      response.data = {
        ...data,
        id: data.auth0Id,
        role: data.roles?.direct ? data.roles?.direct[0]?.id : ''
      };
    }

    return response;
  } catch (error: any) {
    console.error('error by API', error);
    return error.response;
  }
}

export async function updateUserRole(user: UserItem, orgId: any, customHeaders?: RawAxiosRequestHeaders) {
  const currentRoles = user.organizations
    .find((org: UserOrg) => org.organizationId == orgId)
    ?.roles?.direct?.map((role: RoleViewModel) => {
      return {
        roleId: role.id,
        applicationId: '',
        organizationId: orgId
      };
    });
  const isSameRole = currentRoles.find((role: UpdateUserRole) => role.roleId === user.role);
  if (isSameRole) return false;
  const payload: UpdateUserRolePayload = {
    add: [
      {
        roleId: user.role,
        applicationId: '',
        organizationId: orgId
      }
    ],
    remove: currentRoles
  };
  const userId = user.id;
  const config = {
    url: `${URL}/users/${userId}/roles`,
    headers: getHeaders(customHeaders),
    data: JSON.stringify(payload)
  };
  try {
    const response = await axios.put(config.url, config.data, { headers: config.headers });
    if (response.status === 200) {
      const data = response.data;
      response.data = {
        ...data,
        id: data.auth0Id,
        role: data.roles?.direct ? data.roles?.direct[0]?.id : ''
      };
    }

    return response;
  } catch (error: any) {
    console.error('error by API', error);
    return error.response;
  }
}
export async function inviteUsers(organizationId: string | undefined, user: UserInvitation, customHeaders?: RawAxiosRequestHeaders) {
  const payload: NewInvitationRequestViewModel = {
    organizationId: organizationId as string,
    emailsReceived: user.emails as string[],
    roles: [user.role]
  };

  const config = {
    url: `${URL}/users`,
    headers: getHeaders(customHeaders),
    data: JSON.stringify(payload)
  };
  try {
    const response = await axios.post(config.url, config.data, { headers: config.headers });

    return response;
  } catch (error: any) {
    console.error('error by API', error);
    return error.response;
  }
}

export const updateUserStatus = async (userId: string, status: number, customHeaders?: RawAxiosRequestHeaders) => {
  const config = {
    url: `${URL}/users/${userId}/${status}`,
    headers: getHeaders(customHeaders),
    data: JSON.stringify({})
  };
  try {
    const response = await axios.put(config.url, config.data, { headers: config.headers });
    return response;
  } catch (error: any) {
    console.error('error by API', error);
    return error.response;
  }
};

export const resendInvite = async (user: UserItem, organizationId: string | undefined, customHeaders?: RawAxiosRequestHeaders) => {
  const config = {
    url: `${URL}/organizations/${organizationId}/invitations/${user.invitationId}`,
    headers: getHeaders(customHeaders)
  };
  try {
    const response = await axios.put(config.url, {}, { headers: config.headers });

    if (response.status === 200) {
      response.data = { ...user, invitationId: response.data.auth0Id, lastActive: '' };
    }
    return response;
  } catch (error: any) {
    console.error('error by API', error);
    return error.response;
  }
};
export const cancelInvite = async (user: UserItem, organizationId: string | undefined, customHeaders?: RawAxiosRequestHeaders) => {
  const config = {
    url: `${URL}/organizations/${organizationId}/invitations/${user.invitationId}`,
    headers: getHeaders(customHeaders)
  };
  try {
    const response = await axios.delete(config.url, { headers: config.headers });
    if (response.status === 200) {
      response.data = { message: 'Invite cancelled successfully' };
    }
    return response;
  } catch (error: any) {
    console.error('error by API', error);
    return error.response;
  }
};

export const getUserSelf = async (customHeaders?: RawAxiosRequestHeaders) => {
  const config = {
    url: `${URL}/userself`,
    headers: getHeaders(customHeaders)
  };
  try {
    const response = await axios.get(config.url, { headers: config.headers });

    return response;
  } catch (error: any) {
    console.error('error by API', error);
    return null;
  }
};

export const updateDefaultOrg = async (orgId: string, customHeaders?: RawAxiosRequestHeaders) => {
  const config = {
    url: `${URL}/userself/star/organization/${orgId}`,
    headers: getHeaders(customHeaders)
  };
  try {
    const response = await axios.put(config.url, {}, { headers: config.headers });
    return response;
  } catch (error: any) {
    console.error('error by API', error);
    return null;
  }
};
