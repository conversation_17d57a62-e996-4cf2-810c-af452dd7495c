import { ContactInfo, LanguageRegionInfo } from '../models/User.model';
import axios, { RawAxiosRequestHeaders } from 'axios';
import { AUTHRIZATION_BASE_URL as URL } from '../configuration/URL.config';

const getHeaders = (customHeaders?: RawAxiosRequestHeaders) => {
  return {
    ...customHeaders,
    'Content-Type': 'application/json'
  };
};

export async function fetchUserPreference(customHeaders?: RawAxiosRequestHeaders) {
  const config = {
    url: `${URL}/userself`,
    headers: getHeaders(customHeaders)
  };

  try {
    const response = await axios.get(config.url, { headers: config.headers });
    return response;
  } catch (error: any) {
    console.error('error by API', error);
    return error.response;
  }
}
export const updateUserPreference = async (regionData: LanguageRegionInfo, customHeaders?: RawAxiosRequestHeaders) => {
  const payload = {
    language: regionData.language,
    country: regionData.country,
    timezone: regionData.timezone,
    measurementSystem: regionData.measurement
  };
  const config = {
    url: `${URL}/userself`,
    headers: getHeaders(customHeaders),
    data: JSON.stringify(payload)
  };

  try {
    // This will be removed once API provided.
    return { status: 200, data: regionData };
    //
    // const response = await axios.put(config.url, config.data, { headers: config.headers });
    // return response;
  } catch (error: any) {
    console.error('error by API', error);
    return error.response;
  }
};
export const updateUserContactInfo = async (userData: ContactInfo, orgId: string, userId: string, customHeaders?: RawAxiosRequestHeaders) => {
  const payload = {
    firstName: userData.firstName,
    lastName: userData.lastName,
    phoneNumber: userData.mobile,
    roles: [userData.role]
  };
  const config = {
    url: `${URL}/users/${orgId}/${userId}`,
    headers: getHeaders(customHeaders),
    data: JSON.stringify(payload)
  };

  try {
    // This will be removed once API provided.
    return { status: 200, data: userData };
    //
    // const response = await axios.put(config.url, config.data, { headers: config.headers });
    // if (response.status === 200) {
    //   response.data = {
    //     mobile: response.data.phoneNumber,
    //     role: response.data.role,
    //     firstName: response.data.firstName,
    //     lastName: response.data.lastName
    //   };
    // }
    // return response;
  } catch (error: any) {
    console.error('error by API', error);
    return error.response;
  }
};
