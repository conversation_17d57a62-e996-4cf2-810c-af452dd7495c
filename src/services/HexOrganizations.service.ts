import axios, { RawAxiosRequestHeaders } from 'axios';
import { IMenuItem, IOrganization, StatusValues } from '../models/organization.model';
import { AUTHRIZATION_BASE_URL as URL } from '../configuration/URL.config';
import { CreateOrgPayload, CreateOrgResponse, EditOrgPayload, EditOrgResponse, IFluidRelayService, IOrganizationItem } from '../models/organization.model';

const getFormattedDate = (date: string) => {
  try {
    return new Intl.DateTimeFormat('en', { dateStyle: 'medium', timeStyle: 'short' }).format(new Date(date));
  } catch (error) {
    return '';
  }
};
const getHeaders = (customHeaders?: RawAxiosRequestHeaders) => {
  return {
    ...customHeaders,
    'Content-Type': 'application/json'
  };
};

export const getOrganizations = async (page: number, pageSize: number, keywords?: string, sort?: string, customHeaders?: RawAxiosRequestHeaders) => {
  const config = {
    url: `${URL}/organizations?page=${page + 1}&per_page=${pageSize}&q=${keywords}&sort=${sort}`,
    headers: getHeaders(customHeaders)
  };
  try {
    const response = await axios.get(config.url, { headers: config.headers });

    if (response.status === 200 || response.status === 304) {
      response.data.data = response.data.data.map((orgItem: IOrganizationItem): IOrganization => {
        return {
          ...orgItem,
          status: orgItem.archived ? StatusValues.removed : orgItem.status,
          email: orgItem.admin.email,
          updatedDate: getFormattedDate(orgItem.updatedDate),
          createdBy: orgItem.admin.name,
          salesforceId: orgItem.salesforceId ?? '',
          fluidRelayServiceId: orgItem?.fluidRelayService?.origin ?? ''
        };
      });
    }
    return response;
  } catch (error: any) {
    console.error('error by API', error.response);
    return error.response;
  }
};

export const createOrganization = async (org: IOrganization, customHeaders?: RawAxiosRequestHeaders) => {
  const changeCreateOrgKey: CreateOrgPayload = {
    name: org.name,
    // adminEmail: org.email as string,
    address1: org.address1,
    address2: org.address2,
    zipCode: org.zipCode,
    country: org.country,
    status: org.status,
    salesforceId: org.salesforceId ?? '',
    fluidRelayServiceId: org.fluidRelayServiceId ?? ''
  };

  const config = {
    url: `${URL}/organizations`,
    headers: getHeaders(customHeaders),
    data: JSON.stringify(changeCreateOrgKey)
  };

  try {
    const response = await axios.post<CreateOrgResponse>(config.url, config.data, { headers: config.headers });
    if (response.status == 201) {
      const org: IOrganization = {
        ...response.data,
        id: response.data.auth0Id,
        status: response.data.archived ? StatusValues.removed : response.data.enabled ? StatusValues.active : StatusValues.inactive,
        email: response.data.admin.email,
        updatedDate: getFormattedDate(response.data.updatedDate),
        createdBy: response.data.admin.name,
        fluidRelayServiceId: response.data?.fluidRelayService?.origin ?? ''
      };

      return { data: org, status: response.status };
    }
  } catch (error: any) {
    console.log('error from API', error);
    return error.response;
  }
};

export const editOrganization = async (org: IOrganization, customHeaders?: RawAxiosRequestHeaders) => {
  const editData: EditOrgPayload = {
    name: org.name,
    adminEmail: org.email as string,
    address1: org.address1,
    address2: org.address2 || '',
    zipCode: org.zipCode,
    country: org.country,
    status: org.status,
    id: org.id as string,
    salesforceId: org.salesforceId ?? '',
    fluidRelayServiceId: org.fluidRelayServiceId ?? ''
  };

  const config = {
    url: `${URL}/organizations`,
    headers: getHeaders(customHeaders),
    data: JSON.stringify(editData)
  };

  try {
    const response = await axios.patch<EditOrgResponse>(config.url, config.data, { headers: config.headers });
    if (response.status == 200) {
      const org: IOrganization = {
        ...response.data,
        id: response.data.auth0Id,
        status: response.data.archived ? StatusValues.removed : response.data.enabled ? StatusValues.active : StatusValues.inactive,
        email: response.data.admin.email,
        updatedDate: getFormattedDate(response.data.updatedDate),
        createdBy: response.data.admin.name,
        salesforceId: response.data.salesforceId ?? '',
        fluidRelayServiceId: response.data?.fluidRelayService?.origin ?? ''
      };

      return { data: org, status: response.status };
    }
  } catch (error: any) {
    console.error('error from API', error);
    return error.response;
  }
};

export const activateOrDeactivate = async (orgId: string, organizationStatus: any, customHeaders?: RawAxiosRequestHeaders, params?: any) => {
  const config = {
    url: `${URL}/organizations/${orgId}/${organizationStatus}`,
    headers: getHeaders(customHeaders),
    data: {}
  };
  try {
    const response = await axios.put(config.url, config.data, { headers: config.headers });

    return response;
  } catch (error: any) {
    console.error('error from API', error);
    return error.response;
  }
};
export const deleteOrg = async (orgId: string, customHeaders?: RawAxiosRequestHeaders, params?: any) => {
  const config = {
    url: `${URL}/organizations/${orgId}`,
    headers: getHeaders(customHeaders)
  };
  try {
    const response = await axios.delete(config.url, { headers: config.headers });
    return response;
  } catch (error: any) {
    console.error('error from API', error);
    return error.response;
  }
};

export const getOrganizationById = async (orgId: string, customHeaders?: RawAxiosRequestHeaders) => {
  const config = {
    url: `${URL}/organizations/${orgId}`,
    headers: getHeaders(customHeaders)
  };
  try {
    const response = await axios.get(config.url, { headers: config.headers });
    if (response.status === 200) {
      return response;
    }
  } catch (error: any) {
    console.error('error by API', error.response);
    return error.response;
  }
};

export const getFluidRelayServices = async (customHeaders?: RawAxiosRequestHeaders) => {
  const config = {
    url: `${URL}/system/config/frs?includeDisabled=false`,
    headers: getHeaders(customHeaders)
  };
  try {
    const response = await axios.get(config.url, { headers: config.headers });
    if (response.status === 200) {
      response.data = response.data.map((d: IFluidRelayService): IMenuItem => {
        return {
          value: d.id,
          label: d.displayName ?? d.name
        };
      });
    }
    return response;
  } catch (error: any) {
    console.error('error by API', error.response);
    return error.response;
  }
};
