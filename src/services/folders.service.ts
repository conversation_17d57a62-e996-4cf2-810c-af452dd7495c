import { instance as client } from './Communication.service';

export const unfollowProject = async (projectId: string, userIds: Array<any>, jwt: string): Promise<any> => {
  client.defaults.headers.common['authorization'] = `Bearer ${jwt}`;

  try {
    const resp = client.request({
      headers: { authorization: `Bearer ${jwt}` },
      method: 'delete',
      url: `/project/instances/${projectId}/acl`,
      data: JSON.stringify({ userIds: userIds })
    });
    return resp;
  } catch (error: any) {
    console.error('UNFOLLOW API FAILED', error);
    return error.response;
  }
};
