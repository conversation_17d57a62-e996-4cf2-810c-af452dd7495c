import { IDocument } from '@nexusplatform/core-react-components';

export const loadFile = async (blob: Blob) => {
  return new Promise((resolve) => {
    if (!blob) {
      return;
    }
    const fileReader = new FileReader();
    fileReader.onload = (e) => {
      resolve(e.target?.result);
    };
    fileReader.readAsDataURL(blob);
  });
};

const fetchThumbnail = async (item: any, token: string) => {
  try {
    if (item.thumbnail && !item['thumbnailLocal']) {
      const response = await fetch(item.thumbnail, { headers: { authorization: `Bearer ${token}` } });
      if (response.status === 200) {
        const blob = await response.blob();
        await loadFile(blob).then((srcContent) => {
          if (typeof srcContent === 'string' && item.thumbnail) {
            item['thumbnailLocal'] = srcContent;
          }
        });
      }
      return item;
    } else {
      return item;
    }
  } catch (err) {
    console.error(err);
  }
  return item;
};

export const fetchThumbnails = async (data: IDocument[], token: string): Promise<IDocument[]> => {
  return Promise.all(data.map((item) => fetchThumbnail(item, token)));
};

export const downloadThumbnails = async (
  currentProjects: IDocument[],
  newProjects: IDocument[],
  token: Promise<string>,
  setData: (value: React.SetStateAction<IDocument[]>) => void
) => {
  if (!newProjects || newProjects.length < 1) return;
  const updatedThumbnailProjects: IDocument[] = [];
  // Keep the previous thumbnails
  newProjects?.forEach((newData: any) => {
    if (currentProjects.find((i) => i.id === newData.id)) {
      const oldData = currentProjects.filter((i) => i.id === newData.id)[0];
      if ((newData.thumbnail || oldData.thumbnail) && newData.thumbnail !== oldData.thumbnail) {
        updatedThumbnailProjects.push(newData);
      }
    } else updatedThumbnailProjects.push(newData);
  });

  // Download the missing/updated thumbnails
  const projectsWithThumbnails = await fetchThumbnails(updatedThumbnailProjects, await token);

  newProjects.forEach((newProject: any) => {
    if (projectsWithThumbnails.find((i) => i.id === newProject.id)) {
      const updatedData: any = projectsWithThumbnails.filter((i) => i.id === newProject.id)[0];
      newProject['thumbnailLocal'] = updatedData['thumbnailLocal'];
    }
  });

  // rerender with the updated thumbnail data
  projectsWithThumbnails.length > 0 && setData([...newProjects]);
};
