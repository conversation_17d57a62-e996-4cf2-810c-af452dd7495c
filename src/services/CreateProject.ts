import axios from 'axios';
import { BASE_URL } from '../configuration/URL.config';

export const createProject = async (customHeaders?: any, projectData?: any): Promise<any> => {
  try {
    const response = await axios.post<any>(BASE_URL + '/project/instances', projectData, {
      headers: {
        ...customHeaders,
        'Content-Type': 'application/json'
      }
    });
    return response;
  } catch (error: any) {
    console.error('error by API', error.response);
    return error.response;
  }
};
