import React, { ComponentType, useEffect, useState } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';

import { ProtectedRoute } from '@nexusplatform/react';

import { inTeams } from '../components/IntegrateToTeams/teams-js-hooks/utils/inTeams';

// Main Component
import App from '../App';
import DocumentRedirect from '../pages/DocumentRedirect';

const Config = React.lazy(() => import('../components/IntegrateToTeams/Config'));
const SidePanel = React.lazy(() => import('../components/IntegrateToTeams/SidePanel'));
const Stage = React.lazy(() => import('../components/IntegrateToTeams/Stage'));
const AuthStart = React.lazy(() => import('../components/IntegrateToTeams/Authentication/AuthStart'));
const AuthEnd = React.lazy(() => import('../components/IntegrateToTeams/Authentication/AuthEnd'));
const ProjectLandingPage = React.lazy(() => import('../components/ProjectLandingPage'));
const ProjectDocumentView = React.lazy(() => import('../components/ProjectDocumentView'));
const AuditData = React.lazy(() => import('../components/AuditData/AuditData'));
const LandingPage = React.lazy(() => import('../components/LandingPage/LandingPage'));
const ComponentSwitcher = React.lazy(() => import('../components/ComponentSwitcher/ComponentSwitcher'));
import { useFlags, withLDProvider } from 'launchdarkly-react-client-sdk';
import { LD_CONFIG } from '../configuration/LD.config';
import ManageExtensions from '../components/manageExtensions';
import { InformationWithProgressBackDrop } from '../components/BackDrops/InformationWithProgressBackDrop';
import { t } from 'i18next';
import { useAuth } from 'react-oidc-context';
const isTeamsApp = inTeams();

function RoutesWithConfig() {
  const { isAuthenticated } = useAuth();
  const { platformLandingPageRouteEnable } = useFlags();

  const [routesReady, setRoutesReady] = useState<boolean>(false);

  const originalUrlBeforeLogin: string = window.location.href;
  useEffect(() => {
    const timer = setTimeout(() => {
      setRoutesReady(true);
    }, 2000); // 2-second delay to wait for isAuthenticated to have right value

    if (originalUrlBeforeLogin.indexOf('app=') !== -1) {
      if (originalUrlBeforeLogin.indexOf('skipLogin=true') !== -1) {
        window.sessionStorage.setItem('skipRedirectUrl', originalUrlBeforeLogin);
        const platformRedirectUrl = originalUrlBeforeLogin.replace('&skipLogin=true', '');
        window.sessionStorage.setItem('platformRedirectUrl', platformRedirectUrl);
      } else {
        window.sessionStorage.setItem('platformRedirectUrl', originalUrlBeforeLogin);
      }
      return () => clearTimeout(timer);
    }
  }, []);

  useEffect(() => {
    if (originalUrlBeforeLogin.indexOf('isSilentAuth=') !== -1) {
      // if optional param isSilentAuth=true is present in the platform url & the user is already Authenticated from auth0 then skip landingPage & redirect to platform homepage with progress bar
      const silentAuthRedirectUrl = originalUrlBeforeLogin.replace('?isSilentAuth=true', '');
      window.sessionStorage.setItem('skipLandingPage', 'true');
      window.sessionStorage.setItem('platformRedirectUrl', silentAuthRedirectUrl);
    }
  }, []);

  if (!routesReady) {
    return <InformationWithProgressBackDrop open={true} message={t('nexusPlatform')} description={t('loadingYourExperience')} />;
  }

  // if the feature flag platformLandingPageRouteEnable is enabled conditionally show platform route or new platform landingpage
  const renderLandingpage = (platformLandingPageRouteEnable) => {
    switch (platformLandingPageRouteEnable) {
      case true:
        return (
          <>
            <Route path="/" element={isAuthenticated ? <ProtectedRoute component={ProjectLandingPage} /> : <Navigate to="/landingpage" />} />
            <Route path="/landingpage" element={isAuthenticated ? <Navigate to="/" /> : <LandingPage />} />
          </>
        );

      case false:
        return <Route path="/" element={<ProtectedRoute component={ProjectLandingPage} />} />;
    }
  };

  return (
    <Routes>
      <Route element={<App />}>
        {/* {renderLandingpage(platformLandingPageRouteEnable)} */}
        <Route path="/" element={<ProtectedRoute component={ProjectLandingPage} />} />;
        <Route path="/project/:containerId" element={<ProtectedRoute component={DocumentRedirect} />} />
        <Route path="/audit/:containerId/:title" element={<ProtectedRoute component={AuditData} />} />
        <Route path="/projectfolders/:projectId" element={<ProtectedRoute component={ProjectDocumentView} />} />
        <Route path="/config" element={isTeamsApp ? <Config /> : <Navigate to="/" />} />
        <Route path="/sidepanel" element={isTeamsApp ? <SidePanel /> : <Navigate to="/" />} />
        <Route path="/stage" element={isTeamsApp ? <Stage /> : <Navigate to="/" />} />
        <Route path="/authstart" element={isTeamsApp ? <AuthStart /> : <Navigate to="/" />} />
        <Route path="/authend" element={isTeamsApp ? <AuthEnd /> : <Navigate to="/" />} />
        <Route path="/component/:name" element={<ProtectedRoute component={ComponentSwitcher} />} />
        <Route path="/manage-extensions/:selectedApp" element={<ProtectedRoute component={ManageExtensions} />} />
        <Route path="*" element={<Navigate to="/" />} />
      </Route>
    </Routes>
  );
}

export default withLDProvider({
  clientSideID: LD_CONFIG.clientSideID,
  reactOptions: {
    useCamelCaseFlagKeys: true
  }
})(RoutesWithConfig as ComponentType<Record<string, never>>);
