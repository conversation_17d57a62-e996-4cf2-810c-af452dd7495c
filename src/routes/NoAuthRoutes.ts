const noAuthRoutesLocal: string[] = ['/config/', '/sidepanel', '/stage', '/authstart', '/authend'];
const noAuthRoutesRemote: string[] = [
  '/platform-landing/config/',
  '/platform-landing/sidepanel',
  '/platform-landing/stage',
  '/platform-landing/authstart',
  '/platform-landing/authend'
];

export const getNoAuthRoutes = () => {
  return import.meta.env.VITE_APP_BASE !== 'dev' ? noAuthRoutesRemote : noAuthRoutesLocal;
};
