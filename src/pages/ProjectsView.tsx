// React import
import React, { useCallback, useContext, useEffect, useLayoutEffect, useRef, useState } from 'react';
// Custom Components
import { IProject } from '../models/Project';
import { ProjectGroupData, ShareAccessFilter, SortEnum, SortFilterDocument } from '../configuration/filters.config';
import { ContainerType, createSdcInstance, retrieveSdcInstances, SdcCollectionResponse } from '@nexusplatform/core';
import axios, { RawAxiosRequestHeaders } from 'axios';
import QueryString from 'qs';
import { InformationWithProgressBackDrop } from '../components/BackDrops/InformationWithProgressBackDrop';
import { Box, Typography, Stack, Drawer, List, ListItemText, ListItemButton, ListSubheader, Divider, IconButton, Chip, Tooltip, Button, Link } from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import LaunchIcon from '@mui/icons-material/Launch';

import { BASE_PATH, BASE_APP_PATH, BASE_URL, METRO_ROUTE, DIGITAL_PRODUCT_URL } from '../configuration/URL.config';
import { useSearchParams, useNavigate } from 'react-router-dom';
import {
  getApplicationGroupTag,
  getApplicationTag,
  getProjectTypeInfo,
  getAllProjectTypeInfos,
  getRedirectionKey,
  additiveManufacturingProjectTypeInfo,
  smartAssemblyProjectTypeInfo,
  plasticProjectTypeInfo,
  metrologyProjectTypeInfo,
  adamsCarProjectTypeInfo,
  computeProjectTypeInfo,
  productDigitalTwinProjectTypeInfo
} from '../utils/DataURI.utils';
import { useTranslation } from 'react-i18next';
import { HexSearch, IDocumentTypeInfo } from '@nexusplatform/core-react-components';
import { HexFilter } from '../components/HexFilter/HexFilter';
import { HexInputSelect } from '../components/CustomFormFields/HexInputSelect/HexInputSelect';
import { HexInputSelectMobile } from '../components/CustomFormFields/HexInputSelect/HexInputSelectMobile';

import { debounce } from '../utils/Debounce';
import { PAGESIZE } from '../configuration/PAGESIZE.config';
import { HexErrorPageCard } from '../components/ErrorPage/hex-error-page-card';
import { canUserEdit } from '../utils/CanUserEdit';
import { MATERIAL_CENTER_APPLICATIONS } from '../configuration/application.confg';
import SortIcon from '@mui/icons-material/Sort';
import FilterAltOutlinedIcon from '@mui/icons-material/FilterAltOutlined';
import PeopleAltOutlinedIcon from '@mui/icons-material/PeopleAltOutlined';
import { HexFilterMobile } from '../components/HexFilter/HexFilterMobile';
import { TagContext } from '../App';
import { isPointSolution, isProductionSoftwareApplication } from '../utils/IsPointSolution';
import { sdcPrivilegeContext } from '../context/SdcUserPrivilegesContext';
import SearchIcon from '@mui/icons-material/Search';
import { CAD_DEMO_PROJECT, DEMO_PROJECT } from '../utils/data/DemoProject';
import { useFlags } from 'launchdarkly-react-client-sdk';
import { AMS_AUDIENCE } from '../configuration/AUTH.config';
import Extensions from '../components/Extensions/Extensions';
import { ConnectedDocumentView } from '../components/ConnectedDocumentView/ConnectedDocumentView';
import { DocumentAction, IDocument } from '@nexusui/connected-components';
import { HexEmptyProject } from '../components/EmptyProjectView/HexEmptyProject';
import { HexAddProjectButtonMobile } from '../components/HexAddProjectButtonMobile/HexAddProjectButtonMobile';
import { CreateDocument, IDocumentInfo, OptionType, Snackbar } from '@nexusui/components';
import { useHexAuth } from '@nexusplatform/react';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '../redux';
import { setProjectCount } from '../redux/slices/userProjectCount';

export interface IProjectEventDetails {
  id: string;
  name: string;
  description: string;
}

interface ProjectViewProps {
  forceMobileMode?: boolean;
}

/**
 * Provides the project viewer
 */
function ProjectView({ forceMobileMode = false }: ProjectViewProps) {
  const [query] = useSearchParams();
  const appTag = query.get('app');
  const dynamicTag = query.get('dynamic-url');
  const paramsTag = query.get('filter');
  const currentlySelectedTag = query.get('currentlySelected');

  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isLoader, setIsLoader] = useState<boolean>(true);
  const [hexProjects, setHexProjects] = useState<Array<IProject>>([]);
  const [showCreateProject, setShowCreateProject] = useState<boolean>(false);
  const [showCreateDemoProject, setShowCreateDemoProject] = useState<boolean>(false);
  const [containerHeight, setContainerHeight] = useState<number>(0);
  const [showListView, setShowListView] = useState(false);
  const [showSavingDialog, setShowSavingDialog] = useState(false);
  const [hasError, setHasError] = useState<boolean>(false);
  const sortMenuAnchorEl = useRef(null);
  const [showSortFilter, setShowSortFilter] = useState(false);
  const [showMobileSelect, setShowMobileSelect] = useState<boolean>(false);
  const [selectedTags, setSelectedTags] = useState<string | undefined>(paramsTag ? paramsTag : appTag ? getApplicationGroupTag(appTag) : '');
  const [accessType, setAccessType] = useState<string>('all');
  const [showFilterMobile, setShowFilterMobile] = useState<boolean>(false);
  const [solutionOptions, setsolutionOptions] = useState<OptionType[]>([]);
  const projectsContainerRef = useRef<HTMLDivElement>(null);
  const sidePanelRef = useRef<HTMLDivElement>(null);
  const navigate = useNavigate();
  const xmsContinuation = useRef('');
  const hexAuthToken = useRef('');
  const projectNameInfo = useRef<string | undefined>('');
  const { t } = useTranslation();
  const pageSize = useRef(1);
  const projectsCount = useRef<number>(0);
  const [searchProjectName, setSearchProjectName] = useState<string | undefined>('');
  const [xmsToken, setXmsToken] = useState<string>('');
  const showProjectFilter = useRef(true);
  const filterAnchorRef = useRef(null);
  const accessTypeAnchorRef = useRef(null);
  const [isMobile, setIsMobile] = useState(forceMobileMode);
  const projectsRef = useRef<HTMLDivElement>(null);
  const currentPageRecord = 5;
  const selectedApp = useContext<string>(TagContext);
  const { sdcPrivileges } = useContext(sdcPrivilegeContext);
  const { designerButtonVisible, platformManageExtensionsLinkButtonVisible, platformSortLastOpenedBymeFilterMenuItemVisible } = useFlags();
  const [isDocument, setIsDocument] = useState<boolean>(false);
  const [documents, setDocuments] = React.useState<IDocument[]>([]); // The documents you want to display in connected document card
  const { getAccessTokenSilently } = useHexAuth();
  const user = useSelector((state: RootState) => state.userProfile.user);
  const projectCount = useSelector((state: RootState) => state.userProjectCount.projectCount);
  const dispatch = useDispatch();

  const showApexAlert = (e: any) => {
    if (e.detail && e.detail.severity) {
      switch (+e.detail.severity) {
        case 0:
        case 1:
          Snackbar.info(e.detail.message, { enableClose: true });
          break;
        case 2:
          Snackbar.warning(e.detail.message, { enableClose: true });
          break;
        case 3:
        case 4:
          Snackbar.error(e.detail.message, { enableClose: true });
          break;
        case 5:
          Snackbar.success(e.detail.message, { enableClose: true });
          break;
        default:
          Snackbar.info(e.detail.message, { enableClose: true });
          break;
      }
    }
  };
  const SortFilterDocumentFlagged = useRef(SortFilterDocument);

  const handleCreateDocumentClick = async () => {
    const selectedFilterProjectTypeInfo = selectedTags && getAllProjectTypeInfos(selectedTags.split(','));
    const projectTypeInfo = selectedFilterProjectTypeInfo && selectedFilterProjectTypeInfo.length > 0 ? selectedFilterProjectTypeInfo : getProjectTypeInfo(appTag || '');

    setsolutionOptions(() => {
      const options: OptionType[] = [];
      if (projectTypeInfo && projectTypeInfo.length > 0) {
        projectTypeInfo.forEach((project: IDocumentTypeInfo) => {
          options.push({ label: project.mainHeading, value: project.value });
        });
      }
      return [...options];
    });
    setShowCreateProject(true);
  };

  useEffect(() => {
    if (platformSortLastOpenedBymeFilterMenuItemVisible && SortFilterDocumentFlagged.current.options.length === 2) {
      SortFilterDocumentFlagged.current.options.push({ value: SortEnum.lastOpenedByMe, name: 'Sort by last opened by me' });
    }
  }, [platformSortLastOpenedBymeFilterMenuItemVisible]);

  useEffect(() => {
    console.log('Setting up native apps event listeners');
    window.addEventListener('apex:statusmessage', showApexAlert);

    return () => {
      console.log('Removing native apps event listeners');
      window.removeEventListener('apex:statusmessage', showApexAlert);
    };
  }, []);

  useEffect(() => {
    if (appTag) {
      showProjectFilter.current = getApplicationGroupTag(appTag) !== '' ? false : true;
    } else {
      showProjectFilter.current = true;
    }
  }, [appTag]);

  useEffect(() => {
    if (appTag) {
      getExtensions();
    }
  }, [appTag]);

  const getCustomHeaders = async () => {
    const JWT = await getAccessTokenSilently();
    if (JWT) {
      let customHeaders: RawAxiosRequestHeaders;
      hexAuthToken.current = JWT;

      if (xmsContinuation.current !== null && xmsContinuation.current) {
        customHeaders = {
          authorization: `Bearer ${JWT}`,
          'x-ms-continuation': xmsContinuation.current
        };
      } else {
        customHeaders = {
          authorization: `Bearer ${JWT}`
        };
      }
      return customHeaders;
    }
    return undefined;
  };
  const getExtensions = useCallback(async () => {
    const amsCustomHeaders = await getAmsCustomHeaders();
    let appTags;
    let appVariant;
    let result;
    const withVariantURL = new URL(window.location.href);
    if (isProductionSoftwareApplication(selectedApp)) {
      if (withVariantURL.searchParams.has('appVariant')) {
        appTags = withVariantURL.searchParams.get('app');
        appVariant = withVariantURL.searchParams.get('appVariant');
        result = await axios.get(BASE_URL + `/ams/applications/extensions?appTags=${appTags}&appVariant=${appVariant}`, { headers: amsCustomHeaders });
      } else {
        const url = window.location.href.toString().replace(/&/g, ',');
        const urlParams = new URLSearchParams(new URL(url).search);
        appTags = urlParams.get('app');
        result = await axios.get(BASE_URL + `/ams/applications/extensions?appTags=${appTags}`, { headers: amsCustomHeaders });
      }
      const documentView: any = result.data?.find((item) => item?.documentView === 'enabled') ?? [];
      if (documentView.length !== 0) {
        setIsDocument(true);
      }
    }
  }, []);

  const addQueryParam = (key: string, value: string) => {
    const url = new URL(window.location.href);
    if (!value.length) {
      url.searchParams.get(key) && url.searchParams.delete(key);
    } else {
      url.searchParams.set(key, value);
    }
    window.history.pushState(null, '', url.toString());
  };

  const onFilterChange = (filterNames: string[]) => {
    setShowFilterMobile(false);
    // Warning: setQueryParams will cause reload the hole page, replace it with `addQueryParam` function.
    // do not know if we want to have this thing here to also filter by app... maybe
    const selectedTags = filterNames.length > 0 ? filterNames.join(',').toString() : '';
    addQueryParam('app', appTag ? appTag : '');
    addQueryParam('filter', selectedTags);
    setSelectedTags(selectedTags.length > 0 ? selectedTags : undefined);
  };

  useEffect(() => {
    const fetchData = async () => {
      !isLoading ? setDocuments(hexProjects as IDocument[]) : setDocuments([]);
    };
    fetchData();
  }, [isLoading, hexProjects]);

  // TODO: Need more abstraction in this component... we have technical debt.
  const checkSelectedProject = (projectList: IProject[]) => {
    if (currentlySelectedTag) {
      setHexProjects((prevstate: any) => {
        const combinedArr = [...prevstate, ...projectList];
        const distinctArr = combinedArr.filter((item, i) => combinedArr.findIndex((t) => t.id === item.id) === i);
        let data = [...distinctArr];
        // recent item to be listed at the top
        const recentItem = currentlySelectedTag && distinctArr.find((item) => item.id === currentlySelectedTag);
        if (recentItem) {
          data = distinctArr.filter((item) => item.id !== currentlySelectedTag);
          data = [recentItem, ...data];
        }
        return data;
      });
    } else {
      setHexProjects([...projectList]);
    }
  };

  // TODO: This is such a mess it doesn't work currently
  const getHexProjects = useCallback(async () => {
    try {
      const customHeaders = await getCustomHeaders();
      setIsLoader(true);

      if (xmsContinuation.current === 'Token not found') {
        setIsLoader(false);
        setIsLoading(false);
      } else {
        const page = parseInt(PAGESIZE);
        const queryParams: any =
          selectedTags !== '' && !selectedTags?.includes('all')
            ? { pageSize: page, tags: selectedTags, orderBy: 'createdByDate', orderDirection: 'DESC' }
            : { pageSize: page, orderBy: 'createdByDate', orderDirection: 'DESC' };
        if (accessType !== '' && accessType !== 'all') {
          queryParams['accessType'] = accessType;
        }
        const response: SdcCollectionResponse = await retrieveSdcInstances(BASE_URL, customHeaders ?? null, queryParams, undefined, 'v2', ContainerType.All);
        if (response && response.status === 200) {
          setIsLoader(false);

          let editPermission;
          const projectsToPass = response.data.content.map((project: any) => {
            editPermission = canUserEdit(project, user);

            return {
              ...project,
              thumbnail: project.thumbnailRef && project.thumbnailRef.sas ? project.thumbnailRef.sas : null,
              canDelete: user?.id === project.createdBy,
              canUnfollow: user?.id !== project.createdBy,
              canUserRename: editPermission,
              canChangeCover: editPermission
            };
          });
          setIsLoading(false);
          checkSelectedProject(projectsToPass);
          // setHexProjects((prevstate: any) => {
          //   const combinedArr = [...prevstate, ...projectsToPass];
          //   const distinctArr = combinedArr.filter((item, i) => combinedArr.findIndex((t) => t.id === item.id) === i);
          //   let data = [...distinctArr];
          //   // recent item to be listed at the top
          //   const recentItem = currentlySelectedTag && distinctArr.find((item) => item.id === currentlySelectedTag);
          //   if (recentItem) {
          //     data = distinctArr.filter((item) => item.id !== currentlySelectedTag);
          //     data = [recentItem, ...data];
          //   }
          //   return data;
          // });
          xmsContinuation.current = response.continuationToken || 'Token not found';
          setXmsToken(response.continuationToken || 'Token not found');
          pageSize.current = pageSize.current + 1;
          setHasError(false);
        } else {
          setIsLoader(false);
          setHasError(true);
          Snackbar.error(t('errorRetrievingProjects'), { enableClose: true });
        }
      }
    } catch (err) {
      setIsLoading(false);
      setIsLoader(false);
      setHasError(true);
      Snackbar.error(t('errorRetrievingProjects'), { enableClose: true });
    }
  }, [xmsToken]);

  useLayoutEffect(() => {
    const updateView = () => {
      const containerWidth = projectsContainerRef.current?.offsetWidth || 0;
      const sidePanelWidth = sidePanelRef.current?.offsetWidth || 0;
      const totalWidth = containerWidth + sidePanelWidth;

      // Skip width detection if forceMobileMode is enabled
      if (!forceMobileMode) {
        if (!showListView && totalWidth < 900) {
          setShowListView(true);
          setIsMobile(true);
        } else if (showListView && totalWidth >= 900) {
          setShowListView(false);
          setIsMobile(false);
        }
      } else {
        // Force mobile mode settings
        setShowListView(true);
        setIsMobile(true);
      }

      setContainerHeight(projectsContainerRef.current?.offsetHeight || 0);
    };

    const debounceMe = debounce(updateView, 500);
    window.addEventListener('resize', debounceMe);
    updateView();

    return () => window.removeEventListener('resize', debounceMe);
  }, [showListView, forceMobileMode]);

  const closeCreateProject = useCallback(() => {
    setShowCreateProject(false);
    setShowCreateDemoProject(false);
  }, []);

  const handleMaterialEnrichRedirection = (redirectKey: string, id: string) => {
    if (id !== '') {
      if (dynamicTag) {
        window.location.assign(BASE_APP_PATH + (BASE_PATH + BASE_PATH.slice(-1) === '/' ? '' : '/') + `${redirectKey}/${dynamicTag}/` + id);
      } else {
        window.location.assign(BASE_APP_PATH + (BASE_PATH + BASE_PATH.slice(-1) === '/' ? '' : '/') + `${redirectKey}/` + id);
      }
    } else {
      window.location.assign(BASE_APP_PATH + (BASE_PATH + BASE_PATH.slice(-1) === '/' ? '' : '/') + `materials-enrich/`);
    }
  };
  const createNewProject = useCallback(async (value: IProject) => {
    setShowCreateProject(false);
    setShowSavingDialog(true);

    // TODO: Update to have workflow type in the later one
    const newProject: IProject = { project: value.project, description: value.description, schemaTypeId: 'hxgn:SmartDataContract-1.0.0' };
    try {
      const tagToPass = value.tags;
      const defaultTags = getApplicationTag(appTag ? appTag : '');
      if (tagToPass) {
        newProject.tags = tagToPass;
      } else if (defaultTags) {
        newProject.tags = [defaultTags];
      } else {
        throw new Error('No default document tag found');
      }

      if (newProject.tags && newProject.tags.length === 1 && newProject.tags[0] === MATERIAL_CENTER_APPLICATIONS.materialsenrich.tag) {
        const redirectKey = getRedirectionKey('materialsenrich');
        if (redirectKey) {
          handleMaterialEnrichRedirection(redirectKey, '');
        }
      } else {
        const customHeaders = await getCustomHeaders();
        const response = await createSdcInstance(newProject, BASE_URL, customHeaders ? customHeaders : null);
        if (response) {
          // setShowCreateProject(false);
          response.data['canDelete'] = user?.id === response.data.createdBy;
          response.data['canUnfollow'] = user?.id !== response.data.createdBy;
          setHexProjects((prev: any) => [response.data, ...prev]);
          // TODO: Need to add this to not be hard coded
          Snackbar.success(`${newProject.project} ${t('savedSuccessfully')}`, { enableClose: true });
          setShowSavingDialog(false);
          const newProjectId = response.data['id'];
          handleClickProject(newProjectId, { ...response.data, description: response.data.description || '' } as IProject);
          dispatch(setProjectCount(projectCount + 1));
        } else {
          Snackbar.error(`${newProject.project} : ${t('errorWhileSaving')}`, { enableClose: true });
          setShowSavingDialog(false);
        }
      }
    } catch (err) {
      Snackbar.error(`${newProject.project} : ${t('errorWhileSaving')}`, { enableClose: true });
      setShowSavingDialog(false);
    }
  }, []);

  const api = axios.create({
    headers: {
      'Content-Type': 'application/json'
    },
    paramsSerializer: (params) => {
      return QueryString.stringify(params, { arrayFormat: 'repeat' });
    }
  });

  const createDemoProject = useCallback(async (value: IDocumentInfo) => {
    setShowCreateDemoProject(false);
    setShowSavingDialog(true);
    let projectData;
    if (value.solution === 'CAD Assembly') {
      projectData = CAD_DEMO_PROJECT;
      if (value && value.name && value.name !== '') {
        projectData = { ...CAD_DEMO_PROJECT, project: value.name };
      }
    } else if (value.solution === 'Design and Additive Manufacturing') {
      projectData = DEMO_PROJECT;
      if (value && value.name && value.name !== '') {
        projectData = { ...DEMO_PROJECT, project: value.name };
      }
    }
    const getCustomHeaders = async () => {
      const JWT = await getAccessTokenSilently();
      if (JWT) {
        const customHeaders: RawAxiosRequestHeaders = {
          authorization: `Bearer ${JWT}`
        };
        return customHeaders;
      }
    };

    const customHeaders = await getCustomHeaders();
    try {
      const response = await api.post<any>(BASE_URL + '/sdc/instances/createdemosdc', projectData, {
        headers: {
          ...customHeaders,
          'Content-Type': 'application/json'
        }
      });

      const { status, data } = response;
      if (status === 200) {
        setShowSavingDialog(false);
        const newProjectId = response.data['id'];
        handleClickProject(newProjectId, data);
        dispatch(setProjectCount(projectCount + 1));
      }
    } catch (error) {
      console.error('Failed to Create a demo project', value);
      Snackbar.error(t('failedToCreateDemoProject'), { enableClose: true });
      setShowSavingDialog(false);
    }
  }, []);

  const getProject = (id: string): IProject | undefined => {
    return hexProjects.find((project: IProject) => {
      if (id === project.id) {
        return project;
      }
    });
  };
  const triggerCustomEvent = (details: IProjectEventDetails, eventName = 'nexus:projectSelected') => {
    const projectClicked = new CustomEvent<IProjectEventDetails>(eventName, { detail: { ...details } });
    window.dispatchEvent(projectClicked);

    // Code added here to for additive manufacutring
    const externalCefEventHandler = eventName in window ? (window as { [key: string]: any })[eventName] : undefined;
    if (externalCefEventHandler) {
      externalCefEventHandler(JSON.stringify({ detail: { ...details } }));
    }
  };

  // Helper function to handle redirection
  const handleRedirection = (redirectKey: string, id: string, appTag?: string) => {
    let url;
    if (appTag) {
      url = BASE_APP_PATH + (BASE_PATH + BASE_PATH.slice(-1) === '/' ? '' : '/') + `${redirectKey}/` + id + '?app=' + appTag;
    } else {
      url = BASE_APP_PATH + (BASE_PATH + BASE_PATH.slice(-1) === '/' ? '' : '/') + `${redirectKey}/` + id;
    }
    window.location.assign(url);
  };

  // Helper function to trigger custom event
  const triggerEvent = (project: IProject) => {
    triggerCustomEvent({
      id: project.id as string,
      name: project.project,
      description: project.description
    });
  };

  // Helper function to handle specific project tags
  const handleProjectTag = (project: IProject, tag: string, id: string, appTag?: string) => {
    const redirectKey = getRedirectionKey(tag);
    if (redirectKey) {
      handleRedirection(redirectKey, id, appTag);
    }
  };

  // handles clicking a project to dispatch action to data-uri
  const handleClickProject = (id: string | null | undefined, _newProject?: IProject) => {
    if (!id) {
      console.error(`navigation 'id' is undefined. id:${id}`);
      return;
    }

    const project = _newProject ? _newProject : getProject(id);
    if (!project) {
      console.error(`Project not found for id: ${id}`);
      return;
    }

    if (project.tags?.includes('metroReports')) {
      window.open(METRO_ROUTE + project.id, '_blank');
      return;
    }

    if (project.tags?.includes('asterix')) {
      const redirectKey = getRedirectionKey('materialsenrich');
      if (redirectKey && project.id) {
        handleMaterialEnrichRedirection(redirectKey, project.id);
      }
      return;
    }

    if (project.tags?.includes('smartassembly')) {
      triggerEvent(project);
      handleProjectTag(project, 'smartassembly', id);
      return;
    }

    if (project.tags?.includes('adamsCar')) {
      triggerEvent(project);
      handleProjectTag(project, 'adamsCar', id);
      return;
    }

    if (project.tags?.includes('compute')) {
      triggerEvent(project);
      if (appTag?.toLocaleLowerCase() === 'apex') {
        handleProjectTag(project, 'apexcompute', id);
      } else {
        handleProjectTag(project, 'compute', id);
      }
      return;
    }

    if (project.tags?.includes('plastic') && !appTag) {
      handleProjectTag(project, 'plastic', id);
      return;
    }

    if (project.tags?.includes('productDigitalTwin')) {
      handleProjectTag(project, 'productDigitalTwin', id);
      return;
    }

    triggerEvent(project);
    const redirectionKey = getRedirectionKey(appTag || '');
    if (redirectionKey) {
      handleRedirection(redirectionKey, id, appTag as string);
    } else if (appTag) {
      navigate('/project/' + id + '?app=' + appTag, { replace: false });
    } else {
      navigate('/project/' + id, { replace: false });
    }
  };

  const handleCreateDemoProject = async () => {
    setsolutionOptions(() => {
      const options: OptionType[] = [];
      options[0] = { label: 'CAD Assembly', value: 'CAD Assembly', disabled: false };
      options[1] = { label: t('dfam'), value: 'Design and Additive Manufacturing', disabled: false };
      return [...options];
    });
    setShowCreateDemoProject(true);
  };

  const handleManageExtensions = () => {
    const win: any = window.open(`${BASE_APP_PATH}/platform-landing/manage-extensions/${selectedApp}`, '_blank');
    win.onload = function onload() {
      win.document.title = 'Nexus';
    };
  };

  // This is required in case when we first search and navigate inside to a project and navigate back outside to home page and clear the search text.
  // The searchProjectName state needs to be in sync with session projectName.
  useEffect(() => {
    const searchFor = sessionStorage.getItem('projectName');
    searchFor && setSearchProjectName(searchFor);
  }, []);

  const changeFilterInformation = useCallback((event: React.ChangeEvent<HTMLInputElement>, value: string) => {
    // When do the clear action,the valve will return null, and if the value is them same with before, do nothing.
    const inputValue = !value ? '' : value;
    if (inputValue === projectNameInfo.current) {
      return;
    }
    event.preventDefault();
    setSearchProjectName(inputValue);
    projectNameInfo.current = inputValue;
    sessionStorage.setItem('projectName', String(inputValue));
  }, []);

  const checkSearchValue = useCallback(
    debounce((event: any) => {
      event.preventDefault();
      if (event.target.value.trim() || !searchProjectName) {
        projectNameInfo.current = event.target.value;
        sessionStorage.setItem('projectName', String(projectNameInfo.current));
        setSearchProjectName(event.target.value);
      } else {
        sessionStorage.removeItem('projectName');
        sessionStorage.removeItem('accessType');
      }
    }, 1000),
    []
  );

  const defaultAccessType = 'all';
  const handleShareAccessChange = useCallback((v?: any) => {
    setShowMobileSelect(false);
    sessionStorage.setItem('accessType', String(v.value));
    setHexProjects([]);
    setAccessType(v.value);
  }, []);

  const getAmsCustomHeaders = async () => {
    let amsCustomHeaders: RawAxiosRequestHeaders;
    const JWT = await getAccessTokenSilently({ audience: AMS_AUDIENCE });
    if (JWT) {
      amsCustomHeaders = {
        authorization: `Bearer ${JWT}`
      };
      return amsCustomHeaders;
    }
    return undefined;
  };

  const getSessionStorageValues = () => {
    const searchFor = sessionStorage.getItem('projectName');
    const sessionAccessType = sessionStorage.getItem('accessType') ?? accessType;
    return { searchFor, sessionAccessType };
  };

  const constructSearchQueryParams = (searchFor: string, sessionAccessType: string) => {
    const hasValidTags = selectedTags !== '' && !selectedTags?.includes('all');
    const hasValidTagsSearchAccess = (searchFor || searchProjectName) && selectedTags !== '' && !selectedTags?.includes('all') && accessType !== '' && accessType !== 'all';
    const hasValidAccessType = accessType !== '' && accessType !== 'all';
    const hasValidSearch = searchFor;

    if (hasValidTags || hasValidTagsSearchAccess || hasValidAccessType || hasValidSearch) {
      return {
        pageSize: PAGESIZE,
        tags: selectedTags,
        project: searchFor ? searchFor : '',
        accessType: sessionAccessType ? sessionAccessType : '',
        orderBy: 'createdByDate',
        orderDirection: 'DESC'
      };
    }
    return { pageSize: PAGESIZE, orderBy: 'createdByDate', orderDirection: 'DESC' };
  };

  const handleSearchProjectsResponse = async (response: SdcCollectionResponse) => {
    if (response && response.status === 200) {
      setIsLoader(false);
      let editPermission;
      const projectsToPassList = response.data.content.map((project: any) => {
        editPermission = canUserEdit(project, user);
        return {
          ...project,
          thumbnail: project.thumbnailRef && project.thumbnailRef.sas ? project.thumbnailRef.sas : null,
          canDelete: user?.id === project.createdBy,
          canUnfollow: user?.id !== project.createdBy,
          canUserRename: editPermission,
          canChangeCover: editPermission
        };
      });

      xmsContinuation.current = response.continuationToken || 'Token not found';
      setXmsToken(response.continuationToken || 'Token not found');
      checkSelectedProject(projectsToPassList);
      setIsLoading(false);
      setHasError(false);
    } else {
      setIsLoader(false);
      setHasError(true);
      Snackbar.error(t('errorRetrievingProjects'), { enableClose: true });
      setIsLoading(false);
    }
  };

  const getHexSearchProjects = useCallback(async () => {
    try {
      const { searchFor, sessionAccessType } = getSessionStorageValues();
      if ((searchFor && searchFor === '') || sessionAccessType) {
        setHexProjects([]);
        setIsLoader(true);
        const customHeaders = await getCustomHeaders();
        if (typeof customHeaders !== 'undefined') {
          delete customHeaders['x-ms-continuation'];
        }
        const queryParams = constructSearchQueryParams(searchFor as string, sessionAccessType);
        const response: SdcCollectionResponse = await retrieveSdcInstances(BASE_URL, customHeaders ?? null, queryParams, undefined, 'v2', ContainerType.All);
        if (response && response.status === 200 && accessType === 'all' && !searchProjectName && !selectedTags) {
          const projects = response.data.content;
          dispatch(setProjectCount(projects.length));
        }
        await handleSearchProjectsResponse(response);
      } else {
        getHexProjects();
      }
    } catch (err) {
      setIsLoading(false);
      setIsLoader(false);
      setHasError(true);
      Snackbar.error(t('errorCallingResponse'), { enableClose: true });
    }
  }, [searchProjectName, accessType, selectedTags]);

  useEffect(() => {
    getHexSearchProjects();
  }, [getHexSearchProjects]);

  useEffect(() => {
    projectsCount.current = hexProjects.length;
  }, [hexProjects]);

  // close the dialog when we resize between desktop/mobile
  useEffect(() => {
    setShowSortFilter(false);
    window.sessionStorage.setItem('isMobile', isMobile?.toString());
  }, [isMobile]);

  const setPageCountInSessionStorage = useCallback(() => {
    sessionStorage.removeItem('accessType');
  }, []);

  useEffect(() => {
    window.addEventListener('beforeunload', setPageCountInSessionStorage);
    return () => {
      window.removeEventListener('beforeunload', setPageCountInSessionStorage);
    };
  }, [setPageCountInSessionStorage]);

  const onClickSort: React.MouseEventHandler<HTMLButtonElement> = (e) => {
    setShowSortFilter(true);
  };

  const handleSortMenuClick = (option: any) => {
    const userId = user?.id;
    const sortByCreateTimeFunc = (a: IProject, b: IProject) => (b.createdByDate || '')?.localeCompare(a.createdByDate || '');
    const sortByAZFunc = (a: IProject, b: IProject) => a.project?.localeCompare(b.project);
    const sortByLastOpened = (a: IProject, b: IProject) =>
      (b.mostRecentlyUsed ? (b.mostRecentlyUsed[userId as string] ? b.mostRecentlyUsed[userId as string].lastOpenedDate : '') : '')?.localeCompare(
        a.mostRecentlyUsed ? (a.mostRecentlyUsed[userId as string] ? a.mostRecentlyUsed[userId as string].lastOpenedDate : '') : ''
      );
    let sortFunc;
    switch (option.value) {
      case SortEnum.aToz:
        sortFunc = sortByAZFunc;
        break;
      case SortEnum.createDate:
        sortFunc = sortByCreateTimeFunc;
        break;
      case SortEnum.lastOpenedByMe:
        sortFunc = sortByLastOpened;
        break;
    }
    setDocuments((prev) => prev.sort(sortFunc));
    setShowSortFilter(false);
  };

  const mobileFilter = () => {
    setShowFilterMobile((prev) => !prev);
  };

  const mobileSelect = () => {
    setShowMobileSelect((prev) => !prev);
  };

  // This function is used to handle the success event of the document action like rename, delete, unfollow from ConnectedDocumentView component
  const onSuccessEventCallback = (action: DocumentAction, doc?: IDocument) => {
    switch (action) {
      case 'rename':
        setHexProjects((preHexProjects) => preHexProjects.map((pre) => (pre.id === doc?.id ? { ...pre, project: doc?.project || '' } : pre)));
        break;

      case 'delete':
        if (doc) {
          setHexProjects((preHexProjects) => preHexProjects.filter((p) => p.id !== doc.id));
        }
        dispatch(setProjectCount(projectCount - 1));
        break;
      case 'unfollow':
        if (doc) {
          setHexProjects((preHexProjects) => preHexProjects.filter((p) => p.id !== doc.id));
        }
        break;
    }
  };

  const isDisabled = !isPointSolution(selectedApp) || !sdcPrivileges.includes('create:sdcinstance');

  const getTagsFromDocumentSolution = (documentSolution: string) => {
    switch (documentSolution) {
      case additiveManufacturingProjectTypeInfo.value:
        return additiveManufacturingProjectTypeInfo.tags;

      case smartAssemblyProjectTypeInfo.value:
        return smartAssemblyProjectTypeInfo.tags;

      case plasticProjectTypeInfo.value:
        return plasticProjectTypeInfo.tags;

      case metrologyProjectTypeInfo.value:
        return metrologyProjectTypeInfo.tags;

      case adamsCarProjectTypeInfo.value:
        return adamsCarProjectTypeInfo.tags;

      case computeProjectTypeInfo.value:
        return computeProjectTypeInfo.tags;

      case productDigitalTwinProjectTypeInfo.value:
        return productDigitalTwinProjectTypeInfo.tags;

      default:
        return undefined;
    }
  };

  return (
    <>
      <>
        {isProductionSoftwareApplication(selectedApp) && (
          <Box
            sx={{
              flex: '1 1 0px',
              mx: 'auto',
              mt: 2,
              width: 'calc(100% - 32px)'
            }}
          >
            {platformManageExtensionsLinkButtonVisible && sdcPrivileges.includes('manage:extensions') && (
              <Link onClick={handleManageExtensions} color="primary" sx={{ display: 'flex', justifyContent: 'flex-end' }}>
                {t('manageExtensions')}
              </Link>
            )}
            <Extensions />
          </Box>
        )}
        {!isMobile && (
          <Drawer
            anchor={'left'}
            variant={'permanent'}
            PaperProps={{
              ref: sidePanelRef,
              sx: {
                width: isProductionSoftwareApplication(selectedApp) ? 0 : 256,
                top: { xs: 56, sm: 64 },
                height: { xs: `calc(100vh - 56px)`, sm: `calc(100vh - 64px)` },
                zIndex: (t) => t.zIndex.appBar - 1
              }
            }}
          >
            <List>
              <ListItemButton selected>
                <SearchIcon sx={{ mr: 2 }} />
                <ListItemText primary={t('allDocuments')} primaryTypographyProps={{ sx: { fontSize: 14 } }} />
              </ListItemButton>
            </List>
            <Divider />
            <List
              subheader={
                <ListSubheader component={Typography} variant={'body2'} sx={{ fontWeight: 700, maxHeight: 40, backgroundColor: 'background.paper' }}>
                  {t('documents')}
                </ListSubheader>
              }
              sx={{ overflowY: 'auto' }}
            >
              {hexProjects.length ? (
                hexProjects.map((ele) => (
                  <ListItemButton key={ele.id} onClick={() => handleClickProject(ele.id)}>
                    <ListItemText primary={ele.project} primaryTypographyProps={{ noWrap: true, sx: { fontSize: 14 } }} />
                  </ListItemButton>
                ))
              ) : (
                <Typography sx={{ color: 'grey.600', display: 'flex', paddingLeft: '15px' }} variant="caption">
                  {t('noDocuments')}
                </Typography>
              )}
            </List>
          </Drawer>
        )}
        <Box sx={{ ml: isMobile ? 0 : isProductionSoftwareApplication(selectedApp) ? 0 : '256px', flexGrow: 3 }}>
          <Stack data-testid="projects-container">
            {(!isProductionSoftwareApplication(selectedApp) || isDocument) && !hasError && (
              <Box sx={{ mx: 6, mt: 6 }}>
                <Stack spacing={3} direction="row" sx={{ display: 'flex', alignItems: 'center', height: '100%', marginBottom: '30px' }}>
                  <Typography variant="h5" data-testid={'allDocumentsTitle'}>
                    {t('allDocuments')}
                  </Typography>
                  <Chip label={hexProjects.length} sx={{ fontWeight: '700' }} data-testid="documentCount" />
                  {!showListView && (
                    <Box sx={{ justifyContent: 'flex-end', flexGrow: 1, display: 'flex' }}>
                      {designerButtonVisible && (
                        <Box sx={{ mr: '16px' }}>
                          <Button
                            sx={{ width: '100%' }}
                            variant="contained"
                            color="primary"
                            onClick={() => window.open(DIGITAL_PRODUCT_URL, '_blank')}
                            data-testid="add-desiger-document"
                          >
                            <LaunchIcon />
                            <Typography data-testid="add-desiger-document-title" fontFamily={'Open Sans'} fontSize={'15px'} fontWeight={'400'}>
                              {t('digitalProduct')}
                            </Typography>
                          </Button>
                        </Box>
                      )}
                      {!isDisabled && (
                        <Tooltip title={t('createNewProject')} arrow>
                          <Box>
                            <Button
                              variant="contained"
                              color="primary"
                              onClick={() => {
                                handleCreateDocumentClick();
                              }}
                              data-testid="add-projects-card"
                            >
                              <AddIcon sx={{ color: 'common.white' }} />
                              <Typography sx={{ color: 'common.white' }} data-testid="add-projects-title" fontFamily={'Open Sans'} fontSize={'15px'} fontWeight={'400'}>
                                {t('createNew')}
                              </Typography>
                            </Button>
                          </Box>
                        </Tooltip>
                      )}
                    </Box>
                  )}
                  {/* Mobile Filters */}
                  {(!isProductionSoftwareApplication(selectedApp) || isDocument) && isMobile && (
                    <>
                      <Box sx={{ flex: 1 }} />
                      <Stack direction={'row'} sx={{ color: 'text.secondary' }}>
                        {showProjectFilter.current && (
                          <IconButton ref={filterAnchorRef} size={'small'} color={'inherit'} onClick={mobileFilter} disabled={projectCount === 0}>
                            <FilterAltOutlinedIcon />
                          </IconButton>
                        )}
                        <IconButton size={'small'} color={'inherit'} onClick={mobileSelect} ref={accessTypeAnchorRef} disabled={projectCount === 0}>
                          <PeopleAltOutlinedIcon />
                        </IconButton>
                        <IconButton size={'small'} color={'inherit'} onClick={onClickSort} ref={sortMenuAnchorEl} disabled={projectCount === 0}>
                          <SortIcon />
                        </IconButton>
                      </Stack>
                      {showFilterMobile && (
                        <HexFilterMobile
                          anchorElement={filterAnchorRef.current}
                          onFilterChange={onFilterChange}
                          showFilterMobile={showFilterMobile}
                          groupedData={[...ProjectGroupData]}
                          onDialogClosed={() => setShowFilterMobile(false)}
                          defaultFilterValues={selectedTags ? [...(selectedTags || '').split(',')] : ProjectGroupData[0].data.map((data) => data.name)}
                        />
                      )}
                      {showMobileSelect && (
                        <HexInputSelectMobile
                          {...ShareAccessFilter}
                          showMobileSelect={showMobileSelect}
                          multiple={false}
                          onChange={handleShareAccessChange}
                          defaultValue={sessionStorage.getItem('accessType') ?? defaultAccessType}
                          onDialogClosed={() => setShowMobileSelect(false)}
                          anchorElement={accessTypeAnchorRef.current}
                        />
                      )}
                    </>
                  )}
                </Stack>

                <Stack direction={'row'} justifyContent={'space-between'} flexWrap={'wrap'} alignItems={'center'}>
                  <Box sx={{ flex: '1 1 0px', mr: isMobile ? 0 : 2, maxWidth: isMobile ? '100%' : '550px' }}>
                    <HexSearch
                      showListView={showListView}
                      items={hexProjects}
                      checkSearchValue={checkSearchValue}
                      changeFilterInformation={changeFilterInformation}
                      searchValue={searchProjectName}
                      placeholder={t('searchProject')}
                      myOptions={hexProjects && hexProjects.length > 0 ? hexProjects.slice(0, 5).map((obj: any) => obj.project) : []}
                    ></HexSearch>
                  </Box>

                  {/* Desktop Filters */}
                  {!isMobile && (
                    <Stack direction={'row'} alignItems={'center'}>
                      {showProjectFilter.current && (
                        <HexFilter
                          onFilterChange={onFilterChange}
                          groupedData={ProjectGroupData}
                          defaultFilterValues={selectedTags ? [...selectedTags.split(',')] : ProjectGroupData[0].data.map((data) => data.name)}
                        />
                      )}
                      <HexInputSelect {...ShareAccessFilter} multiple={false} onChange={handleShareAccessChange} defaultValue={defaultAccessType} />
                      <IconButton color={'inherit'} onClick={onClickSort} sx={{ ml: 2 }} ref={sortMenuAnchorEl} disabled={projectCount === 0}>
                        <SortIcon />
                      </IconButton>
                    </Stack>
                  )}
                  {/* Shared Sort Menu used on both mobile and desktop */}
                  <HexInputSelectMobile
                    {...SortFilterDocumentFlagged.current}
                    showMobileSelect={showSortFilter}
                    multiple={false}
                    onChange={handleSortMenuClick}
                    defaultValue={SortEnum.createDate}
                    onDialogClosed={() => setShowSortFilter(false)}
                    anchorElement={sortMenuAnchorEl.current}
                  />
                </Stack>
                {isMobile && showListView && (
                  <Box
                    data-testid="projects-mobile-card-container"
                    className={hexProjects.length > currentPageRecord ? 'hex-projects-tile-container' : 'project-list-container'}
                    ref={projectsRef}
                  >
                    {!isDisabled && (
                      <Box data-testid="card" sx={{ width: '100%' }}>
                        <HexAddProjectButtonMobile
                          createProject={() => {
                            if (appTag && appTag === 'materialsenrich') {
                              const redirectKey = getRedirectionKey('materialsenrich');
                              handleMaterialEnrichRedirection(redirectKey, '');
                            } else {
                              handleCreateDocumentClick();
                            }
                          }}
                          key="add-mobile-card"
                          labels={{ addProject: t('createNew'), addProjectTooltipEnabled: t('createNewProject') }}
                          addEnabled={isPointSolution(selectedApp) && sdcPrivileges.includes('create:sdcinstance')}
                        />
                      </Box>
                    )}
                  </Box>
                )}
              </Box>
            )}
            {(!isProductionSoftwareApplication(selectedApp) || isDocument) && (
              <Box className={`hex-project-list-view ${!showProjectFilter.current && isMobile ? 'no-filter' : ''}`} ref={projectsContainerRef}>
                <Box className="hex-flex-column hex-space-between">
                  {hasError && !isLoading && !showCreateProject ? (
                    <HexErrorPageCard retryCallback={() => navigate(`/project`)}></HexErrorPageCard>
                  ) : !isLoader && !isLoading && hexProjects.length === 0 ? (
                    <HexEmptyProject projectName={searchProjectName} projects={hexProjects} containerHeight={containerHeight} createDemoProject={handleCreateDemoProject} />
                  ) : (
                    <Box sx={{ marginLeft: '24px', marginRight: '15px', marginTop: '24px', marginBottom: '40px' }}>
                      <ConnectedDocumentView
                        userDocuments={documents}
                        isLoading={isLoader}
                        isMobile={isMobile}
                        clickProject={handleClickProject}
                        projectsList={undefined}
                        onSuccessEventCallback={onSuccessEventCallback}
                      />
                    </Box>
                  )}
                  {isLoader && <InformationWithProgressBackDrop open={isLoader} message={t('ProjectLoading')} description={t('ProjectLoadingDescription')} />}
                </Box>
              </Box>
            )}
          </Stack>
        </Box>
      </>

      {!isLoading && (showCreateProject || showCreateDemoProject) && (
        <CreateDocument
          open={showCreateProject || showCreateDemoProject}
          solutionOptions={solutionOptions}
          onCreate={(data: IDocumentInfo) => {
            if (showCreateProject) {
              const description = solutionOptions.find((ele: OptionType) => ele.value === data.solution)?.label?.toLocaleString() ?? '';
              const tags = getTagsFromDocumentSolution(data.solution);

              if (!tags || tags.length === 0) {
                console.error('No tags found for the selected solution');
              }
              const project: IProject = { project: data.name, description, schemaTypeId: '', tags };
              createNewProject(project);
            } else {
              createDemoProject(data);
            }
            closeCreateProject();
          }}
          onCancel={closeCreateProject}
          title={showCreateDemoProject ? t('tryDemoBtn') : undefined}
          initialDocumentName={t('projectName')}
        />
      )}

      {showSavingDialog && <InformationWithProgressBackDrop open={showSavingDialog} message={t('creatingProject')} />}
    </>
  );
}
export default ProjectView;
