import React, { useEffect, useRef, useState } from 'react';
import { EmptyState } from '@nexusui/components';
import { Box, Button, Link, Typography } from '@mui/material';
import ArrowForwardOutlinedIcon from '@mui/icons-material/ArrowForwardOutlined';
import { useTranslation } from 'react-i18next';
import { useFluid } from '@nexusplatform/react';
import { useParams, useSearchParams } from 'react-router-dom';
import { BASE_APP_PATH, BASE_PATH, BASE_URL as URL } from '../../configuration/URL.config';
import { getAppNameFromProjectTag, getRedirectionKey } from '../../utils/DataURI.utils';
import { HexErrorPageCard } from '../../components/ErrorPage/hex-error-page-card';
import { InformationWithProgressBackDrop } from '../../components/BackDrops/InformationWithProgressBackDrop';
import { styles } from './DocumentRedirect.styles';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';

const DocumentRedirect = () => {
  const { t } = useTranslation();
  const [count, setCount] = useState(3);
  const [query] = useSearchParams();
  const appTag = query.get('app');
  const [appName, setAppName] = useState<string | null>(appTag);
  const [targetUrl, setTargetUrl] = useState<string>();
  const sdcInstanceId = useParams().containerId;
  const [containerId, setContainerId] = useState<string>();
  const interval = useRef<any>();

  const useFluidConfig = {
    instanceId: sdcInstanceId,
    setContainerId,
    containerId,
    baseURL: URL
  };

  const { binder, projectDetails, errors, hasErrors } = useFluid(useFluidConfig);

  useEffect(() => {
    if (!projectDetails || !sdcInstanceId) return;
    const projectTag = projectDetails?.tags[0];
    const appTagName = appTag !== undefined && appTag !== null ? appTag : getAppNameFromProjectTag(projectTag);
    const redirectKey = appTagName ? getRedirectionKey(appTagName) : undefined;

    if (redirectKey) {
      let url: string;
      url = BASE_APP_PATH + (BASE_PATH + BASE_PATH.slice(-1) === '/' ? '' : '/') + `${redirectKey}/` + sdcInstanceId;
      if (appTag) {
        url = BASE_APP_PATH + (BASE_PATH + BASE_PATH.slice(-1) === '/' ? '' : '/') + `${redirectKey}/` + sdcInstanceId + `?app=${appTag}`;
      }
      setTargetUrl(url);
    }
    setAppName(appTagName);
  }, [projectDetails, sdcInstanceId]);

  useEffect(() => {
    if (!interval.current && count > 0) {
      interval.current = setInterval(() => {
        if (count === 0) {
          clearInterval(interval.current);
        } else {
          setCount((pre) => pre - 1);
        }
      }, 1000);
    }

    return () => {
      clearInterval(interval.current);
    };
  }, []);

  useEffect(() => {
    if (count === 0 && targetUrl) {
      clearInterval(interval.current);
      window.location.assign(targetUrl);
    }
  }, [targetUrl, count, interval]);

  const isNotFound = hasErrors && errors.some((error) => error.includes('SdcInstanceIdNotFound'));
  if (isNotFound) {
    return (
      <Box sx={{ height: 'calc(100vh - 150px)' }}>
        <HexErrorPageCard errorTitle={t('errorPage.docNotFound')} expandedError={[t('errorPage.notFound'), ...errors]} />;
      </Box>
    );
  } else if (hasErrors) {
    return (
      <Box sx={{ height: 'calc(100vh - 150px)' }}>
        <HexErrorPageCard errorTitle={t('errorPage.loadDocFailed')} expandedError={[t('errorPage.tryLater'), ...errors]} />;
      </Box>
    );
  } else if (!binder) {
    return (
      <Box sx={styles.mainContainer}>
        <InformationWithProgressBackDrop open={true} message={t('loadingProjectData')} description={t('loadingProjectDataDescription')} />
      </Box>
    );
  } else if (!targetUrl) {
    return (
      <Box sx={styles.mainContainer}>
        <HexErrorPageCard
          retryCallback={() => window.location.assign(window.location.href)}
          errorTitle={t('errorPage.redirectionNotAvailable')}
          expandedError={[t('errorPage.redirectionNoHostApplication'), ...errors]}
        />
      </Box>
    );
  } else {
    return (
      <Box sx={{ height: 'calc(100vh - 150px)' }}>
        <EmptyState
          icon={<InfoOutlinedIcon sx={{ color: 'grey.300', fontSize: 72 }} />}
          header={
            <Box sx={{ margin: '10px', fontSize: '30px', textAlign: 'center' }}>
              <Box sx={{ fontSize: 24, margin: '5px' }}>
                <Box>
                  <Typography fontFamily={'Open Sans'} variant="h6" sx={{ mb: 2 }}>
                    {t('redirectionTitle')}
                  </Typography>
                </Box>
                <Box>
                  <Typography fontFamily={'Open Sans'} sx={{ mb: 2 }}>
                    {t('redirectionSubTitle')}
                  </Typography>
                </Box>
                <Box>
                  <Typography fontFamily={'Open Sans'} sx={{ mb: 2 }}>
                    {t('redirectionInfo', { timeout: count, targetApp: appName })}
                  </Typography>
                </Box>
              </Box>
            </Box>
          }
          actions={[
            <Button
              key="redirect-button"
              component={Link}
              variant={'outlined'}
              color={'secondary'}
              endIcon={<ArrowForwardOutlinedIcon />}
              disabled={targetUrl === undefined || appName === undefined}
              onClick={() => {
                targetUrl && window.location.assign(targetUrl);
              }}
            >
              {t('redirectionNow')}
            </Button>
          ]}
        />
      </Box>
    );
  }
};

export default DocumentRedirect;
