const DEV_URL = 'https://dev.nexus.hexagon.com';
export const BASE_URL: string = import.meta.env.VITE_APP_BASE !== 'dev' ? '{{URL_TOKEN}}' : 'https://dev.nexus.hexagon.com/api';
export const SILENT_AUTH = '/?isSilentAuth=true';
export const PLATFORM_LANDING_PAGE_URL: string = import.meta.env.VITE_APP_BASE !== 'dev' ? '{{HREF_URL}}' : 'dev.nexus.hexagon.com';
export const NEXUS_HOMEPAGE = `https://${PLATFORM_LANDING_PAGE_URL}`;

export const BASE_PATH: string = import.meta.env.VITE_APP_BASE !== 'dev' ? '/platform-landing' : '/';

export const METRO_ROUTE: string = import.meta.env.VITE_APP_BASE !== 'dev' ? '{{METRO_URL}}' : 'https://dev.nexus.hexagon.com/metrology-reporting/reportViewer/';

export const PORTAL_ROUTE: string = import.meta.env.VITE_APP_BASE !== 'dev' ? `{{PORTAL_URL}}${SILENT_AUTH}` : `https://dev.nexus.hexagon.com/home${SILENT_AUTH}`;

export const BASE_APP_PATH = import.meta.env.VITE_APP_BASE !== 'dev' ? window.location.origin : 'https://dev.nexus.hexagon.com';

export const AUTHRIZATION_BASE_URL = import.meta.env.VITE_APP_BASE !== 'dev' ? `${window.location.origin}/api/ams` : `${DEV_URL}/api/ams`;

export const USER_PORTAL = import.meta.env.VITE_APP_BASE !== 'dev' ? `${window.location.origin}/home/<USER>/users/` : `${DEV_URL}/home/<USER>/users/`;

export const DIGITAL_PRODUCT_URL = import.meta.env.VITE_APP_BASE !== 'dev' ? `${window.location.origin}/digitalproduct` : `${DEV_URL}/digitalproduct`;

export const PRIVILEGE_LOCATOR = 'https://auth.hexagon.com/user/identifier/privileges';

export const ACCOUNT_MANAGEMENT_URL = import.meta.env.VITE_APP_BASE !== 'dev' ? `${window.location.origin}/account-management` : `${DEV_URL}/account-management`;
export const ACCOUNT_MANAGEMENT_ORGS_URL = `${ACCOUNT_MANAGEMENT_URL}/organization`;
export const AUDIT_URL: string = import.meta.env.VITE_APP_BASE !== 'dev' ? `${window.location.origin}/platform-landing/audit/` : `${DEV_URL}/platform-landing/audit/`;
export const ORG_ID_LOCATOR = 'https://auth.hexagon.com/user/identifier/org_id';
export const INAPP_NOTIF_NAME_URL = 'https://auth.hexagon.com/user/identifier/name';
export const INAPP_NOTIF_GIVENNAME_URL = 'https://auth.hexagon.com/user/identifier/given_name';
export const INAPP_NOTIF_FAMILYNAME_URL = 'https://auth.hexagon.com/user/identifier/family_name';
export const PRODUCT_NAME = 'Platform';
export const EVENT_SOURCE = `//${PLATFORM_LANDING_PAGE_URL}/api/sdc/instances/c0b42d10-ed5d-41ba-94c3-63d281c3bdb3`;
export const ORGANIZATION_NAME = 'https://auth.hexagon.com/user/identifier/org_name';
export const HELP_URL = import.meta.env.VITE_APP_BASE !== 'dev' ? `${window.location.origin}/home/<USER>/home/<USER>
export const CONTACT_SALES_URL =
  import.meta.env.VITE_APP_BASE !== 'dev' ? `${window.location.origin}/home/<USER>/sales${SILENT_AUTH}` : `${DEV_URL}/home/<USER>/sales${SILENT_AUTH}`;
// we donot have any community support in DEV and SIT , hence used PRD url for all environments.
export const JOIN_COMMUNITY_URL = `https://nexus.hexagon.com/community`;
export const NEXUS_APPS_URL =
  import.meta.env.VITE_APP_BASE !== 'dev' ? `${window.location.origin}/home/<USER>/category/software${SILENT_AUTH}` : `${DEV_URL}/home/<USER>/category/software${SILENT_AUTH}`;
export const NEXUS_COMPUTE_URL =
  import.meta.env.VITE_APP_BASE !== 'dev' ? `${window.location.origin}/home/<USER>/nexus-compute${SILENT_AUTH}` : `${DEV_URL}/home/<USER>/nexus-compute${SILENT_AUTH}`;
export const METROLOGY_REPORTING_URL =
  import.meta.env.VITE_APP_BASE !== 'dev'
    ? `${window.location.origin}/home/<USER>/metrology-reporting${SILENT_AUTH}`
    : `${DEV_URL}/home/<USER>/metrology-reporting${SILENT_AUTH}`;
