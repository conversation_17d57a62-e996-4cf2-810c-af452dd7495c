export enum openInType {
  WebBrowser = 'WEB Browser',
  CefBrowser = 'CEF Browser'
}
export type AddBaseExtensionFormDataType = {
  id: string;
  name: string;
  description: string;
  tag: string;
  url: string;
  windowType: string;
};
export type AddAppExtensionFormDataType = {
  id: string;
  extensions: string;
  description: string;
  tag: string;
  url: string;
  windowType: string;
};
export type VariantFormDataType = {
  name: string;
  description: string;
  extensions: string[];
  documentationUrl: string;
  communityForumsUrl: string;
  requestHelpUrl: string;
};

export interface IExtension {
  baseId: string | undefined;
  id: string;
  name: string;
  description: string;
  tag: string;
  url: string;
  enabled: boolean;
  openIn: string;
}
export interface IVariant {
  id: string;
  name: string;
  description: string;
  extensions: { id: string; enabled: boolean }[];
  documentationUrl: string;
  communityForumsUrl: string;
  requestHelpUrl: string;
}
