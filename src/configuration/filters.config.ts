import { IGroupedData } from '../models/Project';

// For February 23, only Metrology Reports and AM will be available. so temporary hide unavailable options in prod.
const isProd = location && location.host === 'nexus.hexagon.com';
export const ProjectGroupData: IGroupedData[] = [
  {
    name: 'Project Type',
    data: isProd
      ? [
          { name: 'all', label: 'allProjectTypes' },
          { name: 'additiveManufacturing', label: 'additiveProjectType' },
          { name: 'metroReports', label: 'metroProjectType' }
        ]
      : [
          { name: 'all', label: 'allProjectTypes' },
          { name: 'additiveManufacturing', label: 'additiveProjectType' },
          { name: 'asterix', label: 'materialEnrichProjectType' },
          { name: 'metroReports', label: 'metroProjectType' },
          { name: 'plastic', label: 'plasticProjectType' },
          { name: 'smartassembly', label: 'smartAssemblyProjectType' },
          { name: 'adamsCar', label: 'adamsCar' },
          { name: 'compute', label: 'compute' },
          { name: 'productDigitalTwin', label: 'productDigitalTwin' }
        ]
  }
];
export const ShareAccessFilter = {
  placeholder: 'Share Access Filter',
  options: [
    { value: 'all', name: 'Owned by anyone' },
    { value: 'owned', name: 'Owned by me' },
    { value: 'shared', name: 'Shared with me' },
    { value: 'sharedWithOrganization', name: 'Shared with organization' }
  ]
};

export const ProjectTypeFilter = {
  placeholder: 'Project Type',
  options: [
    { value: 'all', name: 'All Projects' },
    { value: 'additiveManufacturing', name: 'Additive Manufacturing' },
    { value: 'metroReports', name: 'Metrology Reports' }
  ]
};

export enum SortEnum {
  createDate = 'createDate',
  aToz = 'aToz',
  lastOpenedByMe = 'lastOpenedByMe'
}
export const SortFilter = {
  placeholder: 'Sort Filter',
  options: [
    { value: SortEnum.createDate, name: 'Sort by created date' },
    { value: SortEnum.aToz, name: 'Sort by A to Z' }
  ]
};

export const SortFilterDocument = {
  placeholder: 'Sort Filter',
  options: [
    { value: SortEnum.createDate, name: 'Sort by created date' },
    { value: SortEnum.aToz, name: 'Sort by A to Z' }
  ]
};
