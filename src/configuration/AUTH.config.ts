export interface AuthConfig {
  audience: string;
  clientID: string;
  authority: string;
  issuer: string;
}

const AZURE_BUILD_AUTH: AuthConfig = {
  audience: '{{AUTH_AUDIENCE}}',
  clientID: '{{AUTH_CLIENT}}',
  authority: '{{AUTH_AUTHORITY}}',
  issuer: '{{AUTH_ISSUER}}'
};

const DEV_AUTH: AuthConfig = {
  audience: 'api.nexus.hexagon.com/platformsdcservice',
  clientID: 'hYwm8hcWlTq9QAlvACCyzPXTWkR1WVf4',
  authority: 'https://dev.nexus.hexagon.com/auth',
  issuer: 'https://uat.hxauth.com/auth/realms/nexus-dev'
};

export const AMS_AUDIENCE = 'https://nexus.hexagon.com/authz';
export const EMS_AUDIENCE = 'https://nexus.hexagon.com/ems';
export const SDC_AUDIENCE = 'api.nexus.hexagon.com/platformsdcservice';
// export const BASE_URL: string = 'http://localhost:7071/api';
// export const AUTH_CONFIG: any = import.meta.env.AZURE_BUILD ? AZURE_BUILD_AUTH : DEV_AUTH;
// export const AUTH_CONFIG: any = import.meta.env.REACT_APP_AZURE_BUILD === 'prd' ? AZURE_BUILD_AUTH : DEV_AUTH;

export const AUTH_CONFIG: AuthConfig = import.meta.env.VITE_APP_BASE === 'prod' ? AZURE_BUILD_AUTH : DEV_AUTH;

export const SDC_APPS = ['sf-additive', 'apex', 'cads', 'digimatrp', 'marcmentat'];

export const CLIENT_IDS = ['hYwm8hcWlTq9QAlvACCyzPXTWkR1WVf4', 'WQDRcy3G5D9nXi4DMCfr1y3UFDOPlOES', 'nd13Zgsi7VwhzK5Wwe3YzmrF5LHX9hPV', 'TOvFQe3GbAgE5WVIxpkFqORrdNO7Z9zn'];

export const nameLocator = 'https://auth.hexagon.com/user/identifier/name';
export const emailLocator = 'https://auth.hexagon.com/user/identifier/email';
export const givenNameLocator = 'https://auth.hexagon.com/user/identifier/given_name';
export const familyNameLocator = 'https://auth.hexagon.com/user/identifier/family_name';
export const pictureLocator = 'https://auth.hexagon.com/user/identifier/picture';
