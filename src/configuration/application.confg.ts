export interface AppType {
  tag: string;
  dataURI: string;
}
export interface IApps {
  [key: string]: AppType;
}

export const APPLICATIONS: IApps = Object.freeze({
  designer: {
    tag: 'additiveManufacturing',
    dataURI: 'nexus-designer://'
  },
  cads: {
    tag: 'additiveManufacturing',
    dataURI: 'nexus-cads://'
  },
  apex: {
    tag: 'additiveManufacturing',
    dataURI: 'nexus-apex://'
  },
  'sf-additive': {
    tag: 'additiveManufacturing',
    dataURI: 'nexus-sf-additive://'
  },
  metrologyreporting: {
    tag: 'metrologyreporting',
    dataURI: 'nexus-metrologyreporting://'
  },
  esprit: {
    tag: 'esprit',
    dataURI: 'nexus-esprit://'
  },
  materialcenter: {
    tag: 'materialcenter',
    dataURI: 'nexus-materialcenter://'
  },
  asterix: {
    tag: 'asterix',
    dataURI: 'materials-enrich'
  },
  web: {
    tag: 'web',
    dataURI: 'nexus-web://'
  }
});

export const ADDITIVE_APPLICATIONS: IApps = Object.freeze({
  cads: {
    tag: 'cads',
    dataURI: 'additive/project'
  },
  apex: {
    tag: 'apex',
    dataURI: 'additive/project'
  },
  additive: {
    tag: 'additive',
    dataURI: 'additive/project'
  },
  'sf-additive': {
    tag: 'sf-additive',
    dataURI: 'additive/project'
  }
});

export const PLASTIC_APPLICATIONS: IApps = Object.freeze({
  digimatrp: {
    tag: 'digimatrp',
    dataURI: 'plastics-rp'
  },
  marcmentat: {
    tag: 'marcmentat',
    dataURI: 'plastics-mentat'
  },
  plastic: {
    tag: 'plastic',
    dataURI: 'plastics-gateoptimization/projectDetail'
  }
});

export const MATERIAL_CENTER_APPLICATIONS: IApps = Object.freeze({
  materialsenrich: {
    tag: 'asterix',
    dataURI: 'materials-enrich/project'
  }
});

export const MAGNETO_APPLICATIONS: IApps = Object.freeze({
  smartassembly: {
    tag: 'smartassembly',
    dataURI: 'smartassembly'
  }
});

export const ADAM_CAR_APPLICATION: IApps = Object.freeze({
  adamscar: {
    tag: 'adamsCar',
    dataURI: 'adams'
  }
});

export const COMPUTE_APPLICATIONS: IApps = Object.freeze({
  compute: {
    tag: 'compute',
    dataURI: 'compute/project'
  },
  apexcompute: {
    tag: 'apexcompute',
    dataURI: 'apex/project'
  }
});

export const PRODUCT_DIGITAL_TWIN_APPLICATION: IApps = Object.freeze({
  productdigitaltwin: {
    tag: 'productDigitalTwin',
    dataURI: '3dwhiteboard'
  },

  espritedge: {
    tag: 'espritedge',
    dataURI: '3dwhiteboard'
  },
  designer: {
    tag: 'designer',
    dataURI: '3dwhiteboard'
  }
});

export const PRODUCTION_SOFTWARE_APPLICATIONS: IApps = Object.freeze({
  additive: {
    tag: 'additive',
    dataURI: ''
  },
  worknc: {
    tag: 'worknc',
    dataURI: ''
  },
  espritedge: {
    tag: 'espritedge',
    dataURI: ''
  },
  designer: {
    tag: 'designer',
    dataURI: ''
  },
  radan: {
    tag: 'radan',
    dataURI: ''
  },
  radanquoting: {
    tag: 'radanquoting',
    dataURI: ''
  },
  fabrication: {
    tag: 'fabrication',
    dataURI: ''
  },
  fabrication1: {
    tag: 'fabrication1',
    dataURI: ''
  },
  mouldanddie: {
    tag: 'mouldanddie',
    dataURI: ''
  },
  ncsimul: {
    tag: 'ncsimul',
    dataURI: ''
  },
  mdtc: {
    tag: 'mdtc',
    dataURI: ''
  },
  edgecam: {
    tag: 'edgecam',
    dataURI: ''
  },
  tube: {
    tag: 'tube',
    dataURI: ''
  },
  parts: {
    tag: 'parts',
    dataURI: ''
  },
  programmanager: {
    tag: 'programmanager',
    dataURI: ''
  },
  productionmachining: {
    tag: 'productionmachining',
    dataURI: ''
  },
  visi: {
    tag: 'visi',
    dataURI: ''
  }
});

export const additiveTag = 'additiveManufacturing';
export const plasticTag = 'plastic';
export const matEnrichTag = 'asterix';
export const smartAssemblyTag = 'smartassembly';
export const productDigitalTwin = 'productDigitalTwin';
export const adamsCarTag = 'adamsCar';
export const metrologyReportingTag = 'metrologyreporting';
export const productDigitalTwinTag = 'productDigitalTwin';
export const computeTag = 'compute';
export const PLATFORM_APP_VERSION = '{{PLATFORM_APP_VERSION}}';
export const workncTag = 'worknc';
export const espritTag = 'espritedge';
export const designerTag = 'designer';
export const radanTag = 'radan';
export const radanQuotingTag = 'radanquoting';
