/// <reference types="vite/client" />

interface ImportMetaEnv {
  readonly VITE_APP_BASE: string;
  readonly VITE_APP_FLUID_MODE: string;
  readonly VITE_APP_DEBUG_LOG: string;
  readonly MODE: string;
}

interface ImportMeta {
  readonly env: ImportMetaEnv;
}

// For legacy process.env access
declare global {
  interface Window {
    process: {
      env: {
        NODE_ENV: string;
        VITE_APP_BASE?: string;
        VITE_APP_FLUID_MODE?: string;
        VITE_APP_DEBUG_LOG?: string;
        [key: string]: string | undefined;
      };
    };
    global: Window;
    Buffer: typeof Buffer;
  }
}

export {};
