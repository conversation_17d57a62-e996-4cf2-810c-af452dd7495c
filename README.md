# Getting started

This repository contains an application that can authenticate and load your sdc containers. It allows you to create and view your sdc containers

## Tools

- Node.js >= 16.x
- npm >= 8.1.x

## Installation

```
npm i
```

Installs the required dependencies

## Running the package

```
npm run start
```

Open a browser to http://localhost:3000 to interact with the application

## Running test

To execute the tests please run the following command.

```
npm run coverage:ci
```

this will trigger the unit tests

## Debugging React Lib : @nexusplatform/react-components

In the terminal, run the following command to create a global link for your package:

```
npm install '<GenHexFluidReactLibPath>' --force
```

Replace "<GenHexFluidReactLibPath>" with the absolute path of the `GenHexFluidReactLib` folder on your local machine.

- Check if , linking is done properly : {if you are facing issue while link , try to remove node_module and execute : npm install }

1. node_module\nexusplatform\React-components is pointing to GenHexFluidReactLib folder.
2. In Package.json , @nexusplatform/react-components updated with path i.e. for example :"@nexusplatform/react-components": "file:../../HexFluidReactLib"

- if linking is succussfull , then run command

```
npm run startdev
```

- To enable HotReload :

1. change from "@nexusplatform/react-components" to "@nexusplatform/react-components/src/index"
2. if any error, build GenHexFluidReactLib : npm build GenHexFluidReactLib

## Instructions to configure Sonarqube in your local machine VSCode:

Please follow the instructions in the below confluence page to integrate Sonarqube in your local machine VSCode.

[SonarQube Linting and Benefits](https://hexagon.atlassian.net/wiki/spaces/GEN/pages/44429837592/SonarQube+Linting+and+Benefits)
