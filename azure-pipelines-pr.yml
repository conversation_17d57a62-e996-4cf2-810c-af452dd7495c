trigger:
  - none

variables:
  vmImage: ubuntu-latest
  System.Debug: true
  sonarqubeScan: 'false'

pool:
  vmImage: $(vmImage)

stages:
  #######################################################################################################################
  - template: .azuredevops/templates/stages/versioning.yml
    parameters:
      configFilePath: '.azuredevops/GitVersion.yml'

  #######################################################################################################################
  - template: .azuredevops/templates/stages/build.yml
    parameters:
      sonarqubeScan: false
      dependsOn:
        - Versioning
      # sonarqubeScan: ${{ variables.sonarqubeScan }}
