stages:
  - stage: PreReqs
    jobs:
      - job: CalculateVersion
        steps:
          - task: gitversion/setup@0
            displayName: Install GitVersion
            inputs:
              versionSpec: '5.x'

          - task: gitversion/execute@0
            displayName: Calculate SemVer
            name: Version
            inputs:
              useConfigFile: true
              updateAssemblyInfo: false
              configFilePath: 'GitVersion.yml'

          - script: |
              echo '##vso[build.updatebuildnumber]$(GitVersion.SemVer)'
            displayName: 'Set Build Number to Semantic Version'