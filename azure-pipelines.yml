trigger:
  - release/auth0

variables:
  vmImage: ubuntu-latest
  System.Debug: true
  blackduckScan: true

pool:
  vmImage: $(vmImage)

resources:
  repositories:
    - repository: AutomationTests
      type: git
      name: 'MI-Genesis\Nexus.PlatformTestAutomation'

stages:
  #######################################################################################################################
  - template: .azuredevops/templates/stages/versioning.yml
    parameters:
      configFilePath: '.azuredevops/GitVersion.yml'

  #######################################################################################################################
  - template: .azuredevops/templates/stages/build.yml
    parameters:
      sonarqubeScan: true
      dependsOn:
        - Versioning

  #####################################################################################################################
  - template: .azuredevops/templates/stages/deploy_static_app.yml
    parameters:
      env: dev
      FDID: 1cb07995-bfcf-448b-9715-3d984c15562d
      APP_INSIGHTS_KEY: eb752e17-1dfe-4e46-855e-3e1b0b8deb79
      URL_TOKEN: https://dev.nexus.hexagon.com/api
      AUTH_CLIENT: hYwm8hcWlTq9QAlvACCyzPXTWkR1WVf4
      AUTH_DOMAIN: auth.dev.nexus.hexagon.com
      AUTH_AUDIENCE: api.nexus.hexagon.com/platformsdcservice
      HREF_URL: dev.nexus.hexagon.com
      MAT_URL: https://dev.nexus.hexagon.com/api/materials-connect/
      METRO_URL: https://dev.nexus.hexagon.com/metrology-reporting/reportViewer/
      PORTAL_URL: https://dev.nexus.hexagon.com/home
      DEFAULT_ORGANIZATION_ID: org_JhaawqlN0iGdFSud
      LD_CLIENT_SIDE_ID: 63f62871b4a2cb131979179b
      INTENTO_KEY: BzYZ4LWUhnnWxQIwPSqCLNZCMp5FuIRd
      SHOW_PART_TREE: false
      dependsOn:
        - Build

  #######################################################################################################################
  - template: .azuredevops/templates/stages/deploylog.yml
    parameters:
      dependsOn:
        - Deploy_dev_uks
      environment: dev

  #######################################################################################################################
  - template: .azuredevops/templates/stages/black_duck_analyze_stage.yml
    parameters:
      dependsOn:
        - Build
      blackduckScan: ${{ variables.blackduckScan }}

  #########################################################################################################################
  - template: .azuredevops/templates/stages/integrationtest_testing_e2e.yml
    parameters:
      environment: dev
      applicationName: Platform
      emailTo: <EMAIL>;<EMAIL>;<EMAIL>
      dependsOn:
        - Deploy_dev_uks

  ######################################################################################################################
  - template: .azuredevops/templates/stages/deploy_static_app.yml
    parameters:
      env: qa
      FDID: 5f6addec-7ec9-4531-92a1-203981905713
      APP_INSIGHTS_KEY: ba6350de-1b0f-4401-ad6d-d26be4a54d75
      URL_TOKEN: https://qa.nexus.hexagon.com/api
      AUTH_CLIENT: WQDRcy3G5D9nXi4DMCfr1y3UFDOPlOES
      AUTH_DOMAIN: auth.qa.nexus.hexagon.com
      AUTH_AUDIENCE: api.nexus.hexagon.com/platformsdcservice
      HREF_URL: qa.nexus.hexagon.com
      MAT_URL: https://qa.nexus.hexagon.com/api/materials-connect/
      METRO_URL: https://qa.nexus.hexagon.com/metrology-reporting/reportViewer/
      PORTAL_URL: https://qa.nexus.hexagon.com/home
      DEFAULT_ORGANIZATION_ID: org_X5ShNI7gBMAXzP2Q
      LD_CLIENT_SIDE_ID: 63f62871b4a2cb131979179b
      INTENTO_KEY: BzYZ4LWUhnnWxQIwPSqCLNZCMp5FuIRd
      SHOW_PART_TREE: false
      region: euw
      dependsOn:
        - Deploy_dev_uks
  #######################################################################################################################
  # BELOW CODE IS TO DEPLOY ON USW REGION
  # - template: .azuredevops/templates/stages/deploy_static_app.yml
  #   parameters:
  #     env: qa
  #     FDID: 5f6addec-7ec9-4531-92a1-203981905713
  #     APP_INSIGHTS_KEY: ba6350de-1b0f-4401-ad6d-d26be4a54d75
  #     URL_TOKEN: https://qa.nexus.hexagon.com/api
  #     AUTH_CLIENT: WQDRcy3G5D9nXi4DMCfr1y3UFDOPlOES
  #     AUTH_DOMAIN: auth.qa.nexus.hexagon.com
  #     AUTH_AUDIENCE: api.nexus.hexagon.com/platformsdcservice
  #     HREF_URL: qa.nexus.hexagon.com
  #     MAT_URL: https://qa.nexus.hexagon.com/api/materials-connect/material-detail/reactive/v1/
  #     METRO_URL: https://qa.nexus.hexagon.com/metrology-reporting/reportViewer/
  #     PORTAL_URL: https://qa.nexus.hexagon.com/home
  #     DEFAULT_ORGANIZATION_ID: org_X5ShNI7gBMAXzP2Q
  #     SHOW_PART_TREE: false
  #     location: usw
  #     region: usw
  #     dependsOn:
  #       - IntegrationTest_dev

  ######################################################################################################################
  - template: .azuredevops/templates/stages/deploylog.yml
    parameters:
      dependsOn:
        - Deploy_qa_uks
      environment: qa

  ##########################################################################################################################
  - template: .azuredevops/templates/stages/integrationtest_testing_e2e.yml
    parameters:
      environment: qa
      applicationName: Platform
      emailTo: <EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>
      dependsOn:
        - 'Deploy_qa_uks'

  ######################################################################################################################
  - template: .azuredevops/templates/stages/deploy_static_app.yml
    parameters:
      env: sit
      FDID: 5389e6a8-ab0d-476f-9e0f-4bfb454e879b
      APP_INSIGHTS_KEY: 51cf455d-4cec-447c-ab4a-d07fcabefa15
      URL_TOKEN: https://sit.nexus.hexagon.com/api
      AUTH_CLIENT: nd13Zgsi7VwhzK5Wwe3YzmrF5LHX9hPV
      AUTH_DOMAIN: auth.sit.nexus.hexagon.com
      AUTH_AUDIENCE: api.nexus.hexagon.com/platformsdcservice
      HREF_URL: sit.nexus.hexagon.com
      MAT_URL: https://sit.nexus.hexagon.com/api/materials-connect/
      METRO_URL: https://sit.nexus.hexagon.com/metrology-reporting/reportViewer/
      PORTAL_URL: https://sit.nexus.hexagon.com/home
      DEFAULT_ORGANIZATION_ID: org_wZlsv2sJIGoweAz2
      LD_CLIENT_SIDE_ID: 63f62871b4a2cb131979179c
      INTENTO_KEY: 0qxF5QHLOVzS9g1OtdzWye2yrOn91QV0
      SHOW_PART_TREE: false
      dependsOn:
        - Deploy_dev_uks

  #######################################################################################################################
  - template: .azuredevops/templates/stages/deploylog.yml
    parameters:
      dependsOn:
        - Deploy_sit_uks
      environment: sit
  ##########################################################################################################################
  - template: .azuredevops/templates/stages/integrationtest_testing_e2e.yml
    parameters:
      environment: sit
      applicationName: Platform
      emailTo: <EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>
      dependsOn:
        - 'Deploy_sit_uks'

  #######################################################################################################################
  - template: .azuredevops/templates/stages/deploy_static_app.yml
    parameters:
      env: prd
      FDID: 0bb8609f-02e7-4bc5-bce7-53af951326bb
      APP_INSIGHTS_KEY: 0e7ae10a-dabe-4e25-a1f0-0647f01210bf
      URL_TOKEN: https://nexus.hexagon.com/api
      AUTH_CLIENT: TOvFQe3GbAgE5WVIxpkFqORrdNO7Z9zn
      AUTH_DOMAIN: auth.nexus.hexagon.com
      AUTH_AUDIENCE: api.nexus.hexagon.com/platformsdcservice
      HREF_URL: nexus.hexagon.com
      MAT_URL: https://nexus.hexagon.com/api/materials-connect/
      METRO_URL: https://nexus.hexagon.com/metrology-reporting/reportViewer/
      PORTAL_URL: https://nexus.hexagon.com/home
      DEFAULT_ORGANIZATION_ID: org_CBU2QFCZmVg0tOmJ
      LD_CLIENT_SIDE_ID: 63f62871b4a2cb131979179c
      INTENTO_KEY: 0qxF5QHLOVzS9g1OtdzWye2yrOn91QV0
      SHOW_PART_TREE: false
      region: euw
      dependsOn:
        - Deploy_sit_uks
        - Deploy_qa_uks

  # #######################################################################################################################
  # # BELOW CODE IS TO DEPLOY ON USW REGION
  # # - template: .azuredevops/templates/stages/deploy_static_app.yml
  # #   parameters:
  # #     env: prd
  # #     FDID: 0bb8609f-02e7-4bc5-bce7-53af951326bb
  # #     APP_INSIGHTS_KEY: 0e7ae10a-dabe-4e25-a1f0-0647f01210bf
  # #     URL_TOKEN: https://nexus.hexagon.com/api
  # #     AUTH_CLIENT: TOvFQe3GbAgE5WVIxpkFqORrdNO7Z9zn
  # #     AUTH_DOMAIN: auth.nexus.hexagon.com
  # #     AUTH_AUDIENCE: api.nexus.hexagon.com/platformsdcservice
  # #     HREF_URL: nexus.hexagon.com
  # #     MAT_URL: https://nexus.hexagon.com/api/materials-connect/material-detail/reactive/v1/
  # #     METRO_URL: https://nexus.hexagon.com/metrology-reporting/reportViewer/
  # #     PORTAL_URL: https://nexus.hexagon.com/home
  # #     DEFAULT_ORGANIZATION_ID: org_CBU2QFCZmVg0tOmJ
  # #     SHOW_PART_TREE: false
  # #     location: usw
  # #     region: usw
  # #     dependsOn:
  # #       - IntegrationTest_qa
  # #       - IntegrationTest_sit

  #######################################################################################################################
  - template: .azuredevops/templates/stages/deploylog.yml
    parameters:
      dependsOn:
        - Deploy_prd_uks
      environment: prd

  ##########################################################################################################################
  - template: .azuredevops/templates/stages/integrationtest_testing_e2e.yml
    parameters:
      environment: prd
      applicationName: Platform
      emailTo: <EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>;<EMAIL>
      dependsOn:
        - 'Deploy_prd_uks'
