import { defineConfig, loadEnv } from 'vite';
import react from '@vitejs/plugin-react';
import svgrPlugin from 'vite-plugin-svgr';
import { resolve } from 'path';

// https://vitejs.dev/config/
export default defineConfig(async ({ mode }) => {
  // Load env file based on mode
  const env = loadEnv(mode, process.cwd(), '');

  // Import tsconfigPaths dynamically to avoid ESM issues
  const tsconfigPaths = (await import('vite-tsconfig-paths')).default;

  return {
    plugins: [
      react({
        jsxImportSource: '@emotion/react',
        babel: {
          plugins: ['@emotion/babel-plugin']
        }
      }),
      svgrPlugin({
        svgrOptions: {
          icon: true
        }
      }),
      tsconfigPaths()
    ],
    resolve: {
      alias: {
        '@': resolve(__dirname, 'src'),
        path: 'path-browserify',
        stream: 'stream-browserify',
        buffer: 'buffer',
        util: 'util',
        process: 'process/browser'
      }
    },
    define: {
      'process.env': {
        ...env,
        NODE_ENV: JSON.stringify(mode)
      },
      // Expose import.meta.env in the browser for third-party libraries
      __APP_ENV__: JSON.stringify(env.VITE_APP_BASE || 'dev')
    },
    base: env.VITE_APP_BASE !== 'dev' ? '/platform-landing' : '/',
    server: {
      port: 3000,
      open: true
    },
    build: {
      outDir: 'build',
      sourcemap: env.GENERATE_SOURCEMAP === 'true',
      chunkSizeWarningLimit: 1600,
      commonjsOptions: {
        transformMixedEsModules: true
      },
      rollupOptions: {
        output: {
          manualChunks: {
            vendor: ['react', 'react-dom', 'react-router-dom'],
            nexus: ['@nexusplatform/react', '@nexusplatform/sdc-schema', '@nexusplatform/core'],
            mui: ['@mui/material', '@mui/icons-material', '@emotion/react', '@emotion/styled']
          }
        }
      }
    },
    optimizeDeps: {
      esbuildOptions: {
        define: {
          global: 'globalThis'
        }
      }
    }
  };
});
