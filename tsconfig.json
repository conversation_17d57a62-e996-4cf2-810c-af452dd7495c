{"compilerOptions": {"target": "ESNext", "lib": ["DOM", "DOM.Iterable", "ESNext"], "module": "ESNext", "types": ["vite/client"], "skipLibCheck": true, "moduleResolution": "bundler", "allowImportingTsExtensions": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noImplicitAny": true, "strictNullChecks": true, "noFallthroughCasesInSwitch": true, "jsxImportSource": "@emotion/react", "baseUrl": ".", "paths": {"@/*": ["src/*"]}}, "include": ["src"], "references": [{"path": "./tsconfig.node.json"}]}