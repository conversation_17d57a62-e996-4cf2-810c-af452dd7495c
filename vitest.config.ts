import { defineConfig } from 'vitest/config';
import { resolve } from 'path';
import react from '@vitejs/plugin-react';

export default defineConfig(async () => {
  // Import tsconfigPaths dynamically to handle ESM module
  const tsconfigPaths = (await import('vite-tsconfig-paths')).default;

  return {
    plugins: [
      react({
        jsxImportSource: '@emotion/react',
        babel: {
          plugins: ['@emotion/babel-plugin']
        }
      }),
      tsconfigPaths()
    ],
    resolve: {
      alias: {
        '@': resolve(__dirname, 'src'),
        path: 'path-browserify',
        stream: 'stream-browserify',
        buffer: 'buffer',
        util: 'util',
        process: 'process/browser',
        crypto: 'crypto-browserify',
        // Add moduleNameMapper equivalents here
        '\\.(jpg|ico|jpeg|png|gif|eot|otf|webp|svg|ttf|woff|woff2|mp4|webm|wav|mp3|m4a|aac|oga|gltf)$': resolve(__dirname, '__mocks__/fileMock.js'),
        '\\.(css|styl|less|sass|scss)$': 'identity-obj-proxy',
        '@nexusui/icons/(.*)': resolve(__dirname, '__mocks__/iconMock.js'),
        '@nexusui/branding/(.*)': resolve(__dirname, '__mocks__/iconMock.js')
      }
    },
    test: {
      globals: true,
      environment: 'jsdom',
      setupFiles: ['./vitest.setup.ts'],
      testTimeout: 60000, // timeout for each test
      maxThreads: 1, // Limit to a single thread for stability
      minThreads: 1,
      isolate: false, // Disable isolation to prevent process.listeners issues
      pool: 'vmThreads', // Use vmThreads instead of forks or threads
      poolOptions: {
        vmThreads: {
          memoryLimit: '512mb' // Increase memory limit to avoid tests not being run issues
        }
      },
      // ENHANCED COVERAGE CONFIGURATION for Azure Static Web Apps
      coverage: {
        provider: 'v8',
        enabled: true, // Equivalent to collectCoverage: true
        all: true, // Collect coverage from all source files, not just tested ones
        reportsDirectory: './reports/coverage', // Equivalent to coverageDirectory default is './coverage'
        include: ['src/**/*.{ts,tsx}'],
        exclude: [
          'node_modules/',
          'src/**/*.d.ts',
          'src/components/HexagonLogoComponent/**/*.{ts,tsx}',
          'src/components/HexFlagComponent/**/*.{ts,tsx}',
          'src/components/HexAuth/*.{ts,tsx}',
          'src/reportWebVitals.ts',
          'src/setupTests.ts',
          'src/AppInsights.ts',
          'src/index.tsx',
          'src/pages/PlatformView.tsx',
          'src/configuration/*.ts',
          'src/**/*styles.{ts,tsx}',
          'src/**/*test.{ts,tsx}'
        ],
        reporter: [
          'text',
          'json',
          'html',
          'lcov',
          'cobertura' // Added cobertura for Azure DevOps integration
        ],
        // Equivalent to coverageThreshold
        thresholds: {
          lines: 0, // Match your global threshold setting
          functions: 0,
          branches: 0,
          statements: 0
        },
        // Output coverage summary
        reportOnFailure: true,
        cleanOnRerun: true
      },
      // Test file patterns
      include: ['src/**/*.{test,spec}.{js,jsx,ts,tsx}'],
      exclude: [
        'node_modules',
        'build',
        'e2e' // Equivalent to modulePathIgnorePatterns
      ],
      bail: 0, // Do not stop on first failure
      allowOnly: true, // Allow .only in tests for focused testing
      // Add reporter configuration
      reporters: [
        'default',
        'junit' // Equivalent to jest-junit
      ],
      outputFile: {
        junit: './reports/vitest-junit.xml' // Configure output file for junit reporter
      },
      // Handle transformIgnorePatterns equivalent by leveraging Vite's handling
      deps: {
        // Inline these modules in tests - similar to transformIgnorePatterns
        inline: ['@nexusui', '@nexusplatform', '@sds', 'echarts', 'zrender', 'generate-json-patch', '@fontsource/open-sans']
      }
    },
    define: {
      'process.env': {
        NODE_ENV: JSON.stringify(process.env.NODE_ENV || 'test'),
        VITE_APP_BASE: JSON.stringify(process.env.VITE_APP_BASE || 'dev'),
        VITE_APP_FLUID_MODE: JSON.stringify(process.env.VITE_APP_FLUID_MODE || 'frs'),
        VITE_APP_DEBUG_LOG: JSON.stringify(process.env.VITE_APP_DEBUG_LOG || 'debug')
      }
    }
  };
});
